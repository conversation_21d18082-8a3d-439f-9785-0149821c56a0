import{l as Nn,r as Fe,m as On,i as je}from"./manage.6e729324.js";import{d as pe,X as Xt,H as N,Y as qt,D as G,b as C,V as xe,bI as Yt,bJ as fe,r as Re,O as Jt,aA as Qt,M as D,bK as Zt,e as Q,J as ve,g as ot,bL as tt,ah as $n,ac as Nt,h as se,L as Ne,P as he,E as Tn,aG as Pn,aH as In,bn as Ln,bM as Fn,as as at,aJ as An,aa as Ot,bm as Bn,ax as It,K as Pe,bN as Mn,bO as en,bP as jn,bQ as Rn,bR as Hn,ag as Un,bS as Vn,a5 as Gn,bT as zn,bU as Wn,am as Xn,a6 as qn,a7 as Yn,an as Lt,u as tn,ae as Et,af as ue,ai as Ie,W as qe,a2 as Jn,aj as Qn,bV as Zn,bW as eo,bX as to,c as nt,aF as _e,o as ye,aE as Le,w as oe,a as St,j as Oe,I as no,m as oo,F as nn,B as on,i as Ye,aw as Je,t as ao}from"./index.8746381c.js";import{L as lo}from"./List.ee977be2.js";import{_ as ro}from"./index.9b74c380.js";import{C as so}from"./Card.d6389e0b.js";import"./index.8f4a8fa1.js";import"./TabPane.9792ea88.js";import"./useMergedState.8a9045a6.js";import"./index.4c901be3.js";const an=Symbol("TreeContextKey"),io=pe({compatConfig:{MODE:3},name:"TreeContext",props:{value:{type:Object}},setup(e,n){let{slots:o}=n;return Xt(an,N(()=>e.value)),()=>{var t;return(t=o.default)===null||t===void 0?void 0:t.call(o)}}}),$t=()=>qt(an,N(()=>({}))),ln=Symbol("KeysStateKey"),co=e=>{Xt(ln,e)},rn=()=>qt(ln,{expandedKeys:G([]),selectedKeys:G([]),loadedKeys:G([]),loadingKeys:G([]),checkedKeys:G([]),halfCheckedKeys:G([]),expandedKeysSet:N(()=>new Set),selectedKeysSet:N(()=>new Set),loadedKeysSet:N(()=>new Set),loadingKeysSet:N(()=>new Set),checkedKeysSet:N(()=>new Set),halfCheckedKeysSet:N(()=>new Set),flattenNodes:G([])}),uo=e=>{let{prefixCls:n,level:o,isStart:t,isEnd:a}=e;const l=`${n}-indent-unit`,r=[];for(let d=0;d<o;d+=1)r.push(C("span",{key:d,class:{[l]:!0,[`${l}-start`]:t[d],[`${l}-end`]:a[d]}},null));return C("span",{"aria-hidden":"true",class:`${n}-indent`},[r])};var fo=uo;const sn={eventKey:[String,Number],prefixCls:String,title:xe.any,data:{type:Object,default:void 0},parent:{type:Object,default:void 0},isStart:{type:Array},isEnd:{type:Array},active:{type:Boolean,default:void 0},onMousemove:{type:Function},isLeaf:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},selectable:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},disableCheckbox:{type:Boolean,default:void 0},icon:xe.any,switcherIcon:xe.any,domRef:{type:Function}},vo={prefixCls:{type:String},motion:{type:Object},focusable:{type:Boolean},activeItem:{type:Object},focused:{type:Boolean},tabindex:{type:Number},checkable:{type:Boolean},selectable:{type:Boolean},disabled:{type:Boolean},height:{type:Number},itemHeight:{type:Number},virtual:{type:Boolean},onScroll:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onActiveChange:{type:Function},onContextmenu:{type:Function},onListChangeStart:{type:Function},onListChangeEnd:{type:Function}},dn=()=>({prefixCls:String,focusable:{type:Boolean,default:void 0},activeKey:[Number,String],tabindex:Number,children:xe.any,treeData:{type:Array},fieldNames:{type:Object},showLine:{type:[Boolean,Object],default:void 0},showIcon:{type:Boolean,default:void 0},icon:xe.any,selectable:{type:Boolean,default:void 0},expandAction:[String,Boolean],disabled:{type:Boolean,default:void 0},multiple:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},checkStrictly:{type:Boolean,default:void 0},draggable:{type:[Function,Boolean]},defaultExpandParent:{type:Boolean,default:void 0},autoExpandParent:{type:Boolean,default:void 0},defaultExpandAll:{type:Boolean,default:void 0},defaultExpandedKeys:{type:Array},expandedKeys:{type:Array},defaultCheckedKeys:{type:Array},checkedKeys:{type:[Object,Array]},defaultSelectedKeys:{type:Array},selectedKeys:{type:Array},allowDrop:{type:Function},dropIndicatorRender:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onKeydown:{type:Function},onContextmenu:{type:Function},onClick:{type:Function},onDblclick:{type:Function},onScroll:{type:Function},onExpand:{type:Function},onCheck:{type:Function},onSelect:{type:Function},onLoad:{type:Function},loadData:{type:Function},loadedKeys:{type:Array},onMouseenter:{type:Function},onMouseleave:{type:Function},onRightClick:{type:Function},onDragstart:{type:Function},onDragenter:{type:Function},onDragover:{type:Function},onDragleave:{type:Function},onDragend:{type:Function},onDrop:{type:Function},onActiveChange:{type:Function},filterTreeNode:{type:Function},motion:xe.any,switcherIcon:xe.any,height:Number,itemHeight:Number,virtual:{type:Boolean,default:void 0},direction:{type:String},rootClassName:String,rootStyle:Object});var yo=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(e);a<t.length;a++)n.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(o[t[a]]=e[t[a]]);return o};const Ft="open",At="close",ho="---";var Ct=pe({compatConfig:{MODE:3},name:"ATreeNode",inheritAttrs:!1,props:sn,isTreeNode:1,setup(e,n){let{attrs:o,slots:t,expose:a}=n;Yt(!("slots"in e.data),`treeData slots is deprecated, please use ${Object.keys(e.data.slots||{}).map(u=>"`v-slot:"+u+"` ")}instead`);const l=G(!1),r=$t(),{expandedKeysSet:d,selectedKeysSet:g,loadedKeysSet:y,loadingKeysSet:i,checkedKeysSet:s,halfCheckedKeysSet:h}=rn(),{dragOverNodeKey:c,dropPosition:E,keyEntities:b}=r.value,f=N(()=>Qe(e.eventKey,{expandedKeysSet:d.value,selectedKeysSet:g.value,loadedKeysSet:y.value,loadingKeysSet:i.value,checkedKeysSet:s.value,halfCheckedKeysSet:h.value,dragOverNodeKey:c,dropPosition:E,keyEntities:b})),m=fe(()=>f.value.expanded),I=fe(()=>f.value.selected),P=fe(()=>f.value.checked),T=fe(()=>f.value.loaded),O=fe(()=>f.value.loading),X=fe(()=>f.value.halfChecked),z=fe(()=>f.value.dragOver),A=fe(()=>f.value.dragOverGapTop),K=fe(()=>f.value.dragOverGapBottom),_=fe(()=>f.value.pos),B=G(),J=N(()=>{const{eventKey:u}=e,{keyEntities:k}=r.value,{children:H}=k[u]||{};return!!(H||[]).length}),W=N(()=>{const{isLeaf:u}=e,{loadData:k}=r.value,H=J.value;return u===!1?!1:u||!k&&!H||k&&T.value&&!H}),ae=N(()=>W.value?null:m.value?Ft:At),te=N(()=>{const{disabled:u}=e,{disabled:k}=r.value;return!!(k||u)}),Ee=N(()=>{const{checkable:u}=e,{checkable:k}=r.value;return!k||u===!1?!1:k}),ie=N(()=>{const{selectable:u}=e,{selectable:k}=r.value;return typeof u=="boolean"?u:k}),R=N(()=>{const{data:u,active:k,checkable:H,disableCheckbox:Y,disabled:Z,selectable:ee}=e;return D(D({active:k,checkable:H,disableCheckbox:Y,disabled:Z,selectable:ee},u),{dataRef:u,data:u,isLeaf:W.value,checked:P.value,expanded:m.value,loading:O.value,selected:I.value,halfChecked:X.value})}),ge=ot(),q=N(()=>{const{eventKey:u}=e,{keyEntities:k}=r.value,{parent:H}=k[u]||{};return D(D({},Ze(D({},e,f.value))),{parent:H})}),ne=Re({eventData:q,eventKey:N(()=>e.eventKey),selectHandle:B,pos:_,key:ge.vnode.key});a(ne);const de=u=>{const{onNodeDoubleClick:k}=r.value;k(u,q.value)},be=u=>{if(te.value)return;const{onNodeSelect:k}=r.value;u.preventDefault(),k(u,q.value)},we=u=>{if(te.value)return;const{disableCheckbox:k}=e,{onNodeCheck:H}=r.value;if(!Ee.value||k)return;u.preventDefault();const Y=!P.value;H(u,q.value,Y)},Te=u=>{const{onNodeClick:k}=r.value;k(u,q.value),ie.value?be(u):we(u)},Ae=u=>{const{onNodeMouseEnter:k}=r.value;k(u,q.value)},rt=u=>{const{onNodeMouseLeave:k}=r.value;k(u,q.value)},st=u=>{const{onNodeContextMenu:k}=r.value;k(u,q.value)},it=u=>{const{onNodeDragStart:k}=r.value;u.stopPropagation(),l.value=!0,k(u,ne);try{u.dataTransfer.setData("text/plain","")}catch{}},dt=u=>{const{onNodeDragEnter:k}=r.value;u.preventDefault(),u.stopPropagation(),k(u,ne)},ct=u=>{const{onNodeDragOver:k}=r.value;u.preventDefault(),u.stopPropagation(),k(u,ne)},Ue=u=>{const{onNodeDragLeave:k}=r.value;u.stopPropagation(),k(u,ne)},ut=u=>{const{onNodeDragEnd:k}=r.value;u.stopPropagation(),l.value=!1,k(u,ne)},ft=u=>{const{onNodeDrop:k}=r.value;u.preventDefault(),u.stopPropagation(),l.value=!1,k(u,ne)},Ve=u=>{const{onNodeExpand:k}=r.value;O.value||k(u,q.value)},Ge=()=>{const{data:u}=e,{draggable:k}=r.value;return!!(k&&(!k.nodeDraggable||k.nodeDraggable(u)))},ze=()=>{const{draggable:u,prefixCls:k}=r.value;return u&&(u==null?void 0:u.icon)?C("span",{class:`${k}-draggable-icon`},[u.icon]):null},vt=()=>{var u,k,H;const{switcherIcon:Y=t.switcherIcon||((u=r.value.slots)===null||u===void 0?void 0:u[(H=(k=e.data)===null||k===void 0?void 0:k.slots)===null||H===void 0?void 0:H.switcherIcon])}=e,{switcherIcon:Z}=r.value,ee=Y||Z;return typeof ee=="function"?ee(R.value):ee},We=()=>{const{loadData:u,onNodeLoad:k}=r.value;O.value||u&&m.value&&!W.value&&!J.value&&!T.value&&k(q.value)};Jt(()=>{We()}),Qt(()=>{We()});const yt=()=>{const{prefixCls:u}=r.value,k=vt();if(W.value)return k!==!1?C("span",{class:ve(`${u}-switcher`,`${u}-switcher-noop`)},[k]):null;const H=ve(`${u}-switcher`,`${u}-switcher_${m.value?Ft:At}`);return k!==!1?C("span",{onClick:Ve,class:H},[k]):null},ht=()=>{var u,k;const{disableCheckbox:H}=e,{prefixCls:Y}=r.value,Z=te.value;return Ee.value?C("span",{class:ve(`${Y}-checkbox`,P.value&&`${Y}-checkbox-checked`,!P.value&&X.value&&`${Y}-checkbox-indeterminate`,(Z||H)&&`${Y}-checkbox-disabled`),onClick:we},[(k=(u=r.value).customCheckable)===null||k===void 0?void 0:k.call(u)]):null},Xe=()=>{const{prefixCls:u}=r.value;return C("span",{class:ve(`${u}-iconEle`,`${u}-icon__${ae.value||"docu"}`,O.value&&`${u}-icon_loading`)},null)},De=()=>{const{disabled:u,eventKey:k}=e,{draggable:H,dropLevelOffset:Y,dropPosition:Z,prefixCls:ee,indent:v,dropIndicatorRender:p,dragOverNodeKey:x,direction:w}=r.value;return!u&&H!==!1&&x===k?p({dropPosition:Z,dropLevelOffset:Y,indent:v,prefixCls:ee,direction:w}):null},pt=()=>{var u,k,H,Y,Z,ee;const{icon:v=t.icon,data:p}=e,x=t.title||((u=r.value.slots)===null||u===void 0?void 0:u[(H=(k=e.data)===null||k===void 0?void 0:k.slots)===null||H===void 0?void 0:H.title])||((Y=r.value.slots)===null||Y===void 0?void 0:Y.title)||e.title,{prefixCls:w,showIcon:L,icon:$,loadData:S}=r.value,M=te.value,V=`${w}-node-content-wrapper`;let F;if(L){const le=v||((Z=r.value.slots)===null||Z===void 0?void 0:Z[(ee=p==null?void 0:p.slots)===null||ee===void 0?void 0:ee.icon])||$;F=le?C("span",{class:ve(`${w}-iconEle`,`${w}-icon__customize`)},[typeof le=="function"?le(R.value):le]):Xe()}else S&&O.value&&(F=Xe());let j;typeof x=="function"?j=x(R.value):j=x,j=j===void 0?ho:j;const U=C("span",{class:`${w}-title`},[j]);return C("span",{ref:B,title:typeof x=="string"?x:"",class:ve(`${V}`,`${V}-${ae.value||"normal"}`,!M&&(I.value||l.value)&&`${w}-node-selected`),onMouseenter:Ae,onMouseleave:rt,onContextmenu:st,onClick:Te,onDblclick:de},[F,U,De()])};return()=>{const u=D(D({},e),o),{eventKey:k,isLeaf:H,isStart:Y,isEnd:Z,domRef:ee,active:v,data:p,onMousemove:x,selectable:w}=u,L=yo(u,["eventKey","isLeaf","isStart","isEnd","domRef","active","data","onMousemove","selectable"]),{prefixCls:$,filterTreeNode:S,keyEntities:M,dropContainerKey:V,dropTargetKey:F,draggingNodeKey:j}=r.value,U=te.value,le=Zt(L,{aria:!0,data:!0}),{level:me}=M[k]||{},ce=Z[Z.length-1],re=Ge(),Se=!U&&re,Be=j===k,gt=w!==void 0?{"aria-selected":!!w}:void 0;return C("div",Q(Q({ref:ee,class:ve(o.class,`${$}-treenode`,{[`${$}-treenode-disabled`]:U,[`${$}-treenode-switcher-${m.value?"open":"close"}`]:!H,[`${$}-treenode-checkbox-checked`]:P.value,[`${$}-treenode-checkbox-indeterminate`]:X.value,[`${$}-treenode-selected`]:I.value,[`${$}-treenode-loading`]:O.value,[`${$}-treenode-active`]:v,[`${$}-treenode-leaf-last`]:ce,[`${$}-treenode-draggable`]:Se,dragging:Be,"drop-target":F===k,"drop-container":V===k,"drag-over":!U&&z.value,"drag-over-gap-top":!U&&A.value,"drag-over-gap-bottom":!U&&K.value,"filter-node":S&&S(q.value)}),style:o.style,draggable:Se,"aria-grabbed":Be,onDragstart:Se?it:void 0,onDragenter:re?dt:void 0,onDragover:re?ct:void 0,onDragleave:re?Ue:void 0,onDrop:re?ft:void 0,onDragend:re?ut:void 0,onMousemove:x},gt),le),[C(fo,{prefixCls:$,level:me,isStart:Y,isEnd:Z},null),ze(),yt(),ht(),pt()])}}});globalThis&&globalThis.__rest;function Ke(e,n){if(!e)return[];const o=e.slice(),t=o.indexOf(n);return t>=0&&o.splice(t,1),o}function Ce(e,n){const o=(e||[]).slice();return o.indexOf(n)===-1&&o.push(n),o}function Tt(e){return e.split("-")}function cn(e,n){return`${e}-${n}`}function po(e){return e&&e.type&&e.type.isTreeNode}function go(e,n){const o=[],t=n[e];function a(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach(r=>{let{key:d,children:g}=r;o.push(d),a(g)})}return a(t.children),o}function bo(e){if(e.parent){const n=Tt(e.pos);return Number(n[n.length-1])===e.parent.children.length-1}return!1}function mo(e){const n=Tt(e.pos);return Number(n[n.length-1])===0}function Bt(e,n,o,t,a,l,r,d,g,y){var i;const{clientX:s,clientY:h}=e,{top:c,height:E}=e.target.getBoundingClientRect(),f=((y==="rtl"?-1:1)*(((a==null?void 0:a.x)||0)-s)-12)/t;let m=d[o.eventKey];if(h<c+E/2){const _=r.findIndex(W=>W.key===m.key),B=_<=0?0:_-1,J=r[B].key;m=d[J]}const I=m.key,P=m,T=m.key;let O=0,X=0;if(!g.has(I))for(let _=0;_<f&&bo(m);_+=1)m=m.parent,X+=1;const z=n.eventData,A=m.node;let K=!0;return mo(m)&&m.level===0&&h<c+E/2&&l({dragNode:z,dropNode:A,dropPosition:-1})&&m.key===o.eventKey?O=-1:(P.children||[]).length&&g.has(T)?l({dragNode:z,dropNode:A,dropPosition:0})?O=0:K=!1:X===0?f>-1.5?l({dragNode:z,dropNode:A,dropPosition:1})?O=1:K=!1:l({dragNode:z,dropNode:A,dropPosition:0})?O=0:l({dragNode:z,dropNode:A,dropPosition:1})?O=1:K=!1:l({dragNode:z,dropNode:A,dropPosition:1})?O=1:K=!1,{dropPosition:O,dropLevelOffset:X,dropTargetKey:m.key,dropTargetPos:m.pos,dragOverNodeKey:T,dropContainerKey:O===0?null:((i=m.parent)===null||i===void 0?void 0:i.key)||null,dropAllowed:K}}function Mt(e,n){if(!e)return;const{multiple:o}=n;return o?e.slice():e.length?[e[0]]:e}function bt(e){if(!e)return null;let n;if(Array.isArray(e))n={checkedKeys:e,halfCheckedKeys:void 0};else if(typeof e=="object")n={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return null;return n}function wt(e,n){const o=new Set;function t(a){if(o.has(a))return;const l=n[a];if(!l)return;o.add(a);const{parent:r,node:d}=l;d.disabled||r&&t(r.key)}return(e||[]).forEach(a=>{t(a)}),[...o]}var Ko=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(e);a<t.length;a++)n.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(o[t[a]]=e[t[a]]);return o};function He(e,n){return e!=null?e:n}function lt(e){const{title:n,_title:o,key:t,children:a}=e||{},l=n||"title";return{title:l,_title:o||[l],key:t||"key",children:a||"children"}}function Dt(e){function n(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return tt(o).map(a=>{var l,r,d,g;if(!po(a))return null;const y=a.children||{},i=a.key,s={};for(const[_,B]of Object.entries(a.props))s[$n(_)]=B;const{isLeaf:h,checkable:c,selectable:E,disabled:b,disableCheckbox:f}=s,m={isLeaf:h||h===""||void 0,checkable:c||c===""||void 0,selectable:E||E===""||void 0,disabled:b||b===""||void 0,disableCheckbox:f||f===""||void 0},I=D(D({},s),m),{title:P=(l=y.title)===null||l===void 0?void 0:l.call(y,I),icon:T=(r=y.icon)===null||r===void 0?void 0:r.call(y,I),switcherIcon:O=(d=y.switcherIcon)===null||d===void 0?void 0:d.call(y,I)}=s,X=Ko(s,["title","icon","switcherIcon"]),z=(g=y.default)===null||g===void 0?void 0:g.call(y),A=D(D(D({},X),{title:P,icon:T,switcherIcon:O,key:i,isLeaf:h}),m),K=n(z);return K.length&&(A.children=K),A})}return n(e)}function ko(e,n,o){const{_title:t,key:a,children:l}=lt(o),r=new Set(n===!0?[]:n),d=[];function g(y){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return y.map((s,h)=>{const c=cn(i?i.pos:"0",h),E=He(s[a],c);let b;for(let m=0;m<t.length;m+=1){const I=t[m];if(s[I]!==void 0){b=s[I];break}}const f=D(D({},Nt(s,[...t,a,l])),{title:b,key:E,parent:i,pos:c,children:null,data:s,isStart:[...i?i.isStart:[],h===0],isEnd:[...i?i.isEnd:[],h===y.length-1]});return d.push(f),n===!0||r.has(E)?f.children=g(s[l]||[],f):f.children=[],f})}return g(e),d}function xo(e,n,o){let t={};typeof o=="object"?t=o:t={externalGetKey:o},t=t||{};const{childrenPropName:a,externalGetKey:l,fieldNames:r}=t,{key:d,children:g}=lt(r),y=a||g;let i;l?typeof l=="string"?i=h=>h[l]:typeof l=="function"&&(i=h=>l(h)):i=(h,c)=>He(h[d],c);function s(h,c,E,b){const f=h?h[y]:e,m=h?cn(E.pos,c):"0",I=h?[...b,h]:[];if(h){const P=i(h,m),T={node:h,index:c,pos:m,key:P,parentPos:E.node?E.pos:null,level:E.level+1,nodes:I};n(T)}f&&f.forEach((P,T)=>{s(P,T,{node:h,pos:m,level:E?E.level+1:-1},I)})}s(null)}function un(e){let{initWrapper:n,processEntity:o,onProcessFinished:t,externalGetKey:a,childrenPropName:l,fieldNames:r}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},d=arguments.length>2?arguments[2]:void 0;const g=a||d,y={},i={};let s={posEntities:y,keyEntities:i};return n&&(s=n(s)||s),xo(e,h=>{const{node:c,index:E,pos:b,key:f,parentPos:m,level:I,nodes:P}=h,T={node:c,nodes:P,index:E,key:f,pos:b,level:I},O=He(f,b);y[b]=T,i[O]=T,T.parent=y[m],T.parent&&(T.parent.children=T.parent.children||[],T.parent.children.push(T)),o&&o(T,s)},{externalGetKey:g,childrenPropName:l,fieldNames:r}),t&&t(s),s}function Qe(e,n){let{expandedKeysSet:o,selectedKeysSet:t,loadedKeysSet:a,loadingKeysSet:l,checkedKeysSet:r,halfCheckedKeysSet:d,dragOverNodeKey:g,dropPosition:y,keyEntities:i}=n;const s=i[e];return{eventKey:e,expanded:o.has(e),selected:t.has(e),loaded:a.has(e),loading:l.has(e),checked:r.has(e),halfChecked:d.has(e),pos:String(s?s.pos:""),parent:s.parent,dragOver:g===e&&y===0,dragOverGapTop:g===e&&y===-1,dragOverGapBottom:g===e&&y===1}}function Ze(e){const{data:n,expanded:o,selected:t,checked:a,loaded:l,loading:r,halfChecked:d,dragOver:g,dragOverGapTop:y,dragOverGapBottom:i,pos:s,active:h,eventKey:c}=e,E=D(D({dataRef:n},n),{expanded:o,selected:t,checked:a,loaded:l,loading:r,halfChecked:d,dragOver:g,dragOverGapTop:y,dragOverGapBottom:i,pos:s,active:h,eventKey:c,key:c});return"props"in E||Object.defineProperty(E,"props",{get(){return e}}),E}function fn(e,n){const o=new Set;return e.forEach(t=>{n.has(t)||o.add(t)}),o}function Eo(e){const{disabled:n,disableCheckbox:o,checkable:t}=e||{};return!!(n||o)||t===!1}function So(e,n,o,t){const a=new Set(e),l=new Set;for(let d=0;d<=o;d+=1)(n.get(d)||new Set).forEach(y=>{const{key:i,node:s,children:h=[]}=y;a.has(i)&&!t(s)&&h.filter(c=>!t(c.node)).forEach(c=>{a.add(c.key)})});const r=new Set;for(let d=o;d>=0;d-=1)(n.get(d)||new Set).forEach(y=>{const{parent:i,node:s}=y;if(t(s)||!y.parent||r.has(y.parent.key))return;if(t(y.parent.node)){r.add(i.key);return}let h=!0,c=!1;(i.children||[]).filter(E=>!t(E.node)).forEach(E=>{let{key:b}=E;const f=a.has(b);h&&!f&&(h=!1),!c&&(f||l.has(b))&&(c=!0)}),h&&a.add(i.key),c&&l.add(i.key),r.add(i.key)});return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(fn(l,a))}}function Co(e,n,o,t,a){const l=new Set(e);let r=new Set(n);for(let g=0;g<=t;g+=1)(o.get(g)||new Set).forEach(i=>{const{key:s,node:h,children:c=[]}=i;!l.has(s)&&!r.has(s)&&!a(h)&&c.filter(E=>!a(E.node)).forEach(E=>{l.delete(E.key)})});r=new Set;const d=new Set;for(let g=t;g>=0;g-=1)(o.get(g)||new Set).forEach(i=>{const{parent:s,node:h}=i;if(a(h)||!i.parent||d.has(i.parent.key))return;if(a(i.parent.node)){d.add(s.key);return}let c=!0,E=!1;(s.children||[]).filter(b=>!a(b.node)).forEach(b=>{let{key:f}=b;const m=l.has(f);c&&!m&&(c=!1),!E&&(m||r.has(f))&&(E=!0)}),c||l.delete(s.key),E&&r.add(s.key),d.add(s.key)});return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(fn(r,l))}}function mt(e,n,o,t,a,l){let r;l?r=l:r=Eo;const d=new Set(e.filter(y=>!!o[y]));let g;return n===!0?g=So(d,a,t,r):g=Co(d,n.halfCheckedKeys,a,t,r),g}function wo(e){const n=se(0),o=G();return Ne(()=>{const t=new Map;let a=0;const l=e.value||{};for(const r in l)if(Object.prototype.hasOwnProperty.call(l,r)){const d=l[r],{level:g}=d;let y=t.get(g);y||(y=new Set,t.set(g,y)),y.add(d),a=Math.max(a,g)}n.value=a,o.value=t}),{maxLevel:n,levelEntities:o}}var jt=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(e);a<t.length;a++)n.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(o[t[a]]=e[t[a]]);return o},Do=pe({compatConfig:{MODE:3},name:"MotionTreeNode",inheritAttrs:!1,props:D(D({},sn),{active:Boolean,motion:Object,motionNodes:{type:Array},onMotionStart:Function,onMotionEnd:Function,motionType:String}),setup(e,n){let{attrs:o,slots:t}=n;const a=G(!0),l=$t(),r=G(!1),d=N(()=>e.motion?e.motion:Fn()),g=(y,i)=>{var s,h,c,E;i==="appear"?(h=(s=d.value)===null||s===void 0?void 0:s.onAfterEnter)===null||h===void 0||h.call(s,y):i==="leave"&&((E=(c=d.value)===null||c===void 0?void 0:c.onAfterLeave)===null||E===void 0||E.call(c,y)),r.value||e.onMotionEnd(),r.value=!0};return he(()=>e.motionNodes,()=>{e.motionNodes&&e.motionType==="hide"&&a.value&&at(()=>{a.value=!1})},{immediate:!0,flush:"post"}),Jt(()=>{e.motionNodes&&e.onMotionStart()}),Tn(()=>{e.motionNodes&&g()}),()=>{const{motion:y,motionNodes:i,motionType:s,active:h,eventKey:c}=e,E=jt(e,["motion","motionNodes","motionType","active","eventKey"]);return i?C(Ln,Q(Q({},d.value),{},{appear:s==="show",onAfterAppear:b=>g(b,"appear"),onAfterLeave:b=>g(b,"leave")}),{default:()=>[Pn(C("div",{class:`${l.value.prefixCls}-treenode-motion`},[i.map(b=>{const f=jt(b.data,[]),{title:m,key:I,isStart:P,isEnd:T}=b;return delete f.children,C(Ct,Q(Q({},f),{},{title:m,active:h,data:b.data,key:I,eventKey:I,isStart:P,isEnd:T}),t)})]),[[In,a.value]])]}):C(Ct,Q(Q({class:o.class,style:o.style},E),{},{active:h,eventKey:c}),t)}}});function _o(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];const o=e.length,t=n.length;if(Math.abs(o-t)!==1)return{add:!1,key:null};function a(l,r){const d=new Map;l.forEach(y=>{d.set(y,!0)});const g=r.filter(y=>!d.has(y));return g.length===1?g[0]:null}return o<t?{add:!0,key:a(e,n)}:{add:!1,key:a(n,e)}}function Rt(e,n,o){const t=e.findIndex(r=>r.key===o),a=e[t+1],l=n.findIndex(r=>r.key===o);if(a){const r=n.findIndex(d=>d.key===a.key);return n.slice(l+1,r)}return n.slice(l+1)}var Ht=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(e);a<t.length;a++)n.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(o[t[a]]=e[t[a]]);return o};const Ut={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},No=()=>{},$e=`RC_TREE_MOTION_${Math.random()}`,_t={key:$e},vn={key:$e,level:0,index:0,pos:"0",node:_t,nodes:[_t]},Vt={parent:null,children:[],pos:vn.pos,data:_t,title:null,key:$e,isStart:[],isEnd:[]};function Gt(e,n,o,t){return n===!1||!o?e:e.slice(0,Math.ceil(o/t)+1)}function zt(e){const{key:n,pos:o}=e;return He(n,o)}function Oo(e){let n=String(e.key),o=e;for(;o.parent;)o=o.parent,n=`${o.key} > ${n}`;return n}var $o=pe({compatConfig:{MODE:3},name:"NodeList",inheritAttrs:!1,props:vo,setup(e,n){let{expose:o,attrs:t}=n;const a=se(),l=se(),{expandedKeys:r,flattenNodes:d}=rn();o({scrollTo:b=>{a.value.scrollTo(b)},getIndentWidth:()=>l.value.offsetWidth});const g=G(d.value),y=G([]),i=se(null);function s(){g.value=d.value,y.value=[],i.value=null,e.onListChangeEnd()}const h=$t();he([()=>r.value.slice(),d],(b,f)=>{let[m,I]=b,[P,T]=f;const O=_o(P,m);if(O.key!==null){const{virtual:X,height:z,itemHeight:A}=e;if(O.add){const K=T.findIndex(J=>{let{key:W}=J;return W===O.key}),_=Gt(Rt(T,I,O.key),X,z,A),B=T.slice();B.splice(K+1,0,Vt),g.value=B,y.value=_,i.value="show"}else{const K=I.findIndex(J=>{let{key:W}=J;return W===O.key}),_=Gt(Rt(I,T,O.key),X,z,A),B=I.slice();B.splice(K+1,0,Vt),g.value=B,y.value=_,i.value="hide"}}else T!==I&&(g.value=I)}),he(()=>h.value.dragging,b=>{b||s()});const c=N(()=>e.motion===void 0?g.value:d.value),E=()=>{e.onActiveChange(null)};return()=>{const b=D(D({},e),t),{prefixCls:f,selectable:m,checkable:I,disabled:P,motion:T,height:O,itemHeight:X,virtual:z,focusable:A,activeItem:K,focused:_,tabindex:B,onKeydown:J,onFocus:W,onBlur:ae,onListChangeStart:te,onListChangeEnd:Ee}=b,ie=Ht(b,["prefixCls","selectable","checkable","disabled","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabindex","onKeydown","onFocus","onBlur","onListChangeStart","onListChangeEnd"]);return C(An,null,[_&&K&&C("span",{style:Ut,"aria-live":"assertive"},[Oo(K)]),C("div",null,[C("input",{style:Ut,disabled:A===!1||P,tabindex:A!==!1?B:null,onKeydown:J,onFocus:W,onBlur:ae,value:"",onChange:No,"aria-label":"for screen reader"},null)]),C("div",{class:`${f}-treenode`,"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden"}},[C("div",{class:`${f}-indent`},[C("div",{ref:l,class:`${f}-indent-unit`},null)])]),C(lo,Q(Q({},Nt(ie,["onActiveChange"])),{},{data:c.value,itemKey:zt,height:O,fullHeight:!1,virtual:z,itemHeight:X,prefixCls:`${f}-list`,ref:a,onVisibleChange:(R,ge)=>{const q=new Set(R);ge.filter(de=>!q.has(de)).some(de=>zt(de)===$e)&&s()}}),{default:R=>{const{pos:ge}=R,q=Ht(R.data,[]),{title:ne,key:de,isStart:be,isEnd:we}=R,Te=He(de,ge);return delete q.key,delete q.children,C(Do,Q(Q({},q),{},{eventKey:Te,title:ne,active:!!K&&de===K.key,data:R.data,isStart:be,isEnd:we,motion:T,motionNodes:de===$e?y.value:null,motionType:i.value,onMotionStart:te,onMotionEnd:s,onMousemove:E}),null)}})])}}});function To(e){let{dropPosition:n,dropLevelOffset:o,indent:t}=e;const a={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:`${2}px`};switch(n){case-1:a.top=0,a.left=`${-o*t}px`;break;case 1:a.bottom=0,a.left=`${-o*t}px`;break;case 0:a.bottom=0,a.left=`${t}`;break}return C("div",{style:a},null)}const Po=10;var Io=pe({compatConfig:{MODE:3},name:"Tree",inheritAttrs:!1,props:Ot(dn(),{prefixCls:"vc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,expandAction:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:To,allowDrop:()=>!0}),setup(e,n){let{attrs:o,slots:t,expose:a}=n;const l=G(!1);let r={};const d=G(),g=G([]),y=G([]),i=G([]),s=G([]),h=G([]),c=G([]),E={},b=Re({draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null}),f=G([]);he([()=>e.treeData,()=>e.children],()=>{f.value=e.treeData!==void 0?e.treeData.slice():Dt(It(e.children))},{immediate:!0,deep:!0});const m=G({}),I=G(!1),P=G(null),T=G(!1),O=N(()=>lt(e.fieldNames)),X=G();let z=null,A=null,K=null;const _=N(()=>({expandedKeysSet:B.value,selectedKeysSet:J.value,loadedKeysSet:W.value,loadingKeysSet:ae.value,checkedKeysSet:te.value,halfCheckedKeysSet:Ee.value,dragOverNodeKey:b.dragOverNodeKey,dropPosition:b.dropPosition,keyEntities:m.value})),B=N(()=>new Set(c.value)),J=N(()=>new Set(g.value)),W=N(()=>new Set(s.value)),ae=N(()=>new Set(h.value)),te=N(()=>new Set(y.value)),Ee=N(()=>new Set(i.value));Ne(()=>{if(f.value){const v=un(f.value,{fieldNames:O.value});m.value=D({[$e]:vn},v.keyEntities)}});let ie=!1;he([()=>e.expandedKeys,()=>e.autoExpandParent,m],(v,p)=>{let[x,w]=v,[L,$]=p,S=c.value;if(e.expandedKeys!==void 0||ie&&w!==$)S=e.autoExpandParent||!ie&&e.defaultExpandParent?wt(e.expandedKeys,m.value):e.expandedKeys;else if(!ie&&e.defaultExpandAll){const M=D({},m.value);delete M[$e],S=Object.keys(M).map(V=>M[V].key)}else!ie&&e.defaultExpandedKeys&&(S=e.autoExpandParent||e.defaultExpandParent?wt(e.defaultExpandedKeys,m.value):e.defaultExpandedKeys);S&&(c.value=S),ie=!0},{immediate:!0});const R=G([]);Ne(()=>{R.value=ko(f.value,c.value,O.value)}),Ne(()=>{e.selectable&&(e.selectedKeys!==void 0?g.value=Mt(e.selectedKeys,e):!ie&&e.defaultSelectedKeys&&(g.value=Mt(e.defaultSelectedKeys,e)))});const{maxLevel:ge,levelEntities:q}=wo(m);Ne(()=>{if(e.checkable){let v;if(e.checkedKeys!==void 0?v=bt(e.checkedKeys)||{}:!ie&&e.defaultCheckedKeys?v=bt(e.defaultCheckedKeys)||{}:f.value&&(v=bt(e.checkedKeys)||{checkedKeys:y.value,halfCheckedKeys:i.value}),v){let{checkedKeys:p=[],halfCheckedKeys:x=[]}=v;e.checkStrictly||({checkedKeys:p,halfCheckedKeys:x}=mt(p,!0,m.value,ge.value,q.value)),y.value=p,i.value=x}}}),Ne(()=>{e.loadedKeys&&(s.value=e.loadedKeys)});const ne=()=>{D(b,{dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})},de=v=>{X.value.scrollTo(v)};he(()=>e.activeKey,()=>{e.activeKey!==void 0&&(P.value=e.activeKey)},{immediate:!0}),he(P,v=>{at(()=>{v!==null&&de({key:v})})},{immediate:!0,flush:"post"});const be=v=>{e.expandedKeys===void 0&&(c.value=v)},we=()=>{b.draggingNodeKey!==null&&D(b,{draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),z=null,K=null},Te=(v,p)=>{const{onDragend:x}=e;b.dragOverNodeKey=null,we(),x==null||x({event:v,node:p.eventData}),A=null},Ae=v=>{Te(v,null),window.removeEventListener("dragend",Ae)},rt=(v,p)=>{const{onDragstart:x}=e,{eventKey:w,eventData:L}=p;A=p,z={x:v.clientX,y:v.clientY};const $=Ke(c.value,w);b.draggingNodeKey=w,b.dragChildrenKeys=go(w,m.value),d.value=X.value.getIndentWidth(),be($),window.addEventListener("dragend",Ae),x&&x({event:v,node:L})},st=(v,p)=>{const{onDragenter:x,onExpand:w,allowDrop:L,direction:$}=e,{pos:S,eventKey:M}=p;if(K!==M&&(K=M),!A){ne();return}const{dropPosition:V,dropLevelOffset:F,dropTargetKey:j,dropContainerKey:U,dropTargetPos:le,dropAllowed:me,dragOverNodeKey:ce}=Bt(v,A,p,d.value,z,L,R.value,m.value,B.value,$);if(b.dragChildrenKeys.indexOf(j)!==-1||!me){ne();return}if(r||(r={}),Object.keys(r).forEach(re=>{clearTimeout(r[re])}),A.eventKey!==p.eventKey&&(r[S]=window.setTimeout(()=>{if(b.draggingNodeKey===null)return;let re=c.value.slice();const Se=m.value[p.eventKey];Se&&(Se.children||[]).length&&(re=Ce(c.value,p.eventKey)),be(re),w&&w(re,{node:p.eventData,expanded:!0,nativeEvent:v})},800)),A.eventKey===j&&F===0){ne();return}D(b,{dragOverNodeKey:ce,dropPosition:V,dropLevelOffset:F,dropTargetKey:j,dropContainerKey:U,dropTargetPos:le,dropAllowed:me}),x&&x({event:v,node:p.eventData,expandedKeys:c.value})},it=(v,p)=>{const{onDragover:x,allowDrop:w,direction:L}=e;if(!A)return;const{dropPosition:$,dropLevelOffset:S,dropTargetKey:M,dropContainerKey:V,dropAllowed:F,dropTargetPos:j,dragOverNodeKey:U}=Bt(v,A,p,d.value,z,w,R.value,m.value,B.value,L);b.dragChildrenKeys.indexOf(M)!==-1||!F||(A.eventKey===M&&S===0?b.dropPosition===null&&b.dropLevelOffset===null&&b.dropTargetKey===null&&b.dropContainerKey===null&&b.dropTargetPos===null&&b.dropAllowed===!1&&b.dragOverNodeKey===null||ne():$===b.dropPosition&&S===b.dropLevelOffset&&M===b.dropTargetKey&&V===b.dropContainerKey&&j===b.dropTargetPos&&F===b.dropAllowed&&U===b.dragOverNodeKey||D(b,{dropPosition:$,dropLevelOffset:S,dropTargetKey:M,dropContainerKey:V,dropTargetPos:j,dropAllowed:F,dragOverNodeKey:U}),x&&x({event:v,node:p.eventData}))},dt=(v,p)=>{K===p.eventKey&&!v.currentTarget.contains(v.relatedTarget)&&(ne(),K=null);const{onDragleave:x}=e;x&&x({event:v,node:p.eventData})},ct=function(v,p){let x=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var w;const{dragChildrenKeys:L,dropPosition:$,dropTargetKey:S,dropTargetPos:M,dropAllowed:V}=b;if(!V)return;const{onDrop:F}=e;if(b.dragOverNodeKey=null,we(),S===null)return;const j=D(D({},Qe(S,It(_.value))),{active:((w=H.value)===null||w===void 0?void 0:w.key)===S,data:m.value[S].node});L.indexOf(S);const U=Tt(M),le={event:v,node:Ze(j),dragNode:A?A.eventData:null,dragNodesKeys:[A.eventKey].concat(L),dropToGap:$!==0,dropPosition:$+Number(U[U.length-1])};x||F==null||F(le),A=null},Ue=(v,p)=>{const{expanded:x,key:w}=p,L=R.value.filter(S=>S.key===w)[0],$=Ze(D(D({},Qe(w,_.value)),{data:L.data}));be(x?Ke(c.value,w):Ce(c.value,w)),De(v,$)},ut=(v,p)=>{const{onClick:x,expandAction:w}=e;w==="click"&&Ue(v,p),x&&x(v,p)},ft=(v,p)=>{const{onDblclick:x,expandAction:w}=e;(w==="doubleclick"||w==="dblclick")&&Ue(v,p),x&&x(v,p)},Ve=(v,p)=>{let x=g.value;const{onSelect:w,multiple:L}=e,{selected:$}=p,S=p[O.value.key],M=!$;M?L?x=Ce(x,S):x=[S]:x=Ke(x,S);const V=m.value,F=x.map(j=>{const U=V[j];return U?U.node:null}).filter(j=>j);e.selectedKeys===void 0&&(g.value=x),w&&w(x,{event:"select",selected:M,node:p,selectedNodes:F,nativeEvent:v})},Ge=(v,p,x)=>{const{checkStrictly:w,onCheck:L}=e,$=p[O.value.key];let S;const M={event:"check",node:p,checked:x,nativeEvent:v},V=m.value;if(w){const F=x?Ce(y.value,$):Ke(y.value,$),j=Ke(i.value,$);S={checked:F,halfChecked:j},M.checkedNodes=F.map(U=>V[U]).filter(U=>U).map(U=>U.node),e.checkedKeys===void 0&&(y.value=F)}else{let{checkedKeys:F,halfCheckedKeys:j}=mt([...y.value,$],!0,V,ge.value,q.value);if(!x){const U=new Set(F);U.delete($),{checkedKeys:F,halfCheckedKeys:j}=mt(Array.from(U),{checked:!1,halfCheckedKeys:j},V,ge.value,q.value)}S=F,M.checkedNodes=[],M.checkedNodesPositions=[],M.halfCheckedKeys=j,F.forEach(U=>{const le=V[U];if(!le)return;const{node:me,pos:ce}=le;M.checkedNodes.push(me),M.checkedNodesPositions.push({node:me,pos:ce})}),e.checkedKeys===void 0&&(y.value=F,i.value=j)}L&&L(S,M)},ze=v=>{const p=v[O.value.key],x=new Promise((w,L)=>{const{loadData:$,onLoad:S}=e;if(!$||W.value.has(p)||ae.value.has(p))return null;$(v).then(()=>{const V=Ce(s.value,p),F=Ke(h.value,p);S&&S(V,{event:"load",node:v}),e.loadedKeys===void 0&&(s.value=V),h.value=F,w()}).catch(V=>{const F=Ke(h.value,p);if(h.value=F,E[p]=(E[p]||0)+1,E[p]>=Po){const j=Ce(s.value,p);e.loadedKeys===void 0&&(s.value=j),w()}L(V)}),h.value=Ce(h.value,p)});return x.catch(()=>{}),x},vt=(v,p)=>{const{onMouseenter:x}=e;x&&x({event:v,node:p})},We=(v,p)=>{const{onMouseleave:x}=e;x&&x({event:v,node:p})},yt=(v,p)=>{const{onRightClick:x}=e;x&&(v.preventDefault(),x({event:v,node:p}))},ht=v=>{const{onFocus:p}=e;I.value=!0,p&&p(v)},Xe=v=>{const{onBlur:p}=e;I.value=!1,k(null),p&&p(v)},De=(v,p)=>{let x=c.value;const{onExpand:w,loadData:L}=e,{expanded:$}=p,S=p[O.value.key];if(T.value)return;x.indexOf(S);const M=!$;if(M?x=Ce(x,S):x=Ke(x,S),be(x),w&&w(x,{node:p,expanded:M,nativeEvent:v}),M&&L){const V=ze(p);V&&V.then(()=>{}).catch(F=>{const j=Ke(c.value,S);be(j),Promise.reject(F)})}},pt=()=>{T.value=!0},u=()=>{setTimeout(()=>{T.value=!1})},k=v=>{const{onActiveChange:p}=e;P.value!==v&&(e.activeKey!==void 0&&(P.value=v),v!==null&&de({key:v}),p&&p(v))},H=N(()=>P.value===null?null:R.value.find(v=>{let{key:p}=v;return p===P.value})||null),Y=v=>{let p=R.value.findIndex(w=>{let{key:L}=w;return L===P.value});p===-1&&v<0&&(p=R.value.length),p=(p+v+R.value.length)%R.value.length;const x=R.value[p];if(x){const{key:w}=x;k(w)}else k(null)},Z=N(()=>Ze(D(D({},Qe(P.value,_.value)),{data:H.value.data,active:!0}))),ee=v=>{const{onKeydown:p,checkable:x,selectable:w}=e;switch(v.which){case Pe.UP:{Y(-1),v.preventDefault();break}case Pe.DOWN:{Y(1),v.preventDefault();break}}const L=H.value;if(L&&L.data){const $=L.data.isLeaf===!1||!!(L.data.children||[]).length,S=Z.value;switch(v.which){case Pe.LEFT:{$&&B.value.has(P.value)?De({},S):L.parent&&k(L.parent.key),v.preventDefault();break}case Pe.RIGHT:{$&&!B.value.has(P.value)?De({},S):L.children&&L.children.length&&k(L.children[0].key),v.preventDefault();break}case Pe.ENTER:case Pe.SPACE:{x&&!S.disabled&&S.checkable!==!1&&!S.disableCheckbox?Ge({},S,!te.value.has(P.value)):!x&&w&&!S.disabled&&S.selectable!==!1&&Ve({},S);break}}}p&&p(v)};return a({onNodeExpand:De,scrollTo:de,onKeydown:ee,selectedKeys:N(()=>g.value),checkedKeys:N(()=>y.value),halfCheckedKeys:N(()=>i.value),loadedKeys:N(()=>s.value),loadingKeys:N(()=>h.value),expandedKeys:N(()=>c.value)}),Bn(()=>{window.removeEventListener("dragend",Ae),l.value=!0}),co({expandedKeys:c,selectedKeys:g,loadedKeys:s,loadingKeys:h,checkedKeys:y,halfCheckedKeys:i,expandedKeysSet:B,selectedKeysSet:J,loadedKeysSet:W,loadingKeysSet:ae,checkedKeysSet:te,halfCheckedKeysSet:Ee,flattenNodes:R}),()=>{const{draggingNodeKey:v,dropLevelOffset:p,dropContainerKey:x,dropTargetKey:w,dropPosition:L,dragOverNodeKey:$}=b,{prefixCls:S,showLine:M,focusable:V,tabindex:F=0,selectable:j,showIcon:U,icon:le=t.icon,switcherIcon:me,draggable:ce,checkable:re,checkStrictly:Se,disabled:Be,motion:gt,loadData:hn,filterTreeNode:pn,height:gn,itemHeight:bn,virtual:mn,dropIndicatorRender:Kn,onContextmenu:kn,onScroll:xn,direction:En,rootClassName:Sn,rootStyle:Cn}=e,{class:wn,style:Dn}=o,_n=Zt(D(D({},e),o),{aria:!0,data:!0});let Me;return ce?typeof ce=="object"?Me=ce:typeof ce=="function"?Me={nodeDraggable:ce}:Me={}:Me=!1,C(io,{value:{prefixCls:S,selectable:j,showIcon:U,icon:le,switcherIcon:me,draggable:Me,draggingNodeKey:v,checkable:re,customCheckable:t.checkable,checkStrictly:Se,disabled:Be,keyEntities:m.value,dropLevelOffset:p,dropContainerKey:x,dropTargetKey:w,dropPosition:L,dragOverNodeKey:$,dragging:v!==null,indent:d.value,direction:En,dropIndicatorRender:Kn,loadData:hn,filterTreeNode:pn,onNodeClick:ut,onNodeDoubleClick:ft,onNodeExpand:De,onNodeSelect:Ve,onNodeCheck:Ge,onNodeLoad:ze,onNodeMouseEnter:vt,onNodeMouseLeave:We,onNodeContextMenu:yt,onNodeDragStart:rt,onNodeDragEnter:st,onNodeDragOver:it,onNodeDragLeave:dt,onNodeDragEnd:Te,onNodeDrop:ct,slots:t}},{default:()=>[C("div",{role:"tree",class:ve(S,wn,Sn,{[`${S}-show-line`]:M,[`${S}-focused`]:I.value,[`${S}-active-focused`]:P.value!==null}),style:Cn},[C($o,Q({ref:X,prefixCls:S,style:Dn,disabled:Be,selectable:j,checkable:!!re,motion:gt,height:gn,itemHeight:bn,virtual:mn,focusable:V,focused:I.value,tabindex:F,activeItem:H.value,onFocus:ht,onBlur:Xe,onKeydown:ee,onActiveChange:k,onListChangeStart:pt,onListChangeEnd:u,onContextmenu:kn,onScroll:xn},_n),null)])]})}}});function Lo(e,n,o,t,a){const{isLeaf:l,expanded:r,loading:d}=o;let g=n;if(d)return C(Mn,{class:`${e}-switcher-loading-icon`},null);let y;a&&typeof a=="object"&&(y=a.showLeafIcon);let i=null;const s=`${e}-switcher-icon`;return l?a?y&&t?t(o):(typeof a=="object"&&!y?i=C("span",{class:`${e}-switcher-leaf-line`},null):i=C(en,{class:`${e}-switcher-line-icon`},null),i):null:(i=C(jn,{class:s},null),a&&(i=r?C(Rn,{class:`${e}-switcher-line-icon`},null):C(Hn,{class:`${e}-switcher-line-icon`},null)),typeof n=="function"?g=n(D(D({},o),{defaultIcon:i,switcherCls:s})):Un(g)&&(g=Vn(g,{class:s})),g||i)}const Wt=4;function Fo(e){const{dropPosition:n,dropLevelOffset:o,prefixCls:t,indent:a,direction:l="ltr"}=e,r=l==="ltr"?"left":"right",d=l==="ltr"?"right":"left",g={[r]:`${-o*a+Wt}px`,[d]:0};switch(n){case-1:g.top=`${-3}px`;break;case 1:g.bottom=`${-3}px`;break;default:g.bottom=`${-3}px`,g[r]=`${a+Wt}px`;break}return C("div",{style:g,class:`${t}-drop-indicator`},null)}const Ao=new Xn("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Bo=(e,n)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${n.motionDurationSlow}`}}}),Mo=(e,n)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:n.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${n.lineWidthBold}px solid ${n.colorPrimary}`,borderRadius:"50%",content:'""'}}}),jo=(e,n)=>{const{treeCls:o,treeNodeCls:t,treeNodePadding:a,treeTitleHeight:l}=n,r=(l-n.fontSizeLG)/2,d=n.paddingXS;return{[o]:D(D({},Yn(n)),{background:n.colorBgContainer,borderRadius:n.borderRadius,transition:`background-color ${n.motionDurationSlow}`,[`&${o}-rtl`]:{[`${o}-switcher`]:{"&_close":{[`${o}-switcher-icon`]:{svg:{transform:"rotate(90deg)"}}}}},[`&-focused:not(:hover):not(${o}-active-focused)`]:D({},Lt(n)),[`${o}-list-holder-inner`]:{alignItems:"flex-start"},[`&${o}-block-node`]:{[`${o}-list-holder-inner`]:{alignItems:"stretch",[`${o}-node-content-wrapper`]:{flex:"auto"},[`${t}.dragging`]:{position:"relative","&:after":{position:"absolute",top:0,insetInlineEnd:0,bottom:a,insetInlineStart:0,border:`1px solid ${n.colorPrimary}`,opacity:0,animationName:Ao,animationDuration:n.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none"}}}},[`${t}`]:{display:"flex",alignItems:"flex-start",padding:`0 0 ${a}px 0`,outline:"none","&-rtl":{direction:"rtl"},"&-disabled":{[`${o}-node-content-wrapper`]:{color:n.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}}},[`&-active ${o}-node-content-wrapper`]:D({},Lt(n)),[`&:not(${t}-disabled).filter-node ${o}-title`]:{color:"inherit",fontWeight:500},"&-draggable":{[`${o}-draggable-icon`]:{width:l,lineHeight:`${l}px`,textAlign:"center",visibility:"visible",opacity:.2,transition:`opacity ${n.motionDurationSlow}`,[`${t}:hover &`]:{opacity:.45}},[`&${t}-disabled`]:{[`${o}-draggable-icon`]:{visibility:"hidden"}}}},[`${o}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:l}},[`${o}-draggable-icon`]:{visibility:"hidden"},[`${o}-switcher`]:D(D({},Bo(e,n)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,margin:0,lineHeight:`${l}px`,textAlign:"center",cursor:"pointer",userSelect:"none","&-noop":{cursor:"default"},"&_close":{[`${o}-switcher-icon`]:{svg:{transform:"rotate(-90deg)"}}},"&-loading-icon":{color:n.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:l/2,bottom:-a,marginInlineStart:-1,borderInlineEnd:`1px solid ${n.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:l/2*.8,height:l/2,borderBottom:`1px solid ${n.colorBorder}`,content:'""'}}}),[`${o}-checkbox`]:{top:"initial",marginInlineEnd:d,marginBlockStart:r},[`${o}-node-content-wrapper, ${o}-checkbox + span`]:{position:"relative",zIndex:"auto",minHeight:l,margin:0,padding:`0 ${n.paddingXS/2}px`,color:"inherit",lineHeight:`${l}px`,background:"transparent",borderRadius:n.borderRadius,cursor:"pointer",transition:`all ${n.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,"&:hover":{backgroundColor:n.controlItemBgHover},[`&${o}-node-selected`]:{backgroundColor:n.controlItemBgActive},[`${o}-iconEle`]:{display:"inline-block",width:l,height:l,lineHeight:`${l}px`,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}},[`${o}-unselectable ${o}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${o}-node-content-wrapper`]:D({lineHeight:`${l}px`,userSelect:"none"},Mo(e,n)),[`${t}.drop-container`]:{"> [draggable]":{boxShadow:`0 0 0 2px ${n.colorPrimary}`}},"&-show-line":{[`${o}-indent`]:{"&-unit":{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:l/2,bottom:-a,borderInlineEnd:`1px solid ${n.colorBorder}`,content:'""'},"&-end":{"&:before":{display:"none"}}}},[`${o}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${t}-leaf-last`]:{[`${o}-switcher`]:{"&-leaf-line":{"&:before":{top:"auto !important",bottom:"auto !important",height:`${l/2}px !important`}}}}})}},Ro=e=>{const{treeCls:n,treeNodeCls:o,treeNodePadding:t}=e;return{[`${n}${n}-directory`]:{[o]:{position:"relative","&:before":{position:"absolute",top:0,insetInlineEnd:0,bottom:t,insetInlineStart:0,transition:`background-color ${e.motionDurationMid}`,content:'""',pointerEvents:"none"},"&:hover":{"&:before":{background:e.controlItemBgHover}},"> *":{zIndex:1},[`${n}-switcher`]:{transition:`color ${e.motionDurationMid}`},[`${n}-node-content-wrapper`]:{borderRadius:0,userSelect:"none","&:hover":{background:"transparent"},[`&${n}-node-selected`]:{color:e.colorTextLightSolid,background:"transparent"}},"&-selected":{[`
            &:hover::before,
            &::before
          `]:{background:e.colorPrimary},[`${n}-switcher`]:{color:e.colorTextLightSolid},[`${n}-node-content-wrapper`]:{color:e.colorTextLightSolid,background:"transparent"}}}}}},Ho=(e,n)=>{const o=`.${e}`,t=`${o}-treenode`,a=n.paddingXS/2,l=n.controlHeightSM,r=qn(n,{treeCls:o,treeNodeCls:t,treeNodePadding:a,treeTitleHeight:l});return[jo(e,r),Ro(r)]};var Uo=Gn("Tree",(e,n)=>{let{prefixCls:o}=n;return[{[e.componentCls]:zn(`${o}-checkbox`,e)},Ho(o,e),Wn(e)]});const yn=()=>{const e=dn();return D(D({},e),{showLine:Et([Boolean,Object]),multiple:ue(),autoExpandParent:ue(),checkStrictly:ue(),checkable:ue(),disabled:ue(),defaultExpandAll:ue(),defaultExpandParent:ue(),defaultExpandedKeys:Ie(),expandedKeys:Ie(),checkedKeys:Et([Array,Object]),defaultCheckedKeys:Ie(),selectedKeys:Ie(),defaultSelectedKeys:Ie(),selectable:ue(),loadedKeys:Ie(),draggable:ue(),showIcon:ue(),icon:qe(),switcherIcon:xe.any,prefixCls:String,replaceFields:Jn(),blockNode:ue(),openAnimation:xe.any,onDoubleclick:e.onDblclick,"onUpdate:selectedKeys":qe(),"onUpdate:checkedKeys":qe(),"onUpdate:expandedKeys":qe()})};var et=pe({compatConfig:{MODE:3},name:"ATree",inheritAttrs:!1,props:Ot(yn(),{checkable:!1,selectable:!0,showIcon:!1,blockNode:!1}),slots:Object,setup(e,n){let{attrs:o,expose:t,emit:a,slots:l}=n;Yt(!(e.treeData===void 0&&l.default));const{prefixCls:r,direction:d,virtual:g}=tn("tree",e),[y,i]=Uo(r),s=se();t({treeRef:s,onNodeExpand:function(){var f;(f=s.value)===null||f===void 0||f.onNodeExpand(...arguments)},scrollTo:f=>{var m;(m=s.value)===null||m===void 0||m.scrollTo(f)},selectedKeys:N(()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.selectedKeys}),checkedKeys:N(()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.checkedKeys}),halfCheckedKeys:N(()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.halfCheckedKeys}),loadedKeys:N(()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.loadedKeys}),loadingKeys:N(()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.loadingKeys}),expandedKeys:N(()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.expandedKeys})}),Ne(()=>{Qn(e.replaceFields===void 0,"Tree","`replaceFields` is deprecated, please use fieldNames instead")});const c=(f,m)=>{a("update:checkedKeys",f),a("check",f,m)},E=(f,m)=>{a("update:expandedKeys",f),a("expand",f,m)},b=(f,m)=>{a("update:selectedKeys",f),a("select",f,m)};return()=>{const{showIcon:f,showLine:m,switcherIcon:I=l.switcherIcon,icon:P=l.icon,blockNode:T,checkable:O,selectable:X,fieldNames:z=e.replaceFields,motion:A=e.openAnimation,itemHeight:K=28,onDoubleclick:_,onDblclick:B}=e,J=D(D(D({},o),Nt(e,["onUpdate:checkedKeys","onUpdate:expandedKeys","onUpdate:selectedKeys","onDoubleclick"])),{showLine:Boolean(m),dropIndicatorRender:Fo,fieldNames:z,icon:P,itemHeight:K}),W=l.default?tt(l.default()):void 0;return y(C(Io,Q(Q({},J),{},{virtual:g.value,motion:A,ref:s,prefixCls:r.value,class:ve({[`${r.value}-icon-hide`]:!f,[`${r.value}-block-node`]:T,[`${r.value}-unselectable`]:!X,[`${r.value}-rtl`]:d.value==="rtl"},o.class,i.value),direction:d.value,checkable:O,selectable:X,switcherIcon:ae=>Lo(r.value,I,ae,l.leafIcon,m),onCheck:c,onExpand:E,onSelect:b,onDblclick:B||_,children:W}),D(D({},l),{checkable:()=>C("span",{class:`${r.value}-checkbox-inner`},null)})))}}}),ke;(function(e){e[e.None=0]="None",e[e.Start=1]="Start",e[e.End=2]="End"})(ke||(ke={}));function Pt(e,n,o){function t(a){const l=a[n.key],r=a[n.children];o(l,a)!==!1&&Pt(r||[],n,o)}e.forEach(t)}function Vo(e){let{treeData:n,expandedKeys:o,startKey:t,endKey:a,fieldNames:l={title:"title",key:"key",children:"children"}}=e;const r=[];let d=ke.None;if(t&&t===a)return[t];if(!t||!a)return[];function g(y){return y===t||y===a}return Pt(n,l,y=>{if(d===ke.End)return!1;if(g(y)){if(r.push(y),d===ke.None)d=ke.Start;else if(d===ke.Start)return d=ke.End,!1}else d===ke.Start&&r.push(y);return o.includes(y)}),r}function Kt(e,n,o){const t=[...n],a=[];return Pt(e,o,(l,r)=>{const d=t.indexOf(l);return d!==-1&&(a.push(r),t.splice(d,1)),!!t.length}),a}var Go=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(e);a<t.length;a++)n.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(o[t[a]]=e[t[a]]);return o};const zo=()=>D(D({},yn()),{expandAction:Et([Boolean,String])});function Wo(e){const{isLeaf:n,expanded:o}=e;return n?C(en,null,null):o?C(eo,null,null):C(to,null,null)}var kt=pe({compatConfig:{MODE:3},name:"ADirectoryTree",inheritAttrs:!1,props:Ot(zo(),{showIcon:!0,expandAction:"click"}),slots:Object,setup(e,n){let{attrs:o,slots:t,emit:a,expose:l}=n;var r;const d=se(e.treeData||Dt(tt((r=t.default)===null||r===void 0?void 0:r.call(t))));he(()=>e.treeData,()=>{d.value=e.treeData}),Qt(()=>{at(()=>{var K;e.treeData===void 0&&t.default&&(d.value=Dt(tt((K=t.default)===null||K===void 0?void 0:K.call(t))))})});const g=se(),y=se(),i=N(()=>lt(e.fieldNames)),s=se();l({scrollTo:K=>{var _;(_=s.value)===null||_===void 0||_.scrollTo(K)},selectedKeys:N(()=>{var K;return(K=s.value)===null||K===void 0?void 0:K.selectedKeys}),checkedKeys:N(()=>{var K;return(K=s.value)===null||K===void 0?void 0:K.checkedKeys}),halfCheckedKeys:N(()=>{var K;return(K=s.value)===null||K===void 0?void 0:K.halfCheckedKeys}),loadedKeys:N(()=>{var K;return(K=s.value)===null||K===void 0?void 0:K.loadedKeys}),loadingKeys:N(()=>{var K;return(K=s.value)===null||K===void 0?void 0:K.loadingKeys}),expandedKeys:N(()=>{var K;return(K=s.value)===null||K===void 0?void 0:K.expandedKeys})});const c=()=>{const{keyEntities:K}=un(d.value,{fieldNames:i.value});let _;return e.defaultExpandAll?_=Object.keys(K):e.defaultExpandParent?_=wt(e.expandedKeys||e.defaultExpandedKeys||[],K):_=e.expandedKeys||e.defaultExpandedKeys,_},E=se(e.selectedKeys||e.defaultSelectedKeys||[]),b=se(c());he(()=>e.selectedKeys,()=>{e.selectedKeys!==void 0&&(E.value=e.selectedKeys)},{immediate:!0}),he(()=>e.expandedKeys,()=>{e.expandedKeys!==void 0&&(b.value=e.expandedKeys)},{immediate:!0});const m=Zn((K,_)=>{const{isLeaf:B}=_;B||K.shiftKey||K.metaKey||K.ctrlKey||s.value.onNodeExpand(K,_)},200,{leading:!0}),I=(K,_)=>{e.expandedKeys===void 0&&(b.value=K),a("update:expandedKeys",K),a("expand",K,_)},P=(K,_)=>{const{expandAction:B}=e;B==="click"&&m(K,_),a("click",K,_)},T=(K,_)=>{const{expandAction:B}=e;(B==="dblclick"||B==="doubleclick")&&m(K,_),a("doubleclick",K,_),a("dblclick",K,_)},O=(K,_)=>{const{multiple:B}=e,{node:J,nativeEvent:W}=_,ae=J[i.value.key],te=D(D({},_),{selected:!0}),Ee=(W==null?void 0:W.ctrlKey)||(W==null?void 0:W.metaKey),ie=W==null?void 0:W.shiftKey;let R;B&&Ee?(R=K,g.value=ae,y.value=R,te.selectedNodes=Kt(d.value,R,i.value)):B&&ie?(R=Array.from(new Set([...y.value||[],...Vo({treeData:d.value,expandedKeys:b.value,startKey:ae,endKey:g.value,fieldNames:i.value})])),te.selectedNodes=Kt(d.value,R,i.value)):(R=[ae],g.value=ae,y.value=R,te.selectedNodes=Kt(d.value,R,i.value)),a("update:selectedKeys",R),a("select",R,te),e.selectedKeys===void 0&&(E.value=R)},X=(K,_)=>{a("update:checkedKeys",K),a("check",K,_)},{prefixCls:z,direction:A}=tn("tree",e);return()=>{const K=ve(`${z.value}-directory`,{[`${z.value}-directory-rtl`]:A.value==="rtl"},o.class),{icon:_=t.icon,blockNode:B=!0}=e,J=Go(e,["icon","blockNode"]);return C(et,Q(Q(Q({},o),{},{icon:_||Wo,ref:s,blockNode:B},J),{},{prefixCls:z.value,class:K,expandedKeys:b.value,selectedKeys:E.value,onSelect:O,onClick:P,onDblclick:T,onExpand:I,onCheck:X}),t)}}});const xt=Ct;var Xo=D(et,{DirectoryTree:kt,TreeNode:xt,install:e=>(e.component(et.name,et),e.component(xt.name,xt),e.component(kt.name,kt),e)});const qo={style:{"padding-bottom":"50px"}},Yo={key:0},Jo=pe({__name:"RoleDist",setup(e,{expose:n}){const{$infoBox:o,$access:t,$hasAgentEnt:a}=ot().appContext.config.globalProperties,l=Re({hasEnt:t("ENT_UR_ROLE_DIST"),recordId:null,treeData:[],replaceFields:{key:"entId",title:"entName"},checkedKeys:[],allEntList:{}});function r(i){if(!l.hasEnt)return!1;l.checkedKeys=[],l.treeData=[],l.allEntList={},l.recordId=i,Nn("MGR").then(s=>{l.treeData=s,g(s,h=>{l.allEntList[h.entId]={pid:h.pid,children:h.children||[]}}),Fe.list(On,{roleId:i||"NONE",pageSize:-1}).then(h=>{const c=[];h.records.map(E=>{l.allEntList[E.entId]&&l.allEntList[E.entId].children.length<=0&&c.push(E.entId)}),l.checkedKeys=c})})}function d(){if(!l.hasEnt)return!1;const i=[];return l.checkedKeys.map(s=>{const h=[];y(s,h),h.map(c=>{i.indexOf(c)<0&&i.push(c)})}),i}function g(i,s){for(let h=0;h<i.length;h++){const c=i[h];c.children&&c.children.length>0&&g(c.children,s),s(c)}}function y(i,s){l.allEntList[i]&&i!=="ROOT"&&(s.push(i),y(l.allEntList[i].pid,s))}return n({initTree:r,getSelectedEntIdList:d}),(i,s)=>{const h=Xo;return ye(),nt("div",qo,[l.hasEnt?(ye(),nt("p",Yo,"\u8BF7\u9009\u62E9\u6743\u9650\uFF1A")):_e("",!0),C(h,{"tree-data":l.treeData,fieldNames:l.replaceFields,checkedKeys:l.checkedKeys,"onUpdate:checkedKeys":s[0]||(s[0]=c=>l.checkedKeys=c),checkable:!0},null,8,["tree-data","fieldNames","checkedKeys"])])}}}),Qo={class:"drawer-btn-center"},Zo=pe({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>{}}},setup(e,{expose:n}){const{$infoBox:o,$access:t}=ot().appContext.config.globalProperties,a=e,l=Re({confirmLoading:!1,isAdd:!0,isShow:!1,saveObject:{},recordId:null,rules:{roleName:[{required:!0,message:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0",trigger:"blur"}]}}),r=se(),d=se();function g(i){l.isAdd=!i,l.saveObject={},l.confirmLoading=!1,r.value&&r.value.resetFields(),l.isAdd||(l.recordId=i,Fe.getById(je,i).then(s=>{l.saveObject=s})),l.isShow=!0,at(()=>{d.value.initTree(i)})}function y(){r.value.validate().then(i=>{if(i){l.confirmLoading=!0;const s=d.value.getSelectedEntIdList();l.saveObject.entIdListStr=s?JSON.stringify(s):"",l.isAdd?Fe.add(je,l.saveObject).then(h=>{o.message.success("\u65B0\u589E\u6210\u529F"),l.isShow=!1,a.callbackFunc()}).catch(h=>{l.confirmLoading=!1}):Fe.updateById(je,l.recordId,l.saveObject).then(h=>{o.message.success("\u4FEE\u6539\u6210\u529F"),l.isShow=!1,a.callbackFunc()}).catch(h=>{l.confirmLoading=!1})}})}return n({show:g}),(i,s)=>{const h=no,c=oo,E=nn,b=on,f=ro;return ye(),Le(f,{open:l.isShow,"onUpdate:open":s[2]||(s[2]=m=>l.isShow=m),title:l.isAdd?"\u65B0\u589E\u89D2\u8272":"\u4FEE\u6539\u89D2\u8272",width:"600",maskClosable:!1,onClose:s[3]||(s[3]=m=>l.isShow=!1)},{default:oe(()=>[C(E,{ref_key:"infoFormModel",ref:r,model:l.saveObject,"label-col":{span:4},rules:l.rules},{default:oe(()=>[C(c,{label:"\u89D2\u8272\u540D\u79F0\uFF1A",name:"roleName"},{default:oe(()=>[C(h,{value:l.saveObject.roleName,"onUpdate:value":s[0]||(s[0]=m=>l.saveObject.roleName=m)},null,8,["value"])]),_:1})]),_:1},8,["model","rules"]),C(Jo,{ref_key:"roleDistRef",ref:d},null,512),St("div",Qo,[C(b,{style:{marginRight:"8px"},onClick:s[1]||(s[1]=m=>l.isShow=!1)},{default:oe(()=>s[4]||(s[4]=[Oe("\u53D6\u6D88")])),_:1,__:[4]}),C(b,{type:"primary",onClick:y,loading:l.confirmLoading},{default:oe(()=>s[5]||(s[5]=[Oe("\u4FDD\u5B58")])),_:1,__:[5]},8,["loading"])])]),_:1},8,["open","title"])}}}),ea={key:0,class:"table-page-search-wrapper"},ta={class:"table-layer"},na={class:"table-page-search-submitButtons"},oa={key:0},va=pe({__name:"RolePage",setup(e){const{$infoBox:n,$access:o}=ot().appContext.config.globalProperties,a=Re({tableColumns:[{key:"roleId",title:"\u89D2\u8272ID",sorter:!0,fixed:"left",scopedSlots:{customRender:"roleIdSlot"}},{key:"roleName",title:"\u89D2\u8272\u540D\u79F0",dataIndex:"roleName",sorter:!0},{key:"op",title:"\u64CD\u4F5C",width:"200px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],searchData:{},btnLoading:!1}),l=se(),r=se();function d(h){return Fe.list(je,h)}function g(){a.btnLoading=!0,l.value.refTable(!0)}function y(){r.value.show()}function i(h){r.value.show(h)}function s(h){n.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>Fe.delById(je,h).then(c=>{n.message.success("\u5220\u9664\u6210\u529F\uFF01"),l.value.refTable(!1)}))}return(h,c)=>{const E=Ye("cloudpay-text-up"),b=on,f=nn,m=Ye("cloudpayTableColumns"),I=Ye("cloudpayTable"),P=so,T=Ye("page-header-wrapper");return ye(),Le(T,null,{default:oe(()=>[C(P,null,{default:oe(()=>[Je(o)("ENT_UR_ROLE_SEARCH")?(ye(),nt("div",ea,[C(f,{layout:"inline",class:"table-head-ground"},{default:oe(()=>[St("div",ta,[C(E,{placeholder:"\u89D2\u8272ID",value:a.searchData.roleId,"onUpdate:value":c[0]||(c[0]=O=>a.searchData.roleId=O)},null,8,["value"]),C(E,{placeholder:"\u89D2\u8272\u540D\u79F0",value:a.searchData.roleName,"onUpdate:value":c[1]||(c[1]=O=>a.searchData.roleName=O)},null,8,["value"]),St("span",na,[C(b,{type:"primary",onClick:g,loading:a.btnLoading},{default:oe(()=>c[4]||(c[4]=[Oe(" \u67E5\u8BE2 ")])),_:1,__:[4]},8,["loading"]),C(b,{style:{"margin-left":"8px"},onClick:c[2]||(c[2]=()=>a.searchData={})},{default:oe(()=>c[5]||(c[5]=[Oe(" \u91CD\u7F6E ")])),_:1,__:[5]})])])]),_:1})])):_e("",!0),C(I,{ref_key:"infoTable",ref:l,initData:!0,reqTableDataFunc:d,tableColumns:a.tableColumns,searchData:a.searchData,onBtnLoadClose:c[3]||(c[3]=O=>a.btnLoading=!1),rowKey:"roleName"},{opRow:oe(()=>[Je(o)("ENT_UR_ROLE_ADD")?(ye(),Le(b,{key:0,type:"primary",onClick:y,class:"mg-b-30"},{default:oe(()=>c[6]||(c[6]=[Oe(" \u65B0\u5EFA ")])),_:1,__:[6]})):_e("",!0)]),bodyCell:oe(({column:O,record:X})=>[O.key==="roleId"?(ye(),nt("b",oa,ao(X.roleId),1)):_e("",!0),O.key==="op"?(ye(),Le(m,{key:1},{default:oe(()=>[Je(o)("ENT_UR_ROLE_EDIT")?(ye(),Le(b,{key:0,type:"link",onClick:z=>i(X.roleId)},{default:oe(()=>c[7]||(c[7]=[Oe(" \u4FEE\u6539 ")])),_:2,__:[7]},1032,["onClick"])):_e("",!0),Je(o)("ENT_UR_ROLE_DEL")?(ye(),Le(b,{key:1,type:"link",danger:"",onClick:z=>s(X.roleId)},{default:oe(()=>c[8]||(c[8]=[Oe(" \u5220\u9664 ")])),_:2,__:[8]},1032,["onClick"])):_e("",!0)]),_:2},1024)):_e("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),C(Zo,{ref_key:"infoAddOrEdit",ref:r,callbackFunc:g},null,512)]),_:1})}}});export{va as default};
