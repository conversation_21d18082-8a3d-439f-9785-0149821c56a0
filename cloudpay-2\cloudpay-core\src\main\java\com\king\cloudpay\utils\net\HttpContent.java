package com.king.cloudpay.utils.net;

import com.alibaba.fastjson.JSON;

import java.util.Map;

import static java.util.Objects.requireNonNull;

/**
 * Http请求内容
 *
 * <AUTHOR>
 * @date 2021-06-08 11:00
 */
public class HttpContent {

    byte[] byteArrayContent;

    String contentType;

    private HttpContent(byte[] byteArrayContent, String contentType) {
        this.byteArrayContent = byteArrayContent;
        this.contentType = contentType;
    }

    public String stringContent() {
        return new String(this.byteArrayContent, APIResource.CHARSET);
    }

    public static HttpContent buildJSONContent(Map<String, Object> params) {
        requireNonNull(params);

        return new HttpContent(JSON.toJSONString(params).getBytes(APIResource.CHARSET), String.format("application/json; charset=%s", APIResource.CHARSET));
    }

    private static String createJSONString(Map<String, Object> params) {
        return JSON.toJSONString(params);
    }

}
