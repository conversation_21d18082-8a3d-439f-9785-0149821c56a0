import{ai as S}from"./index.fba97cfa.js";var E={exports:{}};(function(g){(function(t,s){g.exports?g.exports=s():t.ReconnectingWebSocket=s()})(S,function(){if(!("WebSocket"in window))return;function t(s,h,d){var p={debug:!1,automaticOpen:!0,reconnectInterval:1e3,maxReconnectInterval:3e4,reconnectDecay:1.5,timeoutInterval:2e3,maxReconnectAttempts:null};d||(d={});for(var u in p)typeof d[u]!="undefined"?this[u]=d[u]:this[u]=p[u];this.url=s,this.reconnectAttempts=0,this.readyState=WebSocket.CONNECTING,this.protocol=null;var e=this,c,m=!1,v=!1,o=document.createElement("div");o.addEventListener("open",function(n){e.onopen(n)}),o.addEventListener("close",function(n){e.onclose(n)}),o.addEventListener("connecting",function(n){e.onconnecting(n)}),o.addEventListener("message",function(n){e.onmessage(n)}),o.addEventListener("error",function(n){e.onerror(n)}),this.addEventListener=o.addEventListener.bind(o),this.removeEventListener=o.removeEventListener.bind(o),this.dispatchEvent=o.dispatchEvent.bind(o);function a(n,l){var f=document.createEvent("CustomEvent");return f.initCustomEvent(n,!1,!1,l),f}this.open=function(n){if(c=new WebSocket(e.url,h||[]),n){if(this.maxReconnectAttempts&&this.reconnectAttempts>this.maxReconnectAttempts)return}else o.dispatchEvent(a("connecting")),this.reconnectAttempts=0;(e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","attempt-connect",e.url);var l=c,f=setTimeout(function(){(e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","connection-timeout",e.url),v=!0,l.close(),v=!1},e.timeoutInterval);c.onopen=function(r){clearTimeout(f),(e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","onopen",e.url),e.protocol=c.protocol,e.readyState=WebSocket.OPEN,e.reconnectAttempts=0;var i=a("open");i.isReconnect=n,n=!1,o.dispatchEvent(i)},c.onclose=function(r){if(clearTimeout(b),c=null,m)e.readyState=WebSocket.CLOSED,o.dispatchEvent(a("close"));else{e.readyState=WebSocket.CONNECTING;var i=a("connecting");i.code=r.code,i.reason=r.reason,i.wasClean=r.wasClean,o.dispatchEvent(i),!n&&!v&&((e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","onclose",e.url),o.dispatchEvent(a("close")));var b=e.reconnectInterval*Math.pow(e.reconnectDecay,e.reconnectAttempts);setTimeout(function(){e.reconnectAttempts++,e.open(!0)},b>e.maxReconnectInterval?e.maxReconnectInterval:b)}},c.onmessage=function(r){(e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","onmessage",e.url,r.data);var i=a("message");i.data=r.data,o.dispatchEvent(i)},c.onerror=function(r){(e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","onerror",e.url,r),o.dispatchEvent(a("error"))}},this.automaticOpen==!0&&this.open(!1),this.send=function(n){if(c)return(e.debug||t.debugAll)&&console.debug("ReconnectingWebSocket","send",e.url,n),c.send(n);throw"INVALID_STATE_ERR : Pausing to reconnect websocket"},this.close=function(n,l){typeof n=="undefined"&&(n=1e3),m=!0,c&&c.close(n,l)},this.refresh=function(){c&&c.close()}}return t.prototype.onopen=function(s){},t.prototype.onclose=function(s){},t.prototype.onconnecting=function(s){},t.prototype.onmessage=function(s){},t.prototype.onerror=function(s){},t.debugAll=!1,t.CONNECTING=WebSocket.CONNECTING,t.OPEN=WebSocket.OPEN,t.CLOSING=WebSocket.CLOSING,t.CLOSED=WebSocket.CLOSED,t})})(E);var R=E.exports;export{R};
