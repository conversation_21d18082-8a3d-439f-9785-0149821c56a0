package com.king.cloudpay.mch.service;

import cn.hutool.core.util.IdUtil;
import com.king.cloudpay.core.beans.RequestKitBean;
import com.king.cloudpay.core.cache.ITokenService;
import com.king.cloudpay.core.cache.RedisUtil;
import com.king.cloudpay.core.constants.CS;
import com.king.cloudpay.core.entity.MchInfo;
import com.king.cloudpay.core.entity.SysUser;
import com.king.cloudpay.core.exception.BizException;
import com.king.cloudpay.core.exception.CloudpayAuthenticationException;
import com.king.cloudpay.core.googleCode.GoogleAuthenticator;
import com.king.cloudpay.core.jwt.JWTPayload;
import com.king.cloudpay.core.jwt.JWTUtils;
import com.king.cloudpay.core.model.security.JeeUserDetails;
import com.king.cloudpay.mch.bootstrap.config.SystemYmlConfig;
import com.king.cloudpay.service.impl.MchInfoService;
import com.king.cloudpay.service.impl.SysRoleEntRelaService;
import com.king.cloudpay.service.impl.SysRoleService;
import com.king.cloudpay.service.impl.SysUserService;
import com.king.cloudpay.service.mapper.SysEntitlementMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 认证Service
 *
 * @modify zhuxiao
 * @date 2021-04-27 15:50
 */
@Slf4j
@Service
public class AuthService {

    // Manual logger field (in case @Slf4j doesn't work properly)
    private static final Logger log = LoggerFactory.getLogger(AuthService.class);

    @Resource
    private AuthenticationManager authenticationManager;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysRoleEntRelaService sysRoleEntRelaService;
    @Autowired
    private MchInfoService mchInfoService;
    @Autowired
    private SysEntitlementMapper sysEntitlementMapper;
    @Autowired
    private SystemYmlConfig systemYmlConfig;
    @Autowired
    private RequestKitBean requestKitBean;

    /**
     * 认证
     *
     */
    public String auth(String username, String password, String gvercode) {

        //1. 生成spring-security usernamePassword类型对象
        UsernamePasswordAuthenticationToken upToken = new UsernamePasswordAuthenticationToken(username, password);

        //spring-security 自动认证过程；
        // 1. 进入 JeeUserDetailsServiceImpl.loadUserByUsername 获取用户基本信息；
        //2. SS根据UserDetails接口验证是否用户可用；
        //3. 最后返回loadUserByUsername 封装的对象信息；
        Authentication authentication = null;
        try {
            authentication = authenticationManager.authenticate(upToken);
        } catch (CloudpayAuthenticationException jex) {
            throw jex.getBizException() == null ? new BizException(jex.getMessage()) : jex.getBizException();
        } catch (BadCredentialsException e) {
            // 密码验证失败，提供更友好的错误信息
            throw new BizException("用户名或密码错误，请检查后重试！");
        } catch (AuthenticationException e) {
            log.error("AuthenticationException:", e);
            throw new BizException("认证服务出现异常，请重试或联系系统管理员！");
        }
        JeeUserDetails jeeUserDetails = (JeeUserDetails) authentication.getPrincipal();

        //验证通过后 再查询用户角色和权限信息集合
        SysUser sysUser = jeeUserDetails.getSysUser();
        // 校验ip是否白名单
        String clientIp = requestKitBean.getClientIp();
        log.info("商户后台请求IP:" + clientIp);
        if (StringUtils.isNotEmpty(sysUser.getWhiteIp())) {
            String[] split = sysUser.getWhiteIp().split(",");
            List<String> list = Arrays.asList(split);
            if (!list.contains(clientIp)) {
                throw new BizException("无访问权限");
            }
        } else {
            // 判断是否本地
            if (!"0:0:0:0:0:0:0:1".equals(clientIp)) {
                throw new BizException("无访问权限");
            }
        }

        //非超级管理员 && 不包含左侧菜单 进行错误提示
        if (sysUser.getIsAdmin() != CS.YES && sysEntitlementMapper.userHasLeftMenu(sysUser.getSysUserId(), CS.SYS_TYPE.MCH) <= 0) {
            throw new BizException("当前用户未分配任何菜单权限，请联系管理员进行分配后再登录！");
        }

        // 查询当前用户的商户信息
        MchInfo mchInfo = mchInfoService.getById(sysUser.getBelongInfoId());
        if (mchInfo != null) {
            // 判断当前商户状态是否可用
            if (mchInfo.getState() == CS.NO) {
                throw new BizException("当前商户状态不可用！");
            }
        }

        // 查询google验证器是否绑定
        if (mchInfo.getGoogleState() == 1) {
            if ("undefined".equals(gvercode)) {
                throw new BizException("请输入Google验证码！");
            }
            try {
                Long.valueOf(gvercode);
            } catch (Exception e) {
                throw new BizException("请输入正确的Google验证码！");
            }
            // 判断google验证器密码是否正确
            boolean flag = GoogleAuthenticator.check_code(mchInfo.getGooglePrivate(), Long.valueOf(gvercode), System.currentTimeMillis());
            if (!flag) {
                throw new BizException("Google验证码错误！");
            }
        }
        // 放置权限集合
        jeeUserDetails.setAuthorities(getUserAuthority(sysUser));

        //生成token
        String cacheKey = CS.getCacheKeyToken(sysUser.getSysUserId(), IdUtil.fastUUID());

        //生成iToken 并放置到缓存
        ITokenService.processTokenCache(jeeUserDetails, cacheKey); //处理token 缓存信息

        //将信息放置到Spring-security context中
        UsernamePasswordAuthenticationToken authenticationRest = new UsernamePasswordAuthenticationToken(jeeUserDetails, null, jeeUserDetails.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authenticationRest);

        //返回JWTToken
        return JWTUtils.generateToken(new JWTPayload(jeeUserDetails), systemYmlConfig.getJwtSecret());
    }

    /**
     * 根据用户ID 更新缓存中的权限集合， 使得分配实时生效
     *
     */
    public void refAuthentication(List<Long> sysUserIdList) {

        if (sysUserIdList == null || sysUserIdList.isEmpty()) {
            return;
        }

        Map<Long, SysUser> sysUserMap = new HashMap<>();

        // 查询 sysUserId 和 state
        sysUserService.list(
                SysUser.gw()
                        .select(SysUser::getSysUserId, SysUser::getState)
                        .in(SysUser::getSysUserId, sysUserIdList)
        ).stream().forEach(item -> sysUserMap.put(item.getSysUserId(), item));

        for (Long sysUserId : sysUserIdList) {

            Collection<String> cacheKeyList = RedisUtil.keys(CS.getCacheKeyToken(sysUserId, "*"));
            if (cacheKeyList == null || cacheKeyList.isEmpty()) {
                continue;
            }

            for (String cacheKey : cacheKeyList) {

                //用户不存在 || 已禁用 需要删除Redis
                if (sysUserMap.get(sysUserId) == null || sysUserMap.get(sysUserId).getState() == CS.PUB_DISABLE) {
                    RedisUtil.del(cacheKey);
                    continue;
                }

                JeeUserDetails jwtBaseUser = RedisUtil.getObject(cacheKey, JeeUserDetails.class);
                if (jwtBaseUser == null) {
                    continue;
                }

                // 重新放置sysUser对象
                jwtBaseUser.setSysUser(sysUserService.getById(sysUserId));

                //查询放置权限数据
                jwtBaseUser.setAuthorities(getUserAuthority(jwtBaseUser.getSysUser()));

                //保存token  失效时间不变
                RedisUtil.set(cacheKey, jwtBaseUser);
            }
        }

    }

    /**
     * 根据用户ID 删除用户缓存信息
     *
     */
    public void delAuthentication(List<Long> sysUserIdList) {
        if (sysUserIdList == null || sysUserIdList.isEmpty()) {
            return;
        }
        for (Long sysUserId : sysUserIdList) {
            Collection<String> cacheKeyList = RedisUtil.keys(CS.getCacheKeyToken(sysUserId, "*"));
            if (cacheKeyList == null || cacheKeyList.isEmpty()) {
                continue;
            }
            for (String cacheKey : cacheKeyList) {
                RedisUtil.del(cacheKey);
            }
        }
    }

    public List<SimpleGrantedAuthority> getUserAuthority(SysUser sysUser) {

        //用户拥有的角色集合  需要以ROLE_ 开头,  用户拥有的权限集合
        List<String> roleList = sysRoleService.findListByUser(sysUser.getSysUserId());
        List<String> entList = sysRoleEntRelaService.selectEntIdsByUserId(sysUser.getSysUserId(), sysUser.getIsAdmin(), sysUser.getSysType());

        List<SimpleGrantedAuthority> grantedAuthorities = new LinkedList<>();
        roleList.stream().forEach(role -> grantedAuthorities.add(new SimpleGrantedAuthority(role)));
        entList.stream().forEach(ent -> grantedAuthorities.add(new SimpleGrantedAuthority(ent)));
        return grantedAuthorities;
    }

}
