package com.king.cloudpay.pay.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.king.cloudpay.components.mq.constant.MQVenderCS;
import com.king.cloudpay.components.mq.model.AbstractMQ;
import com.king.cloudpay.components.mq.vender.IMQSender;

/**
 * NoOp MQ配置类，用于禁用MQ功能时提供空实现
 */
@Configuration
@ConditionalOnProperty(name = MQVenderCS.YML_VENDER_KEY, havingValue = "none")
public class NoOpMQConfig {

    @Bean
    public IMQSender noOpMQSender() {
        return new IMQSender() {
            @Override
            public void send(AbstractMQ mqModel) {
                // Empty implementation, no message sent
                System.out.println("NoOp MQ Sender: Message ignored - " + mqModel.getClass().getSimpleName());
            }

            @Override
            public void send(AbstractMQ mqModel, int delay) {
                // Empty implementation, no message sent
                System.out.println("NoOp MQ Sender: Delayed message ignored - " + mqModel.getClass().getSimpleName() + " with delay: " + delay);
            }
        };
    }
}
