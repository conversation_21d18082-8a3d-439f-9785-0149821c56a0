import{p as s}from"./index.8746381c.js";const o={list:(e,t)=>s.request({url:e,method:"GET",params:t},!0,!0,!1),add:(e,t)=>s.request({url:e,method:"POST",data:t},!0,!0,!1),getById:(e,t)=>s.request({url:e+"/"+t,method:"GET"},!0,!0,!1),updateById:(e,t,r)=>s.request({url:e+"/"+t,method:"PUT",data:r},!0,!0,!1),delById:(e,t)=>s.request({url:e+"/"+t,method:"DELETE"},!0,!0,!1)},i={list:(e,t)=>s.request({url:e,method:"GET",params:t},!0,!0,!0),add:(e,t)=>s.request({url:e,method:"POST",data:t},!0,!0,!0),getById:(e,t)=>s.request({url:e+"/"+t,method:"GET"},!0,!0,!0),updateById:(e,t,r)=>s.request({url:e+"/"+t,method:"PUT",data:r},!0,!0,!0),delById:(e,t)=>s.request({url:e+"/"+t,method:"DELETE"},!0,!0,!0)},p="/api/sysEnts",_="/api/sysRoles",d="/api/sysRoleEntRelas",I="/api/sysUsers",l="/api/sysUserRoleRelas",c="/api/isvInfo",y="/api/mchInfo",T="/api/mchApps",f="/api/payOrder",m="/api/refundOrder",L="/api/mchNotify",R="api/sysLog",u="api/sysConfigs",a="api/mainChart",P="/api/payIfDefines",h="/api/payWays",S="/api/isv/payConfigs",E="/api/mch/payConfigs",A="/api/mch/payPassages",U="/api/transferOrders",q={avatar:s.baseUrl+"/api/ossFiles/avatar",ifBG:s.baseUrl+"/api/ossFiles/ifBG",cert:s.baseUrl+"/api/ossFiles/cert"};function g(e){return s.request({url:"/api/sysEnts/showTree?sysType="+e,method:"GET"})}function C(e,t,r){return s.request({url:"/api/payOrder/refunds/"+e,method:"POST",data:{refundAmount:t,refundReason:r}})}function O(e,t){return s.request({url:"api/sysUserRoleRelas/relas/"+e,method:"POST",data:{roleIdListStr:JSON.stringify(t)}})}function G(e,t){return s.request({url:"/api/isv/payConfigs/"+e+"/"+t,method:"get"})}function N(e,t){return s.request({url:"/api/mch/payConfigs/"+e+"/"+t,method:"get"})}function F(e,t){return s.request({url:"/api/mch/payPassages/availablePayInterface/"+e+"/"+t,method:"GET"})}function v(){return s.request({url:a+"/payAmountWeek",method:"GET"})}function B(){return s.request({url:a+"/numCount",method:"GET"})}function M(e){return s.request({url:a+"/payCount",method:"GET",params:e})}function Y(e){return s.request({url:a+"/payTypeCount",method:"GET",params:e})}function b(e){return s.request({url:"/api/current/modifyPwd",method:"put",data:e})}function D(e){return s.request({url:"/api/current/user",method:"put",data:e})}function H(){return s.request({url:"/api/current/user",method:"get"})}function W(e){return s.request({url:u+"/"+e,method:"GET"})}function k(e,t){return s.request({url:"/api/sysEnts/bySysType",method:"GET",params:{entId:e,sysType:t}})}function w(e){return s.request({url:"/api/mchNotify/resend/"+e,method:"POST"})}function x(e){return s.request({url:"/api/mch/payConfigs/alipayIsvsubMchAuthUrls/"+e,method:"GET"})}export{I as A,F as B,A as C,x as D,f as E,C as F,m as G,U as H,L as I,w as J,W as K,u as L,R as M,D as a,b,v as c,B as d,M as e,Y as f,H as g,i as h,_ as i,l as j,O as k,g as l,d as m,k as n,p as o,h as p,P as q,o as r,c as s,G as t,q as u,S as v,y as w,T as x,N as y,E as z};
