import{M as T,b as w,a1 as de,J as A,ab as ve,bS as pe,d as oe,r as q,bY as F,G as x,h as ge,P as C,bm as me,O as ne,E as le,V as be,H as N,D as P,ax as Se,as as G,aA as Me,L as Te,e as ye}from"./index.8746381c.js";function ee(){const e=n=>{e.current=n};return e}const j=(e,n)=>{let{height:l,offset:o,prefixCls:r,onInnerResize:s}=e,{slots:f}=n;var a;let d={},i={display:"flex",flexDirection:"column"};return o!==void 0&&(d={height:`${l}px`,position:"relative",overflow:"hidden"},i=T(T({},i),{transform:`translateY(${o}px)`,position:"absolute",left:0,right:0,top:0})),w("div",{style:d},[w(de,{onResize:v=>{let{offsetHeight:h}=v;h&&s&&s()}},{default:()=>[w("div",{style:i,class:A({[`${r}-holder-inner`]:r})},[(a=f.default)===null||a===void 0?void 0:a.call(f)])]})])};j.displayName="Filter";j.inheritAttrs=!1;j.props={prefixCls:String,height:Number,offset:Number,onInnerResize:Function};var He=j;const se=(e,n)=>{let{setRef:l}=e,{slots:o}=n;var r;const s=ve((r=o.default)===null||r===void 0?void 0:r.call(o));return s&&s.length?pe(s[0],{ref:l}):s};se.props={setRef:{type:Function,default:()=>{}}};var we=se;const Ee=20;function te(e){return"touches"in e?e.touches[0].pageY:e.pageY}var Re=oe({compatConfig:{MODE:3},name:"ScrollBar",inheritAttrs:!1,props:{prefixCls:String,scrollTop:Number,scrollHeight:Number,height:Number,count:Number,onScroll:{type:Function},onStartMove:{type:Function},onStopMove:{type:Function}},setup(){return{moveRaf:null,scrollbarRef:ee(),thumbRef:ee(),visibleTimeout:null,state:q({dragging:!1,pageY:null,startTop:null,visible:!1})}},watch:{scrollTop:{handler(){this.delayHidden()},flush:"post"}},mounted(){var e,n;(e=this.scrollbarRef.current)===null||e===void 0||e.addEventListener("touchstart",this.onScrollbarTouchStart,F?{passive:!1}:!1),(n=this.thumbRef.current)===null||n===void 0||n.addEventListener("touchstart",this.onMouseDown,F?{passive:!1}:!1)},beforeUnmount(){this.removeEvents(),clearTimeout(this.visibleTimeout)},methods:{delayHidden(){clearTimeout(this.visibleTimeout),this.state.visible=!0,this.visibleTimeout=setTimeout(()=>{this.state.visible=!1},2e3)},onScrollbarTouchStart(e){e.preventDefault()},onContainerMouseDown(e){e.stopPropagation(),e.preventDefault()},patchEvents(){window.addEventListener("mousemove",this.onMouseMove),window.addEventListener("mouseup",this.onMouseUp),this.thumbRef.current.addEventListener("touchmove",this.onMouseMove,F?{passive:!1}:!1),this.thumbRef.current.addEventListener("touchend",this.onMouseUp)},removeEvents(){window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("mouseup",this.onMouseUp),this.scrollbarRef.current.removeEventListener("touchstart",this.onScrollbarTouchStart,F?{passive:!1}:!1),this.thumbRef.current&&(this.thumbRef.current.removeEventListener("touchstart",this.onMouseDown,F?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchmove",this.onMouseMove,F?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchend",this.onMouseUp)),x.cancel(this.moveRaf)},onMouseDown(e){const{onStartMove:n}=this.$props;T(this.state,{dragging:!0,pageY:te(e),startTop:this.getTop()}),n(),this.patchEvents(),e.stopPropagation(),e.preventDefault()},onMouseMove(e){const{dragging:n,pageY:l,startTop:o}=this.state,{onScroll:r}=this.$props;if(x.cancel(this.moveRaf),n){const s=te(e)-l,f=o+s,a=this.getEnableScrollRange(),d=this.getEnableHeightRange(),i=d?f/d:0,v=Math.ceil(i*a);this.moveRaf=x(()=>{r(v)})}},onMouseUp(){const{onStopMove:e}=this.$props;this.state.dragging=!1,e(),this.removeEvents()},getSpinHeight(){const{height:e,scrollHeight:n}=this.$props;let l=e/n*100;return l=Math.max(l,Ee),l=Math.min(l,e/2),Math.floor(l)},getEnableScrollRange(){const{scrollHeight:e,height:n}=this.$props;return e-n||0},getEnableHeightRange(){const{height:e}=this.$props,n=this.getSpinHeight();return e-n||0},getTop(){const{scrollTop:e}=this.$props,n=this.getEnableScrollRange(),l=this.getEnableHeightRange();return e===0||n===0?0:e/n*l},showScroll(){const{height:e,scrollHeight:n}=this.$props;return n>e}},render(){const{dragging:e,visible:n}=this.state,{prefixCls:l}=this.$props,o=this.getSpinHeight()+"px",r=this.getTop()+"px",s=this.showScroll(),f=s&&n;return w("div",{ref:this.scrollbarRef,class:A(`${l}-scrollbar`,{[`${l}-scrollbar-show`]:s}),style:{width:"8px",top:0,bottom:0,right:0,position:"absolute",display:f?void 0:"none"},onMousedown:this.onContainerMouseDown,onMousemove:this.delayHidden},[w("div",{ref:this.thumbRef,class:A(`${l}-scrollbar-thumb`,{[`${l}-scrollbar-thumb-moving`]:e}),style:{width:"100%",height:o,top:r,left:0,position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:"99px",cursor:"pointer",userSelect:"none"},onMousedown:this.onMouseDown},null)])}});function xe(e,n,l,o){const r=new Map,s=new Map,f=ge(Symbol("update"));C(e,()=>{f.value=Symbol("update")});let a;function d(){x.cancel(a)}function i(){d(),a=x(()=>{r.forEach((h,c)=>{if(h&&h.offsetParent){const{offsetHeight:g}=h;s.get(c)!==g&&(f.value=Symbol("update"),s.set(c,h.offsetHeight))}})})}function v(h,c){const g=n(h),p=r.get(g);c?(r.set(g,c.$el||c),i()):r.delete(g),!p!=!c&&(c?l==null||l(h):o==null||o(h))}return me(()=>{d()}),[v,i,s,f]}function Le(e,n,l,o,r,s,f,a){let d;return i=>{if(i==null){a();return}x.cancel(d);const v=n.value,h=o.itemHeight;if(typeof i=="number")f(i);else if(i&&typeof i=="object"){let c;const{align:g}=i;"index"in i?{index:c}=i:c=v.findIndex(y=>r(y)===i.key);const{offset:p=0}=i,I=(y,$)=>{if(y<0||!e.value)return;const D=e.value.clientHeight;let b=!1,M=$;if(D){const O=$||g;let k=0,E=0,R=0;const Y=Math.min(v.length,c);for(let S=0;S<=Y;S+=1){const V=r(v[S]);E=k;const B=l.get(V);R=E+(B===void 0?h:B),k=R,S===c&&B===void 0&&(b=!0)}const _=e.value.scrollTop;let H=null;switch(O){case"top":H=E-p;break;case"bottom":H=R-D+p;break;default:{const S=_+D;E<_?M="top":R>S&&(M="bottom")}}H!==null&&H!==_&&f(H)}d=x(()=>{b&&s(),I(y-1,M)},2)};I(5)}}}const Fe=typeof navigator=="object"&&/Firefox/i.test(navigator.userAgent);var Ce=Fe,ie=(e,n)=>{let l=!1,o=null;function r(){clearTimeout(o),l=!0,o=setTimeout(()=>{l=!1},50)}return function(s){let f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const a=s<0&&e.value||s>0&&n.value;return f&&a?(clearTimeout(o),l=!1):(!a||l)&&r(),!l&&a}};function $e(e,n,l,o){let r=0,s=null,f=null,a=!1;const d=ie(n,l);function i(h){if(!e.value)return;x.cancel(s);const{deltaY:c}=h;r+=c,f=c,!d(c)&&(Ce||h.preventDefault(),s=x(()=>{o(r*(a?10:1)),r=0}))}function v(h){!e.value||(a=h.detail===f)}return[i,v]}const De=14/15;function Oe(e,n,l){let o=!1,r=0,s=null,f=null;const a=()=>{s&&(s.removeEventListener("touchmove",d),s.removeEventListener("touchend",i))},d=c=>{if(o){const g=Math.ceil(c.touches[0].pageY);let p=r-g;r=g,l(p)&&c.preventDefault(),clearInterval(f),f=setInterval(()=>{p*=De,(!l(p,!0)||Math.abs(p)<=.1)&&clearInterval(f)},16)}},i=()=>{o=!1,a()},v=c=>{a(),c.touches.length===1&&!o&&(o=!0,r=Math.ceil(c.touches[0].pageY),s=c.target,s.addEventListener("touchmove",d,{passive:!1}),s.addEventListener("touchend",i))},h=()=>{};ne(()=>{document.addEventListener("touchmove",h,{passive:!1}),C(e,c=>{n.value.removeEventListener("touchstart",v),a(),clearInterval(f),c&&n.value.addEventListener("touchstart",v,{passive:!1})},{immediate:!0})}),le(()=>{document.removeEventListener("touchmove",h)})}var _e=globalThis&&globalThis.__rest||function(e,n){var l={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(l[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(l[o[r]]=e[o[r]]);return l};const Ne=[],Ie={overflowY:"auto",overflowAnchor:"none"};function ke(e,n,l,o,r,s){let{getKey:f}=s;return e.slice(n,l+1).map((a,d)=>{const i=n+d,v=r(a,i,{}),h=f(a);return w(we,{key:h,setRef:c=>o(a,c)},{default:()=>[v]})})}const Be=oe({compatConfig:{MODE:3},name:"List",inheritAttrs:!1,props:{prefixCls:String,data:be.array,height:Number,itemHeight:Number,fullHeight:{type:Boolean,default:void 0},itemKey:{type:[String,Number,Function],required:!0},component:{type:[String,Object]},virtual:{type:Boolean,default:void 0},children:Function,onScroll:Function,onMousedown:Function,onMouseenter:Function,onVisibleChange:Function},setup(e,n){let{expose:l}=n;const o=N(()=>{const{height:t,itemHeight:u,virtual:m}=e;return!!(m!==!1&&t&&u)}),r=N(()=>{const{height:t,itemHeight:u,data:m}=e;return o.value&&m&&u*m.length>t}),s=q({scrollTop:0,scrollMoving:!1}),f=N(()=>e.data||Ne),a=P([]);C(f,()=>{a.value=Se(f.value).slice()},{immediate:!0});const d=P(t=>{});C(()=>e.itemKey,t=>{typeof t=="function"?d.value=t:d.value=u=>u==null?void 0:u[t]},{immediate:!0});const i=P(),v=P(),h=P(),c=t=>d.value(t),g={getKey:c};function p(t){let u;typeof t=="function"?u=t(s.scrollTop):u=t;const m=k(u);i.value&&(i.value.scrollTop=m),s.scrollTop=m}const[I,y,$,D]=xe(a,c,null,null),b=q({scrollHeight:void 0,start:0,end:0,offset:void 0}),M=P(0);ne(()=>{G(()=>{var t;M.value=((t=v.value)===null||t===void 0?void 0:t.offsetHeight)||0})}),Me(()=>{G(()=>{var t;M.value=((t=v.value)===null||t===void 0?void 0:t.offsetHeight)||0})}),C([o,a],()=>{o.value||T(b,{scrollHeight:void 0,start:0,end:a.value.length-1,offset:void 0})},{immediate:!0}),C([o,a,M,r],()=>{o.value&&!r.value&&T(b,{scrollHeight:M.value,start:0,end:a.value.length-1,offset:void 0}),i.value&&(s.scrollTop=i.value.scrollTop)},{immediate:!0}),C([r,o,()=>s.scrollTop,a,D,()=>e.height,M],()=>{if(!o.value||!r.value)return;let t=0,u,m,L;const z=a.value.length,ce=a.value,Z=s.scrollTop,{itemHeight:Q,height:X}=e,ue=Z+X;for(let U=0;U<z;U+=1){const fe=ce[U],he=c(fe);let K=$.get(he);K===void 0&&(K=Q);const W=t+K;u===void 0&&W>=Z&&(u=U,m=t),L===void 0&&W>ue&&(L=U),t=W}u===void 0&&(u=0,m=0,L=Math.ceil(X/Q)),L===void 0&&(L=z-1),L=Math.min(L+1,z),T(b,{scrollHeight:t,start:u,end:L,offset:m})},{immediate:!0});const O=N(()=>b.scrollHeight-e.height);function k(t){let u=t;return Number.isNaN(O.value)||(u=Math.min(u,O.value)),u=Math.max(u,0),u}const E=N(()=>s.scrollTop<=0),R=N(()=>s.scrollTop>=O.value),Y=ie(E,R);function _(t){p(t)}function H(t){var u;const{scrollTop:m}=t.currentTarget;m!==s.scrollTop&&p(m),(u=e.onScroll)===null||u===void 0||u.call(e,t)}const[S,V]=$e(o,E,R,t=>{p(u=>u+t)});Oe(o,i,(t,u)=>Y(t,u)?!1:(S({preventDefault(){},deltaY:t}),!0));function B(t){o.value&&t.preventDefault()}const J=()=>{i.value&&(i.value.removeEventListener("wheel",S,F?{passive:!1}:!1),i.value.removeEventListener("DOMMouseScroll",V),i.value.removeEventListener("MozMousePixelScroll",B))};Te(()=>{G(()=>{i.value&&(J(),i.value.addEventListener("wheel",S,F?{passive:!1}:!1),i.value.addEventListener("DOMMouseScroll",V),i.value.addEventListener("MozMousePixelScroll",B))})}),le(()=>{J()});const re=Le(i,a,$,e,c,y,p,()=>{var t;(t=h.value)===null||t===void 0||t.delayHidden()});l({scrollTo:re});const ae=N(()=>{let t=null;return e.height&&(t=T({[e.fullHeight?"height":"maxHeight"]:e.height+"px"},Ie),o.value&&(t.overflowY="hidden",s.scrollMoving&&(t.pointerEvents="none"))),t});return C([()=>b.start,()=>b.end,a],()=>{if(e.onVisibleChange){const t=a.value.slice(b.start,b.end+1);e.onVisibleChange(t,a.value)}},{flush:"post"}),{state:s,mergedData:a,componentStyle:ae,onFallbackScroll:H,onScrollBar:_,componentRef:i,useVirtual:o,calRes:b,collectHeight:y,setInstance:I,sharedConfig:g,scrollBarRef:h,fillerInnerRef:v,delayHideScrollBar:()=>{var t;(t=h.value)===null||t===void 0||t.delayHidden()}}},render(){const e=T(T({},this.$props),this.$attrs),{prefixCls:n="rc-virtual-list",height:l,itemHeight:o,fullHeight:r,data:s,itemKey:f,virtual:a,component:d="div",onScroll:i,children:v=this.$slots.default,style:h,class:c}=e,g=_e(e,["prefixCls","height","itemHeight","fullHeight","data","itemKey","virtual","component","onScroll","children","style","class"]),p=A(n,c),{scrollTop:I}=this.state,{scrollHeight:y,offset:$,start:D,end:b}=this.calRes,{componentStyle:M,onFallbackScroll:O,onScrollBar:k,useVirtual:E,collectHeight:R,sharedConfig:Y,setInstance:_,mergedData:H,delayHideScrollBar:S}=this;return w("div",ye({style:T(T({},h),{position:"relative"}),class:p},g),[w(d,{class:`${n}-holder`,style:M,ref:"componentRef",onScroll:O,onMouseenter:S},{default:()=>[w(He,{prefixCls:n,height:y,offset:$,onInnerResize:R,ref:"fillerInnerRef"},{default:()=>ke(H,D,b,_,v,Y)})]}),E&&w(Re,{ref:"scrollBarRef",prefixCls:n,scrollTop:I,height:l,scrollHeight:y,count:H.length,onScroll:k,onStartMove:()=>{this.state.scrollMoving=!0},onStopMove:()=>{this.state.scrollMoving=!1}},null)])}});var Ue=Be;export{Ue as L,ee as c};
