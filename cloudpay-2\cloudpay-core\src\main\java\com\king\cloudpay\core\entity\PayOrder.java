package com.king.cloudpay.core.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.king.cloudpay.core.aop.Excel;
import com.king.cloudpay.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 支付订单表
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_pay_order")
public class PayOrder extends BaseModel<PayOrder> {

    public static final LambdaQueryWrapper<PayOrder> gw() {
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    public static final byte STATE_INIT = 0; //订单生成
    public static final byte STATE_ING = 1; //支付中
    public static final byte STATE_SUCCESS = 2; //支付成功
    public static final byte STATE_FAIL = 3; //支付失败
    public static final byte STATE_CANCEL = 4; //已撤销
    public static final byte STATE_REFUND = 5; //已退款
    public static final byte STATE_CLOSED = 6; //订单关闭

    public static final byte SETTLED = 1; // 已结算
    public static final byte UNSETTLEMENT = 0; // 未结算

    public static final byte PAYTE_H5 = 1; // h5 支付方式
    public static final byte PAYTE_PLUGIN = 2; // h5 插件支付方式
    public static final byte PAYTE_QRCODE = 3; // 二维码支付凡事
    public static final byte PAYTE_API = 4; // api 原生方式
    public static final byte PAYTE_CARDS = 5; // 收款账户模式，个卡
    public static final byte PAYTE_U = 6; // u卡支付
    public static final byte PAYTE_PUBLIC = 8; // 公户支付模式

    public static final byte REFUND_STATE_NONE = 0; //未发生实际退款
    public static final byte REFUND_STATE_SUB = 1; //部分退款
    public static final byte REFUND_STATE_ALL = 2; //全额退款

    public static final byte DIVISION_MODE_FORBID = 0; //该笔订单不允许分账
    public static final byte DIVISION_MODE_AUTO = 1; //支付成功按配置自动完成分账
    public static final byte DIVISION_MODE_MANUAL = 2; //商户手动分账(解冻商户金额)

    public static final byte DIVISION_STATE_UNHAPPEN = 0; //未发生分账
    public static final byte DIVISION_STATE_WAIT_TASK = 1; //等待分账任务处理
    public static final byte DIVISION_STATE_ING = 2; //分账处理中
    public static final byte DIVISION_STATE_FINISH = 3; //分账任务已结束(不体现状态)

    public static final byte PAYSUCCESSSTATE_PART = 0; // 部分支付成功
    public static final byte PAYSUCCESSSTATE_ALL = 1; // 订单金额全部支付成功

    /**
     * 支付订单号
     */
    @TableId
    @Excel(name = "支付订单号")
    private String payOrderId;

    /**
     * 商户号
     */
    @Excel(name = "商户号")
    private String mchNo;

    /**
     * 卡商号
     */
    @Excel(name = "卡商号")
    private String cardNo;

    /**
     * 服务商号
     */
    @Excel(name = "服务商号")
    private String isvNo;

    /**
     * 应用ID
     */
    @Excel(name = "应用ID")
    private String appId;

    /**
     * 绑定 usdt账号
     */
    private Long uId;
    /**
     * 商户名称
     */
    @Excel(name = "商户名称")
    private String mchName;

    /**
     * 支付类型 1.H5 支付方式 2.H5 插件模式 3.二维码支付 4 原生 api 凡事 5-收款账户模式 6 usdt 支付 7 卡商代收代付
     */
    @Excel(name = "支付类型", readConverterExp = "1=H5 支付方式,2=H5 插件模式,3=二维码支付,4=原生 api 凡事,5=收款账户模式,6=usdt 支付,7=卡商代收代付")
    private String payType;

    /**
     * 类型: 1-普通商户, 2-特约商户(服务商模式)
     */
    private Byte mchType;

    /**
     * 商户订单号
     */
    @Excel(name = "商户订单号")
    private String mchOrderNo;

    /**
     * 支付接口代码
     */
    @Excel(name = "支付接口代码")
    private String ifCode;

    /**
     * 支付方式代码
     */
    @Excel(name = "支付方式代码")
    private String wayCode;

    /**
     * 支付金额,单位分
     */
    @Excel(name = "支付金额", pToYuan = true)
    private Long amount;

    /**
     * 实际支付金额,单位分
     */
    @Excel(name = "实际支付金额", pToYuan = true)
    private Long practicalAmount;

    //加纳支付wepay
    @Excel(name = "银行编号", pToYuan = true)
    private Long customerBankCode;

    /**
     * 代收单笔收取费用，单位分
     */
    @Excel(name = "代收单笔收取费用", pToYuan = true)
    private Long paySingleRateCost;

    /**
     * 代收单笔成本费油
     */
    @Excel(name = "代收单笔成本费油", pToYuan = true)
    private Long paySingleCostAmount;

    /**
     * 商户手续费费率快照
     */
    @Excel(name = "商户手续费费率")
    private BigDecimal mchFeeRate;

    /**
     * 商户手续费,单位分
     */
    @Excel(name = "商户手续费", pToYuan = true)
    private Long mchFeeAmount;

    /**
     * 实际商户手续费,单位分
     */
    @Excel(name = "实际商户手续费", pToYuan = true)
    private Long practicalMchFeeAmount;

    /**
     * 代收成本费率费率
     */
    @Excel(name = "代收成本费率费率")
    private BigDecimal payRateCost;

    /**
     * 代收成本费用
     */
    @Excel(name = "代收成本费用", pToYuan = true)
    private Long payRateAmount;

    /**
     * 实际代收成本费用
     */
    @Excel(name = "实际代收成本费用", pToYuan = true)
    private Long practicalPayRateAmount;

    /**
     * 卡商成本费率
     */
    @Excel(name = "卡商成本费率")
    private BigDecimal cardCostRate;

    /**
     * 卡商成本费用 （分）
     */
    @Excel(name = "卡商成本费用", pToYuan = true)
    @TableField("card_cost_amount")
    private Long cardCostAmount;

    /**
     * 实际卡商成本费用 （分）
     */
    @Excel(name = "实际卡商成本费用", pToYuan = true)
    @TableField("practical_card_cost_amount")
    private Long practicalCardCostAmount;

    /**
     * 三位货币代码,人民币:cny
     */
    @Excel(name = "货币代码")
    private String currency;

    /**
     * 顧客姓名
     */
    @Excel(name = "顧客姓名")
    private String customerName;

    /**
     * 顧客證書
     */
    @Excel(name = "顧客證書")
    private String customerCert;

    /**
     * 顧客證書
     */
    @Excel(name = "顧客姓名")
    private String name;

    /**
     * 顧客郵箱
     */
    @Excel(name = "顧客郵箱")
    private String customerEmail;

    /**
     * 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
     */
    @Excel(name = "支付状态", readConverterExp = "0=订单生成,1=支付中,2=支付成功,3=支付失败,4=已撤销,5=已退款,6=订单关闭")
    private Byte state;

    /**
     * 结算状态: 0-未结算, 1-已结算
     */
    @Excel(name = "支付状态", readConverterExp = "0=未结算,1=已结算")
    private Byte settlementState;

    /**
     * 向下游回调状态, 0-未发送, 1-已发送
     */
    @Excel(name = "向下游回调状态", readConverterExp = "0=未发送,1=已发送")
    private Byte notifyState;

    /**
     * 客户端IP
     */
    @Excel(name = "客户端IP")
    private String clientIp;

    /**
     * 客户手机号
     */
    @Excel(name = "客户手机号")
    private String phoneNumber;

    /**
     * 商品标题
     */
    @Excel(name = "商品标题")
    private String subject;

    /**
     * 商品描述信息
     */
    @Excel(name = "商品描述信息")
    private String body;

    /**
     * 特定渠道发起额外参数
     */
    @Excel(name = "特定渠道发起额外参数")
    private String channelExtra;

    /**
     * 渠道用户标识,如微信openId,支付宝账号
     */
    private String channelUser;

    /**
     * 渠道订单号
     */
    @Excel(name = "渠道订单号")
    private String channelOrderNo;

    /**
     * 退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款
     */
    private Byte refundState;

    /**
     * 退款次数
     */
    private Integer refundTimes;

    /**
     * 退款总金额,单位分
     */
    private Long refundAmount;

    /**
     * 订单分账模式：0-该笔订单不允许分账, 1-支付成功按配置自动完成分账, 2-商户手动分账(解冻商户金额)
     */
    private Byte divisionMode;

    /**
     * 0-未发生分账, 1-等待分账任务处理, 2-分账处理中, 3-分账任务已结束(不体现状态)
     */
    private Byte divisionState;

    /**
     * 最新分账时间
     */
    private Date divisionLastTime;

    /**
     * 渠道支付错误码
     */
    @Excel(name = "渠道支付错误码")
    private String errCode;

    /**
     * 渠道支付错误描述
     */
    @Excel(name = "渠道支付错误描述")
    private String errMsg;

    /**
     * 商户扩展参数
     */
    @Excel(name = "商户扩展参数")
    private String extParam;

    /**
     * 异步通知地址
     */
    @Excel(name = "异步通知地址")
    private String notifyUrl;

    /**
     * 页面跳转地址
     */
    @Excel(name = "页面跳转地址")
    private String returnUrl;

    /**
     * 订单失效时间
     */
    @Excel(name = "订单失效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expiredTime;

    /**
     * 订单支付成功时间
     */
    @Excel(name = "订单支付成功时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date successTime;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 会员是否上传交易流水号 0 未上传 1 已上传
     */
    private Byte userIsUpload;

    /**
     * 第三方线下支付完成状态 0 部分支付成功，1 全部支付成功
     */
    private Byte paySuccessState;

    /**
     * 多笔交易记录
     */
    private String tradeExpandRecord;

    /**
     * 卡商收款凭证上传状态 0 未上传 1已上传
     */
    private Byte cardCertificateState;

    /**
     * 卡商收款凭证
     */
    private String cardCertificateImgFileList;

    /**
     * 支付时绑定的银行卡号
     */
    private Long bankAccountId;

    /**
     * 如果这笔订单是 卡商 app 短信自动识别的，绑定那条短信识别成功短信ID
     */
    private Long smsId;

    /**
     * 结算静默天数 比如遇到休息日
     */
    private Long restDays;

    /**
     * 渠道链接
     */
    private String channelUrl;

    // 作为前端显示参数
    /**
     * 支付订单为 尼日利亚的显示：尼日利亚的收款卡号
     */
    @TableField(exist = false)
    private JSONObject virtualAccountInfo;

    /**
     * 会员上传的交易流水号信息
     */
    @TableField(exist = false)
    private List<BankAccountDeal> bankAccountDeal;

    /**
     * 商户代收到了哪张银行卡
     */
    @TableField(exist = false)
    private BankAccount bankAccount;

    /**
     * 卡商总手续费
     */
    @TableField(exist = false)
    private Long totalCardMerchantFee;

    /**
     * app 自动代收匹配中的短信信息
     */
    @TableField(exist = false)
    private TransactionSms transactionSms;

    // Manual getter and setter methods for Lombok compatibility
    // These ensure the methods are available even if Lombok processing fails
    public String getPayOrderId() {
        return payOrderId;
    }

    public void setPayOrderId(String payOrderId) {
        this.payOrderId = payOrderId;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getMchNo() {
        return mchNo;
    }

    public void setMchNo(String mchNo) {
        this.mchNo = mchNo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getWayCode() {
        return wayCode;
    }

    public void setWayCode(String wayCode) {
        this.wayCode = wayCode;
    }

}
