import{m as V,r as k,n as q,j as F}from"./manage.2dfb5a24.js";import{e as x,g as B,r as S,o as d,c as I,E,b as r,a0 as M,f as w,C as b,w as u,a as N,d as h,a1 as z,I as H,j as J,F as A,B as $,V as G,M as L,t as Q,m as C,$ as W}from"./index.fba97cfa.js";const X={style:{"padding-bottom":"50px"}},Y={key:0},Z=x({__name:"RoleDist",setup(O,{expose:y}){const{$infoBox:c,$access:D}=B().appContext.config.globalProperties,n=S({hasEnt:D("ENT_UR_ROLE_DIST"),recordId:null,treeData:[],replaceFields:{key:"entId",title:"entName"},checkedKeys:[],allEntList:{}});function e(l){if(!n.hasEnt)return!1;n.checkedKeys=[],n.treeData=[],n.allEntList={},n.recordId=l,V().then(s=>{n.treeData=s,p(s,t=>{n.allEntList[t.entId]={pid:t.pid,children:t.children||[]}}),k.list(q,{roleId:l||"NONE",pageSize:-1}).then(t=>{const a=[];t.records.map(o=>{n.allEntList[o.entId]&&n.allEntList[o.entId].children.length<=0&&a.push(o.entId)}),n.checkedKeys=a})})}function i(){if(!n.hasEnt)return!1;const l=[];return n.checkedKeys.map(s=>{const t=[];_(s,t),t.map(a=>{l.indexOf(a)<0&&l.push(a)})}),l}function p(l,s){for(let t=0;t<l.length;t++){const a=l[t];a.children&&a.children.length>0&&p(a.children,s),s(a)}}function _(l,s){n.allEntList[l]&&l!=="ROOT"&&(s.push(l),_(n.allEntList[l].pid,s))}return y({initTree:e,getSelectedEntIdList:i}),(l,s)=>{const t=M;return d(),I("div",X,[n.hasEnt?(d(),I("p",Y,"\u8BF7\u9009\u62E9\u6743\u9650\uFF1A")):E("",!0),r(t,{"tree-data":n.treeData,fieldNames:n.replaceFields,checkedKeys:n.checkedKeys,"onUpdate:checkedKeys":s[0]||(s[0]=a=>n.checkedKeys=a),checkable:!0},null,8,["tree-data","fieldNames","checkedKeys"])])}}}),ee={class:"drawer-btn-center"},te=x({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>{}}},setup(O,{expose:y}){const c=O,{$infoBox:D,$access:n}=B().appContext.config.globalProperties,e=S({confirmLoading:!1,isAdd:!0,isShow:!1,saveObject:{},recordId:null,rules:{roleName:[{required:!0,message:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0",trigger:"blur"}]}}),i=w(),p=w();function _(s){e.isAdd=!s,e.saveObject={},e.confirmLoading=!1,i.value&&i.value.resetFields(),e.isAdd||(e.recordId=s,k.getById(F,s).then(t=>{e.saveObject=t})),e.isShow=!0,z(()=>p.value.initTree(s))}function l(){i.value.validate().then(s=>{if(s){e.confirmLoading=!0;const t=p.value.getSelectedEntIdList();e.saveObject.entIdListStr=t?JSON.stringify(t):"",e.isAdd?k.add(F,e.saveObject).then(a=>{D.message.success("\u65B0\u589E\u6210\u529F"),e.isShow=!1,c.callbackFunc()}).catch(a=>{e.confirmLoading=!1}):k.updateById(F,e.recordId,e.saveObject).then(a=>{D.message.success("\u4FEE\u6539\u6210\u529F"),e.isShow=!1,c.callbackFunc()}).catch(a=>{e.confirmLoading=!1})}})}return y({show:_}),(s,t)=>{const a=H,o=J,v=A,f=$,R=G;return d(),b(R,{open:e.isShow,"onUpdate:open":t[2]||(t[2]=m=>e.isShow=m),title:e.isAdd?"\u65B0\u589E\u89D2\u8272":"\u4FEE\u6539\u89D2\u8272",width:"30%",maskClosable:!1,onClose:t[3]||(t[3]=m=>e.isShow=!1)},{default:u(()=>[r(v,{ref_key:"infoFormModel",ref:i,model:e.saveObject,"label-col":{span:4},"wrapper-col":{span:15},rules:e.rules},{default:u(()=>[r(o,{label:"\u89D2\u8272\u540D\u79F0\uFF1A",name:"roleName"},{default:u(()=>[r(a,{value:e.saveObject.roleName,"onUpdate:value":t[0]||(t[0]=m=>e.saveObject.roleName=m),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1},8,["model","rules"]),r(Z,{ref_key:"roleDistRef",ref:p},null,512),N("div",ee,[r(f,{style:{marginRight:"8px"},onClick:t[1]||(t[1]=m=>e.isShow=!1)},{default:u(()=>t[4]||(t[4]=[h("\u53D6\u6D88")])),_:1}),r(f,{type:"primary",onClick:l,loading:e.confirmLoading},{default:u(()=>t[5]||(t[5]=[h("\u4FDD\u5B58")])),_:1},8,["loading"])])]),_:1},8,["open","title"])}}}),ne={key:0,class:"table-page-search-wrapper"},ae={class:"table-layer"},oe={class:"table-page-search-submitButtons"},se={key:0},re=x({__name:"RolePage",setup(O){const{$infoBox:y,$access:c}=B().appContext.config.globalProperties,n=S({tableColumns:[{width:"40%",key:"roleId",title:"\u89D2\u8272ID",sorter:!0,scopedSlots:{customRender:"roleIdSlot"}},{width:"40%",key:"roleName",title:"\u89D2\u8272\u540D\u79F0",dataIndex:"roleName",sorter:!0},{key:"op",title:"\u64CD\u4F5C",width:"200px",align:"center",scopedSlots:{customRender:"opSlot"}}],searchData:{},btnLoading:!1}),e=w(),i=w();function p(a){return k.list(F,a)}function _(){n.btnLoading=!0,e.value.refTable(!0)}function l(){i.value.show()}function s(a){i.value.show(a)}function t(a){y.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>k.delById(F,a).then(o=>{y.message.success("\u5220\u9664\u6210\u529F\uFF01"),e.value.refTable(!1)}))}return(a,o)=>{const v=C("cloudpay-text-up"),f=$,R=A,m=C("cloudpayTableColumns"),U=C("cloudpayTable"),j=W,K=C("page-header-wrapper");return d(),b(K,null,{default:u(()=>[r(j,null,{default:u(()=>[L(c)("ENT_UR_ROLE_SEARCH")?(d(),I("div",ne,[r(R,{layout:"inline",class:"table-head-ground"},{default:u(()=>[N("div",ae,[r(v,{placeholder:"\u89D2\u8272ID",value:n.searchData.roleId,"onUpdate:value":o[0]||(o[0]=g=>n.searchData.roleId=g)},null,8,["value"]),r(v,{placeholder:"\u89D2\u8272\u540D\u79F0",value:n.searchData.roleName,"onUpdate:value":o[1]||(o[1]=g=>n.searchData.roleName=g)},null,8,["value"]),N("span",oe,[r(f,{type:"primary",onClick:_,loading:n.btnLoading},{default:u(()=>o[4]||(o[4]=[h(" \u67E5\u8BE2 ")])),_:1},8,["loading"]),r(f,{style:{"margin-left":"8px"},onClick:o[2]||(o[2]=()=>n.searchData={})},{default:u(()=>o[5]||(o[5]=[h(" \u91CD\u7F6E ")])),_:1})])])]),_:1})])):E("",!0),r(U,{ref_key:"infoTable",ref:e,initData:!0,reqTableDataFunc:p,tableColumns:n.tableColumns,searchData:n.searchData,onBtnLoadClose:o[3]||(o[3]=g=>n.btnLoading=!1),rowKey:"roleId"},{opRow:u(()=>[L(c)("ENT_UR_ROLE_ADD")?(d(),b(f,{key:0,type:"primary",onClick:l},{default:u(()=>o[6]||(o[6]=[h(" \u65B0\u5EFA ")])),_:1})):E("",!0)]),bodyCell:u(({column:g,record:T})=>[g.key=="roleId"?(d(),I("b",se,Q(T.roleId),1)):E("",!0),g.key=="op"?(d(),b(m,{key:1},{default:u(()=>[L(c)("ENT_UR_ROLE_EDIT")?(d(),b(f,{key:0,type:"link",onClick:P=>s(T.roleId)},{default:u(()=>o[7]||(o[7]=[h(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])):E("",!0),L(c)("ENT_UR_ROLE_DEL")?(d(),b(f,{key:1,type:"link",danger:"",onClick:P=>t(T.roleId)},{default:u(()=>o[8]||(o[8]=[h(" \u5220\u9664 ")])),_:2},1032,["onClick"])):E("",!0)]),_:2},1024)):E("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),r(te,{ref_key:"infoAddOrEdit",ref:i,callbackFunc:_},null,512)]),_:1})}}});export{re as default};
