<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100" height="127" viewBox="0 0 100 127">
  <defs>
    <clipPath id="clip-path">
      <rect id="SVGID" width="100" height="127" fill="none"/>
    </clipPath>
  </defs>
  <g id="组_1021" data-name="组 1021" transform="translate(-877.908 -86.051)">
    <g id="组_1020" data-name="组 1020" transform="translate(877.908 86.051)" clip-path="url(#clip-path)">
      <rect id="矩形_2605" data-name="矩形 2605" width="99.744" height="48.028" fill="#eee"/>
      <g id="组_1017" data-name="组 1017" transform="translate(7.74 8.423)">
        <rect id="矩形_2606" data-name="矩形 2606" width="4.369" height="31.182" transform="translate(25.558)" fill="#313133"/>
        <rect id="矩形_2607" data-name="矩形 2607" width="2.966" height="31.182" transform="translate(38.139)" fill="#313133"/>
        <rect id="矩形_2608" data-name="矩形 2608" width="3.962" height="31.182" transform="translate(51.021)" fill="#313133"/>
        <rect id="矩形_2609" data-name="矩形 2609" width="3.244" height="31.182" transform="translate(44.812)" fill="#313133"/>
        <rect id="矩形_2610" data-name="矩形 2610" width="2.911" height="31.182" transform="translate(33.003)" fill="#313133"/>
        <rect id="矩形_2611" data-name="矩形 2611" width="3.274" height="31.182" transform="translate(13.322)" fill="#313133"/>
        <rect id="矩形_2612" data-name="矩形 2612" width="3.93" height="31.182" transform="translate(18.647)" fill="#313133"/>
        <rect id="矩形_2613" data-name="矩形 2613" width="3.337" height="31.182" transform="translate(7.761)" fill="#313133"/>
        <rect id="矩形_2614" data-name="矩形 2614" width="4.795" height="31.182" fill="#313133"/>
        <rect id="矩形_2615" data-name="矩形 2615" width="3.683" height="31.182" transform="translate(74.157)" fill="#313133"/>
        <rect id="矩形_2616" data-name="矩形 2616" width="4.646" height="31.182" transform="translate(79.618)" fill="#313133"/>
        <rect id="矩形_2617" data-name="矩形 2617" width="2.718" height="31.182" transform="translate(67.731)" fill="#313133"/>
        <rect id="矩形_2618" data-name="矩形 2618" width="2.966" height="31.182" transform="translate(62.546)" fill="#313133"/>
        <rect id="矩形_2619" data-name="矩形 2619" width="3.436" height="31.182" transform="translate(56.762)" fill="#313133"/>
      </g>
      <g id="组_1019" data-name="组 1019" transform="translate(-0.267 30.926)">
        <image id="矩形_2620" data-name="矩形 2620" width="100.303" height="45.176" xlink:href="data:image/png;base64,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"/>
        <g id="组_1018" data-name="组 1018" transform="translate(32.131 44.584)">
          <path id="矩形_2621" data-name="矩形 2621" d="M0,0H36.016a0,0,0,0,1,0,0V29.29A16.014,16.014,0,0,1,20,45.3H16.014A16.014,16.014,0,0,1,0,29.29V0A0,0,0,0,1,0,0Z" transform="translate(0 6.186)" fill="#0063b0"/>
          <rect id="矩形_2622" data-name="矩形 2622" width="28.119" height="6.186" transform="translate(3.948)" fill="#313133"/>
          <rect id="矩形_2623" data-name="矩形 2623" width="23.12" height="7.511" transform="translate(6.448 12.077)" fill="#fff"/>
          <path id="路径_4175" data-name="路径 4175" d="M955.437,273.217a2.206,2.206,0,1,1-2.205-2.209A2.207,2.207,0,0,1,955.437,273.217Z" transform="translate(-942.997 -245.438)" fill="#313133"/>
          <path id="路径_4176" data-name="路径 4176" d="M969.786,273.217a2.206,2.206,0,1,1-2.205-2.209A2.208,2.208,0,0,1,969.786,273.217Z" transform="translate(-949.517 -245.438)" fill="#313133"/>
          <path id="路径_4177" data-name="路径 4177" d="M984.135,273.217a2.206,2.206,0,1,1-2.205-2.209A2.208,2.208,0,0,1,984.135,273.217Z" transform="translate(-956.038 -245.438)" fill="#313133"/>
        </g>
      </g>
    </g>
  </g>
</svg>
