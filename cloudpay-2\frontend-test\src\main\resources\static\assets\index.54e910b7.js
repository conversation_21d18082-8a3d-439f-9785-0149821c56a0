import{a5 as E,a6 as R,bt as O,bu as j,M as B,a7 as X,d as z,u as F,H as S,J as H,b as d,e as b,D as J,L as U,bv as V,bw as q,aJ as G,bx as K,V as P,by as Q,ak as Y}from"./index.8746381c.js";const v=(e,t,l)=>{const a=O(l);return{[`${e.componentCls}-${t}`]:{color:e[`color${l}`],background:e[`color${a}Bg`],borderColor:e[`color${a}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},Z=e=>j(e,(t,l)=>{let{textColor:a,lightBorderColor:r,lightColor:o,darkColor:c}=l;return{[`${e.componentCls}-${t}`]:{color:a,background:o,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}}),ee=e=>{const{paddingXXS:t,lineWidth:l,tagPaddingHorizontal:a,componentCls:r}=e,o=a-l,c=t-l;return{[r]:B(B({},X(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:o,fontSize:e.tagFontSize,lineHeight:`${e.tagLineHeight}px`,whiteSpace:"nowrap",background:e.tagDefaultBg,border:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.tagDefaultColor},[`${r}-close-icon`]:{marginInlineStart:c,color:e.colorTextDescription,fontSize:e.tagIconSize,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},["&-checkable"]:{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},["&-hidden"]:{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:o}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}};var _=E("Tag",e=>{const{fontSize:t,lineHeight:l,lineWidth:a,fontSizeIcon:r}=e,o=Math.round(t*l),c=e.fontSizeSM,g=o-a*2,C=e.colorFillAlter,i=e.colorText,n=R(e,{tagFontSize:c,tagLineHeight:g,tagDefaultBg:C,tagDefaultColor:i,tagIconSize:r-2*a,tagPaddingHorizontal:8,tagBorderlessBg:e.colorFillTertiary});return[ee(n),Z(n),v(n,"success","Success"),v(n,"processing","Info"),v(n,"error","Error"),v(n,"warning","Warning")]});const oe=()=>({prefixCls:String,checked:{type:Boolean,default:void 0},onChange:{type:Function},onClick:{type:Function},"onUpdate:checked":Function}),le=z({compatConfig:{MODE:3},name:"ACheckableTag",inheritAttrs:!1,props:oe(),setup(e,t){let{slots:l,emit:a,attrs:r}=t;const{prefixCls:o}=F("tag",e),[c,g]=_(o),C=n=>{const{checked:u}=e;a("update:checked",!u),a("change",!u),a("click",n)},i=S(()=>H(o.value,g.value,{[`${o.value}-checkable`]:!0,[`${o.value}-checkable-checked`]:e.checked}));return()=>{var n;return c(d("span",b(b({},r),{},{class:[i.value,r.class],onClick:C}),[(n=l.default)===null||n===void 0?void 0:n.call(l)]))}}});var m=le;const ae=()=>({prefixCls:String,color:{type:String},closable:{type:Boolean,default:!1},closeIcon:P.any,visible:{type:Boolean,default:void 0},onClose:{type:Function},onClick:Q(),"onUpdate:visible":Function,icon:P.any,bordered:{type:Boolean,default:!0}}),h=z({compatConfig:{MODE:3},name:"ATag",inheritAttrs:!1,props:ae(),slots:Object,setup(e,t){let{slots:l,emit:a,attrs:r}=t;const{prefixCls:o,direction:c}=F("tag",e),[g,C]=_(o),i=J(!0);U(()=>{e.visible!==void 0&&(i.value=e.visible)});const n=s=>{s.stopPropagation(),a("update:visible",!1),a("close",s),!s.defaultPrevented&&e.visible===void 0&&(i.value=!1)},u=S(()=>V(e.color)||q(e.color)),D=S(()=>H(o.value,C.value,{[`${o.value}-${e.color}`]:u.value,[`${o.value}-has-color`]:e.color&&!u.value,[`${o.value}-hidden`]:!i.value,[`${o.value}-rtl`]:c.value==="rtl",[`${o.value}-borderless`]:!e.bordered})),M=s=>{a("click",s)};return()=>{var s,p,f;const{icon:k=(s=l.icon)===null||s===void 0?void 0:s.call(l),color:$,closeIcon:y=(p=l.closeIcon)===null||p===void 0?void 0:p.call(l),closable:w=!1}=e,A=()=>w?y?d("span",{class:`${o.value}-close-icon`,onClick:n},[y]):d(Y,{class:`${o.value}-close-icon`,onClick:n},null):null,L={backgroundColor:$&&!u.value?$:void 0},T=k||null,x=(f=l.default)===null||f===void 0?void 0:f.call(l),N=T?d(G,null,[T,d("span",null,[x])]):x,W=e.onClick!==void 0,I=d("span",b(b({},r),{},{onClick:M,class:[D.value,r.class],style:[L,r.style]}),[N,A()]);return g(W?d(K,null,{default:()=>[I]}):I)}}});h.CheckableTag=m;h.install=function(e){return e.component(h.name,h),e.component(m.name,m),e};var ne=h;export{ne as _};
