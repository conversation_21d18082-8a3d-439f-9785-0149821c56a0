import{r as w,L as S,M as W}from"./manage.2dfb5a24.js";import{e as U,r as $,D as J,o as p,C as m,w as u,b as e,d as l,t as i,E as f,Q as K,l as Q,U as P,n as z,W as X,a2 as Z,j as uu,V as eu,g as au,f as N,A as tu,ac as lu,a as x,c as R,J as nu,M as T,N as ou,m as v,ad as du,B as su,F as iu,$ as ru}from"./index.fba97cfa.js";const Du=U({__name:"Detail",setup(V,{expose:E}){const t=$({visible:!1,detailData:{}});function O(s){w.getById(S,s).then(r=>{t.detailData=r}),t.visible=!0}return E({show:O}),(s,r)=>{const n=J,d=K,o=Q,F=P,I=z,k=X,g=Z,D=uu,a=eu;return p(),m(a,{width:"50%",closable:!0,open:t.visible,"onUpdate:open":r[1]||(r[1]=y=>t.visible=y),title:"\u8BB0\u5F55\u8BE6\u60C5",onClose:r[2]||(r[2]=y=>t.visible=!1)},{default:u(()=>[e(I,{justify:"space-between",type:"flex"},{default:u(()=>[e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u8BB0\u5F55ID"},{default:u(()=>[l(i(t.detailData.recordId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5546\u6237\u53F7"},{default:u(()=>[l(i(t.detailData.mchNo),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5E94\u7528ID"},{default:u(()=>[l(i(t.detailData.appId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u652F\u4ED8\u63A5\u53E3\u4EE3\u7801"},{default:u(()=>[l(i(t.detailData.ifCode),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u7CFB\u7EDF\u652F\u4ED8\u8BA2\u5355\u53F7"},{default:u(()=>[l(i(t.detailData.payOrderId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u652F\u4ED8\u8BA2\u5355\u6E20\u9053\u652F\u4ED8\u8BA2\u5355\u53F7"},{default:u(()=>[l(i(t.detailData.payOrderChannelOrderNo),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u8BA2\u5355\u91D1\u989D"},{default:u(()=>[l(i(t.detailData.payOrderAmount/100),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u57FA\u6570"},{default:u(()=>[l(i(t.detailData.payOrderDivisionAmount/100)+" \uFF08\u8BA2\u5355\u91D1\u989D-\u624B\u7EED\u8D39-\u9000\u6B3E\u91D1\u989D\uFF09 ",1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u7CFB\u7EDF\u5206\u8D26\u6279\u6B21\u53F7"},{default:u(()=>[l(i(t.detailData.batchOrderId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u4E0A\u6E38\u5206\u8D26\u6279\u6B21\u53F7"},{default:u(()=>[l(i(t.detailData.channelBatchOrderId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u72B6\u6001"},{default:u(()=>[t.detailData.state===0?(p(),m(F,{key:t.detailData.state,color:"orange"},{default:u(()=>r[3]||(r[3]=[l(" \u5206\u8D26\u4E2D ")])),_:1})):f("",!0),t.detailData.state===1?(p(),m(F,{key:t.detailData.state,color:"blue"},{default:u(()=>r[4]||(r[4]=[l(" \u5206\u8D26\u6210\u529F ")])),_:1})):f("",!0),t.detailData.state===2?(p(),m(F,{key:t.detailData.state,color:"volcano"},{default:u(()=>r[5]||(r[5]=[l(" \u5206\u8D26\u5931\u8D25 ")])),_:1})):f("",!0),t.detailData.state===3?(p(),m(F,{key:t.detailData.state,color:"purple"},{default:u(()=>r[6]||(r[6]=[l(" \u5DF2\u53D7\u7406 ")])),_:1})):f("",!0)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u63A5\u6536\u8005ID"},{default:u(()=>[l(i(t.detailData.receiverId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u6536\u6B3E\u8D26\u53F7\u7EC4ID"},{default:u(()=>[l(i(t.detailData.receiverGroupId),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u6536\u6B3E\u8D26\u53F7\u522B\u540D"},{default:u(()=>[l(i(t.detailData.receiverAlias),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u63A5\u6536\u8D26\u53F7\u7C7B\u578B"},{default:u(()=>[l(i(t.detailData.accType==0?"\u4E2A\u4EBA":"\u5546\u6237"),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u63A5\u6536\u8D26\u53F7"},{default:u(()=>[l(i(t.detailData.accNo),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u63A5\u6536\u8D26\u53F7\u540D\u79F0"},{default:u(()=>[l(i(t.detailData.accName),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u5173\u7CFB\u7C7B\u578B"},{default:u(()=>[l(i(t.detailData.relationType),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u5173\u7CFB\u7C7B\u578B\u540D\u79F0"},{default:u(()=>[l(i(t.detailData.relationTypeName),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5B9E\u9645\u5206\u8D26\u6BD4\u4F8B"},{default:u(()=>[l(i((t.detailData.divisionProfit*100).toFixed(2))+"% ",1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u5206\u8D26\u91D1\u989D"},{default:u(()=>[l(i(t.detailData.calDivisionAmount/100),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:u(()=>[l(i(t.detailData.createdAt),1)]),_:1})]),_:1})]),_:1}),e(o,{sm:12},{default:u(()=>[e(d,null,{default:u(()=>[e(n,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:u(()=>[l(i(t.detailData.updatedAt),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(k),e(I,{justify:"start",type:"flex"},{default:u(()=>[e(o,{sm:24},{default:u(()=>[e(D,{label:"\u4E0A\u6E38\u8FD4\u56DE\u6570\u636E\u5305"},{default:u(()=>[e(g,{disabled:"disabled",style:{height:"100px",color:"black"},value:t.detailData.channelRespResult,"onUpdate:value":r[0]||(r[0]=y=>t.detailData.channelRespResult=y)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["open"])}}}),_u={class:"table-page-search-wrapper"},pu={class:"table-layer"},fu={class:"table-page-search-submitButtons"},mu={key:0},yu=U({__name:"DivisionRecordPage",setup(V){const{$infoBox:E,$access:t}=au().appContext.config.globalProperties,s=$({btnLoading:!1,tableColumns:[{key:"calDivisionAmount",title:"\u5206\u8D26\u91D1\u989D",scopedSlots:{customRender:"amountSlot"}},{key:"batchOrderId",title:"\u5206\u8D26\u6279\u6B21\u53F7",dataIndex:"batchOrderId"},{key:"payOrderId",title:"\u652F\u4ED8\u8BA2\u5355\u53F7",dataIndex:"payOrderId"},{key:"ifCode",title:"\u63A5\u53E3\u4EE3\u7801",dataIndex:"ifCode"},{key:"payOrderAmount",dataIndex:"payOrderAmount",title:"\u8BA2\u5355\u91D1\u989D",customRender:({text:D})=>"\uFFE5"+(D/100).toFixed(2)},{key:"payOrderDivisionAmount",dataIndex:"payOrderDivisionAmount",title:"\u5206\u8D26\u57FA\u6570",customRender:({text:D})=>"\uFFE5"+(D/100).toFixed(2)},{key:"receiverAlias",title:"\u8D26\u53F7\u522B\u540D",dataIndex:"receiverAlias"},{key:"accNo",title:"\u63A5\u6536\u8D26\u53F7",dataIndex:"accNo"},{key:"accName",title:"\u8D26\u53F7\u59D3\u540D",dataIndex:"accName"},{key:"relationTypeName",title:"\u5206\u8D26\u5173\u7CFB\u7C7B\u578B",dataIndex:"relationTypeName"},{key:"divisionProfit",dataIndex:"divisionProfit",title:"\u5206\u8D26\u6BD4\u4F8B",customRender:({text:D})=>(D*100).toFixed(2)+"%"},{key:"state",title:"\u5206\u8D26\u72B6\u6001",scopedSlots:{customRender:"stateSlot"}},{key:"createdAt",dataIndex:"createdAt",title:"\u521B\u5EFA\u65E5\u671F"},{key:"op",title:"\u64CD\u4F5C",width:"100px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],searchData:{},createdStart:"",createdEnd:""}),r=N(),n=N();function d(){s.btnLoading=!0,r.value.refTable(!0)}function o(D){return w.list(S,D)}function F(D){n.value.show(D)}function I(D){E.confirmPrimary("\u786E\u8BA4\u91CD\u65B0\u5206\u8D26?","\u91CD\u65B0\u5206\u8D26\u5C06\u6309\u7167\u8BA2\u5355\u7EF4\u5EA6\u91CD\u65B0\u53D1\u8D77\uFF08\u4EC5\u9650\u5206\u8D26\u5931\u8D25\u8BA2\u5355\uFF09\u3002",()=>{W(D).then(a=>{r.value.refTable(!1),E.message.warning("\u8BF7\u7B49\u5F85\u63A5\u53E3\u6700\u65B0\u72B6\u6001")})})}function k(D,a){s.searchData.createdStart=a[0],s.searchData.createdEnd=a[1]}function g(D){return D&&D>ou().endOf("day")}return(D,a)=>{const y=v("a-icon"),L=tu,b=v("cloudpay-text-up"),B=lu,Y=du,C=su,j=iu,A=P,H=v("cloudpayTableColumns"),M=v("cloudpayTable"),h=ru,q=v("page-header-wrapper");return p(),m(q,null,{default:u(()=>[e(h,null,{default:u(()=>[x("div",_u,[e(j,{layout:"inline",class:"table-head-ground"},{default:u(()=>[x("div",pu,[e(L,{class:"table-head-layout",onChange:k,"show-time":{format:"HH:mm:ss"},format:"YYYY-MM-DD HH:mm:ss","disabled-date":g},{default:u(()=>[e(y,{slot:"suffixIcon",type:"sync"})]),_:1}),e(b,{placeholder:"\u5206\u8D26\u63A5\u53D7\u8005ID",value:s.searchData.receiverId,"onUpdate:value":a[0]||(a[0]=_=>s.searchData.receiverId=_)},null,8,["value"]),e(b,{placeholder:"\u5206\u8D26\u8D26\u53F7\u7EC4ID",value:s.searchData.receiverGroupId,"onUpdate:value":a[1]||(a[1]=_=>s.searchData.receiverGroupId=_)},null,8,["value"]),e(b,{placeholder:"\u5E94\u7528AppId",value:s.searchData.appId,"onUpdate:value":a[2]||(a[2]=_=>s.searchData.appId=_)},null,8,["value"]),e(b,{placeholder:"\u652F\u4ED8\u8BA2\u5355\u53F7",value:s.searchData.payOrderId,"onUpdate:value":a[3]||(a[3]=_=>s.searchData.payOrderId=_)},null,8,["value"]),e(b,{placeholder:"\u5206\u8D26\u63A5\u6536\u8D26\u53F7",value:s.searchData.accNo,"onUpdate:value":a[4]||(a[4]=_=>s.searchData.accNo=_)},null,8,["value"]),e(Y,{value:s.searchData.state,"onUpdate:value":a[5]||(a[5]=_=>s.searchData.state=_),placeholder:"\u5206\u8D26\u72B6\u6001",class:"table-head-layout"},{default:u(()=>[e(B,{value:""},{default:u(()=>a[8]||(a[8]=[l("\u5168\u90E8")])),_:1}),e(B,{value:"0"},{default:u(()=>a[9]||(a[9]=[l("\u5F85\u5206\u8D26")])),_:1}),e(B,{value:"1"},{default:u(()=>a[10]||(a[10]=[l("\u5206\u8D26\u6210\u529F")])),_:1}),e(B,{value:"2"},{default:u(()=>a[11]||(a[11]=[l("\u5206\u8D26\u5931\u8D25")])),_:1}),e(B,{value:"3"},{default:u(()=>a[12]||(a[12]=[l("\u5DF2\u53D7\u7406")])),_:1})]),_:1},8,["value"]),x("span",fu,[e(C,{type:"primary",onClick:d,loading:s.btnLoading},{default:u(()=>a[13]||(a[13]=[l(" \u641C\u7D22 ")])),_:1},8,["loading"]),e(C,{style:{"margin-left":"8px"},onClick:a[6]||(a[6]=()=>s.searchData={})},{default:u(()=>a[14]||(a[14]=[l(" \u91CD\u7F6E ")])),_:1})])])]),_:1})]),e(M,{onBtnLoadClose:a[7]||(a[7]=_=>s.btnLoading=!1),ref_key:"infoTable",ref:r,initData:!0,reqTableDataFunc:o,tableColumns:s.tableColumns,searchData:s.searchData,rowKey:"recordId"},{bodyCell:u(({column:_,record:c})=>[_.key==="calDivisionAmount"?(p(),R("b",mu,"\uFFE5"+i(c.calDivisionAmount/100),1)):f("",!0),_.key==="state"?(p(),R(nu,{key:1},[c.state===0?(p(),m(A,{key:c.state,color:"orange"},{default:u(()=>a[15]||(a[15]=[l("\u5206\u8D26\u4E2D")])),_:2},1024)):f("",!0),c.state===1?(p(),m(A,{key:c.state,color:"blue"},{default:u(()=>a[16]||(a[16]=[l("\u5206\u8D26\u6210\u529F")])),_:2},1024)):f("",!0),c.state===2?(p(),m(A,{key:c.state,color:"volcano"},{default:u(()=>a[17]||(a[17]=[l("\u5206\u8D26\u5931\u8D25")])),_:2},1024)):f("",!0),c.state===3?(p(),m(A,{key:c.state,color:"purple"},{default:u(()=>a[18]||(a[18]=[l("\u5DF2\u53D7\u7406")])),_:2},1024)):f("",!0)],64)):f("",!0),_.key==="op"?(p(),m(H,{key:2},{default:u(()=>[T(t)("ENT_DIVISION_RECORD_VIEW")?(p(),m(C,{key:0,type:"link",onClick:G=>F(c.recordId)},{default:u(()=>a[19]||(a[19]=[l(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])):f("",!0),c.state==2&&T(t)("ENT_DIVISION_RECORD_RESEND")?(p(),m(C,{key:1,type:"link",onClick:G=>I(c.recordId)},{default:u(()=>a[20]||(a[20]=[l(" \u91CD\u8BD5 ")])),_:2},1032,["onClick"])):f("",!0)]),_:2},1024)):f("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),e(Du,{ref_key:"recordDetail",ref:n},null,512)]),_:1})}}});export{yu as default};
