import{K as B,r as C,L as D}from"./manage.6e729324.js";import{d as K,g as L,r as h,c as p,b as o,w as t,o as _,a as f,aJ as k,aK as I,j as V,m as w,B as N,F as T,aE as A,I as P}from"./index.8746381c.js";import{T as S,_ as U}from"./TabPane.9792ea88.js";import{a as j,_ as $}from"./index.7c25015e.js";import"./useMergedState.8a9045a6.js";const q={style:{background:"#fff",padding:"0 20px"}},G={class:"account-settings-info-view"},J={style:{display:"flex","justify-content":"center"}},W=K({__name:"SysConfig",setup(M){const{$infoBox:i,$access:O}=L().appContext.config.globalProperties,n=h({btnLoading:!1,configData:{},groupKey:"applicationConfig"});function r(){B(n.groupKey).then(a=>{n.configData=a,n.groupKey=a[0].groupKey})}r();function l(a){a&&(n.groupKey=a,r())}function g(a){i.confirmPrimary("\u786E\u8BA4\u4FEE\u6539\u5E94\u7528\u914D\u7F6E\u5417\uFF1F","",()=>{n.btnLoading=!0;const e=new FormData;for(var s in n.configData)e.append(n.configData[s].configKey,n.configData[s].configVal);C.updateById(D,n.groupKey,e).then(u=>{i.message.success("\u4FEE\u6539\u6210\u529F")}).finally(u=>{n.btnLoading=!1})})}return(a,e)=>{const s=P,u=w,d=$,m=j,y=N,b=T,F=U,E=S;return _(),p("div",q,[o(E,{onChange:l},{default:t(()=>[o(F,{key:"applicationConfig",tab:"\u5E94\u7528\u914D\u7F6E"},{default:t(()=>[f("div",G,[o(b,{ref:"configFormModel"},{default:t(()=>[o(m,null,{default:t(()=>[(_(!0),p(k,null,I(n.configData,(c,v)=>(_(),A(d,{span:8,offset:1,key:v},{default:t(()=>[o(u,{label:c.configName},{default:t(()=>[o(s,{type:c.type==="text"?"text":"textarea",value:c.configVal,"onUpdate:value":x=>c.configVal=x,autocomplete:"off"},null,8,["type","value","onUpdate:value"])]),_:2},1032,["label"])]),_:2},1024))),128))]),_:1}),f("div",J,[o(u,null,{default:t(()=>[o(y,{type:"primary",onClick:g,loading:n.btnLoading},{default:t(()=>e[0]||(e[0]=[V(" \u786E\u8BA4\u66F4\u65B0 ")])),_:1,__:[0]},8,["loading"])]),_:1})])]),_:1},512)])]),_:1})]),_:1})])}}});export{W as default};
