import{r as N,H as U}from"./manage.2dfb5a24.js";import{e as T,r as H,D as q,o as c,C as b,w as t,b as e,d as l,t as n,a as p,E as g,Q as W,l as J,U as R,W as K,n as z,a2 as G,j as X,F as S,V as Z,_ as ee,g as te,f as O,A as ae,ac as ue,c as B,M as le,N as ne,m as I,ad as oe,B as se,O as de,$ as re}from"./index.fba97cfa.js";const _e={style:{width:"100%","text-align":"center"}},ie=["src"],fe=T({__name:"TransferOrderDetail",setup(L,{expose:k}){const a=H({detailData:{},userH5ConfirmUrl:null,userH5ConfirmQrImgUrl:null,isShow:!1,recordId:null});function _(E){a.userH5ConfirmUrl=null,a.userH5ConfirmQrImgUrl=null,N.getById(U,E).then(i=>{if(a.detailData=i,i.channelResData&&i.ifCode==="wxpay"){let o=JSON.parse(i.channelResData);o&&o.userH5ConfirmQrImgUrl&&(a.userH5ConfirmUrl=o.userH5ConfirmUrl,a.userH5ConfirmQrImgUrl=o.userH5ConfirmQrImgUrl)}}),a.isShow=!0}return k({show:_}),(E,i)=>{const o=q,r=W,s=J,C=R,F=K,h=z,f=G,u=X,y=S,A=Z;return c(),b(A,{width:"50%",placement:"right",closable:!0,open:a.isShow,"onUpdate:open":i[2]||(i[2]=D=>a.isShow=D),title:"\u8F6C\u8D26\u8BA2\u5355\u8BE6\u60C5",onClose:i[3]||(i[3]=D=>a.isShow=!1)},{default:t(()=>[e(h,{justify:"space-between",type:"flex"},{default:t(()=>[e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u5E94\u7528APPID"},{default:t(()=>[l(n(a.detailData.appId),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u8F6C\u8D26\u8BA2\u5355\u53F7"},{default:t(()=>[e(C,{color:"purple"},{default:t(()=>[l(n(a.detailData.transferId),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u5546\u6237\u8F6C\u8D26\u5355\u53F7"},{default:t(()=>[l(n(a.detailData.mchOrderNo),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u6E20\u9053\u8BA2\u5355\u53F7"},{default:t(()=>[l(n(a.detailData.channelOrderNo),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u91D1\u989D"},{default:t(()=>[e(C,{color:"green"},{default:t(()=>[l(n(a.detailData.amount/100),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u8D27\u5E01\u4EE3\u7801"},{default:t(()=>[l(n(a.detailData.currency),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u6536\u6B3E\u8D26\u53F7"},{default:t(()=>[e(C,{color:"green"},{default:t(()=>[l(n(a.detailData.accountNo),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u6536\u6B3E\u4EBA\u59D3\u540D"},{default:t(()=>[l(n(a.detailData.accountName),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u8F6C\u8D26\u5907\u6CE8"},{default:t(()=>[l(n(a.detailData.transferDesc),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u8BA2\u5355\u72B6\u6001"},{default:t(()=>[e(C,{color:a.detailData.state===0?"blue":a.detailData.state===1?"orange":a.detailData.state===2?"green":"volcano"},{default:t(()=>[l(n(a.detailData.state===0?"\u8BA2\u5355\u751F\u6210":a.detailData.state===1?"\u8F6C\u8D26\u4E2D":a.detailData.state===2?"\u8F6C\u8D26\u6210\u529F":a.detailData.state===3?"\u8F6C\u8D26\u5931\u8D25":a.detailData.state===4?"\u4EFB\u52A1\u5173\u95ED":"\u672A\u77E5"),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u8F6C\u8D26\u6210\u529F\u65F6\u95F4"},{default:t(()=>[l(n(a.detailData.successTime),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:t(()=>[l(n(a.detailData.createdAt),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:t(()=>[l(n(a.detailData.updatedAt),1)]),_:1})]),_:1})]),_:1}),e(F),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u63A5\u53E3\u4EE3\u7801"},{default:t(()=>[l(n(a.detailData.ifCode),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u5165\u8D26\u7C7B\u578B"},{default:t(()=>[l(n(a.detailData.entryType),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u5BA2\u6237\u7AEFIP"},{default:t(()=>[l(n(a.detailData.clientIp),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:24},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u5F02\u6B65\u901A\u77E5\u5730\u5740"},{default:t(()=>[l(n(a.detailData.notifyUrl),1)]),_:1})]),_:1})]),_:1})]),_:1}),a.userH5ConfirmUrl&&a.userH5ConfirmQrImgUrl?(c(),b(h,{key:0,justify:"space-between",type:"flex"},{default:t(()=>[e(F),e(s,{sm:24},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u9886\u53D6\u94FE\u63A5"},{default:t(()=>[l(n(a.userH5ConfirmUrl),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u9886\u53D6\u4E8C\u7EF4\u7801"},{default:t(()=>[p("div",_e,[p("img",{src:a.userH5ConfirmQrImgUrl,alt:""},null,8,ie),i[4]||(i[4]=p("p",null,"\u8BF7\u4F7F\u7528\u5FAE\u4FE1\u626B\u7801\u9886\u53D6",-1))])]),_:1})]),_:1})]),_:1})]),_:1})):g("",!0),e(F),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u6E20\u9053\u8BA2\u5355\u53F7"},{default:t(()=>[l(n(a.detailData.channelOrderNo),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u6E20\u9053\u9519\u8BEF\u7801"},{default:t(()=>[l(n(a.detailData.errCode),1)]),_:1})]),_:1})]),_:1}),e(s,{sm:12},{default:t(()=>[e(r,null,{default:t(()=>[e(o,{label:"\u6E20\u9053\u9519\u8BEF\u63CF\u8FF0"},{default:t(()=>[l(n(a.detailData.errMsg),1)]),_:1})]),_:1})]),_:1}),e(y,{layout:"vertical"},{default:t(()=>[e(s,{sm:24},{default:t(()=>[e(u,{label:"\u6E20\u9053\u989D\u5916\u53C2\u6570:"},{default:t(()=>[e(f,{disabled:"disabled",style:{height:"100px",color:"black"},value:a.detailData.channelExtra,"onUpdate:value":i[0]||(i[0]=D=>a.detailData.channelExtra=D)},null,8,["value"])]),_:1})]),_:1}),e(F),e(s,{sm:24},{default:t(()=>[e(u,{label:"\u6269\u5C55\u53C2\u6570:"},{default:t(()=>[e(f,{disabled:"disabled",style:{height:"100px",color:"black"},value:a.detailData.extParam,"onUpdate:value":i[1]||(i[1]=D=>a.detailData.extParam=D)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["open"])}}});const pe={class:"table-page-search-wrapper"},me={class:"table-layer"},ce={class:"table-page-search-submitButtons"},De={key:0},be={key:2,class:"order-list"},ge={style:{"margin-bottom":"0"}},Ce={slot:"title"},Fe={key:1,style:{"font-weight":"normal"}},ye={key:0,style:{"margin-bottom":"0","margin-top":"10px"}},Ee={slot:"title"},he={key:1,style:{"font-weight":"normal"}},ve=T({__name:"TransferOrderList",setup(L){const{$access:k}=te().appContext.config.globalProperties,_=H({btnLoading:!1,tableColumns:[{title:"\u8F6C\u8D26\u91D1\u989D",key:"amount"},{key:"orderNo",title:"\u8BA2\u5355\u53F7",scopedSlots:{customRender:"orderSlot"},width:260},{title:"\u6536\u6B3E\u8D26\u53F7",dataIndex:"accountNo",width:200},{title:"\u6536\u6B3E\u4EBA\u59D3\u540D",dataIndex:"accountName"},{title:"\u8F6C\u8D26\u5907\u6CE8",dataIndex:"transferDesc"},{title:"\u72B6\u6001",key:"state",width:100},{title:"\u521B\u5EFA\u65E5\u671F",dataIndex:"createdAt"},{title:"\u64CD\u4F5C",width:"100px",fixed:"right",align:"center",key:"op"}],searchData:{},createdStart:"",createdEnd:"",date:""}),E=O(),i=O();function o(){_.btnLoading=!0,E.value.refTable(!0)}function r(f){return N.list(U,f)}function s(f){console.log(i.value),i.value.show(f)}function C(f,u){_.searchData.createdStart=u[0],_.searchData.createdEnd=u[1]}function F(f){return f&&f>ne().endOf("day")}function h(f,u){const y=Math.floor(u/2);return f.substring(0,y-1)+"..."+f.substring(f.length-y,f.length)}return(f,u)=>{const y=I("a-icon"),A=ae,D=I("cloudpay-text-up"),v=ue,$=oe,x=se,P=S,Q=R,w=de,V=I("cloudpayTableColumns"),M=I("cloudpayTable"),j=re,Y=I("page-header-wrapper");return c(),b(Y,null,{default:t(()=>[e(j,null,{default:t(()=>[p("div",pe,[e(P,{layout:"inline",class:"table-head-ground"},{default:t(()=>[p("div",me,[e(A,{class:"table-head-layout",onChange:C,value:_.date,"onUpdate:value":u[0]||(u[0]=m=>_.date=m),"show-time":{format:"HH:mm:ss"},format:"YYYY-MM-DD HH:mm:ss","disabled-date":F},{default:t(()=>[e(y,{slot:"suffixIcon",type:"sync"})]),_:1},8,["value"]),e(D,{placeholder:"\u8F6C\u8D26/\u5546\u6237/\u6E20\u9053\u8BA2\u5355\u53F7",value:_.searchData.unionOrderId,"onUpdate:value":u[1]||(u[1]=m=>_.searchData.unionOrderId=m)},null,8,["value"]),e(D,{placeholder:"\u5E94\u7528AppId",value:_.searchData.appId,"onUpdate:value":u[2]||(u[2]=m=>_.searchData.appId=m)},null,8,["value"]),e($,{value:_.searchData.state,"onUpdate:value":u[3]||(u[3]=m=>_.searchData.state=m),placeholder:"\u8F6C\u8D26\u72B6\u6001",class:"table-head-layout"},{default:t(()=>[e(v,{value:""},{default:t(()=>u[6]||(u[6]=[l("\u5168\u90E8")])),_:1}),e(v,{value:"0"},{default:t(()=>u[7]||(u[7]=[l("\u8BA2\u5355\u751F\u6210")])),_:1}),e(v,{value:"1"},{default:t(()=>u[8]||(u[8]=[l("\u8F6C\u8D26\u4E2D")])),_:1}),e(v,{value:"2"},{default:t(()=>u[9]||(u[9]=[l("\u8F6C\u8D26\u6210\u529F")])),_:1}),e(v,{value:"3"},{default:t(()=>u[10]||(u[10]=[l("\u8F6C\u8D26\u5931\u8D25")])),_:1})]),_:1},8,["value"]),p("span",ce,[e(x,{type:"primary",onClick:o,loading:_.btnLoading},{default:t(()=>u[11]||(u[11]=[l(" \u641C\u7D22 ")])),_:1},8,["loading"]),e(x,{style:{"margin-left":"8px"},onClick:u[4]||(u[4]=()=>{_.searchData={},_.date=""})},{default:t(()=>u[12]||(u[12]=[l(" \u91CD\u7F6E ")])),_:1})])])]),_:1})]),e(M,{onBtnLoadClose:u[5]||(u[5]=m=>_.btnLoading=!1),ref_key:"infoTable",ref:E,initData:!0,reqTableDataFunc:r,tableColumns:_.tableColumns,searchData:_.searchData,rowKey:"transferId",tableRowCrossColor:!0},{bodyCell:t(({column:m,record:d})=>[m.key==="amount"?(c(),B("b",De,"\uFFE5"+n(d.amount/100),1)):g("",!0),m.key==="state"?(c(),b(Q,{key:d.state,color:d.state===0?"blue":d.state===1?"orange":d.state===2?"green":"volcano"},{default:t(()=>[l(n(d.state===0?"\u8BA2\u5355\u751F\u6210":d.state===1?"\u8F6C\u8D26\u4E2D":d.state===2?"\u8F6C\u8D26\u6210\u529F":d.state===3?"\u8F6C\u8D26\u5931\u8D25":d.state===4?"\u4EFB\u52A1\u5173\u95ED":"\u672A\u77E5"),1)]),_:2},1032,["color"])):g("",!0),m.key==="orderNo"?(c(),B("div",be,[p("p",null,[u[13]||(u[13]=p("span",{style:{color:"#729ed5",background:"#e7f5f7"}},"\u8F6C\u8D26",-1)),l(" "+n(d.transferId),1)]),p("p",ge,[u[14]||(u[14]=p("span",{style:{color:"#56cf56",background:"#d8eadf"}},"\u5546\u6237",-1)),d.mchOrderNo.length>d.transferId.length?(c(),b(w,{key:0,placement:"bottom",style:{"font-weight":"normal"}},{default:t(()=>[p("template",Ce,[p("span",null,n(d.mchOrderNo),1)]),l(" "+n(h(d.mchOrderNo,d.transferId.length)),1)]),_:2},1024)):(c(),B("span",Fe,n(d.mchOrderNo),1))]),d.channelOrderNo?(c(),B("p",ye,[u[15]||(u[15]=p("span",{style:{color:"#fff",background:"#e09c4d"}},"\u6E20\u9053",-1)),d.channelOrderNo.length>d.transferId.length?(c(),b(w,{key:0,placement:"bottom",style:{"font-weight":"normal"}},{default:t(()=>[p("template",Ee,[p("span",null,n(d.channelOrderNo),1)]),l(" "+n(h(d.channelOrderNo,d.transferId.length)),1)]),_:2},1024)):(c(),B("span",he,n(d.channelOrderNo),1))])):g("",!0)])):g("",!0),m.key==="op"?(c(),b(V,{key:3},{default:t(()=>[le(k)("ENT_TRANSFER_ORDER_VIEW")?(c(),b(x,{key:0,type:"link",onClick:Be=>s(d.transferId)},{default:t(()=>u[16]||(u[16]=[l(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)):g("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),e(fe,{ref_key:"transferOrderDetailRef",ref:i},null,512)]),_:1})}}});var Ae=ee(ve,[["__scopeId","data-v-729fb682"]]);export{Ae as default};
