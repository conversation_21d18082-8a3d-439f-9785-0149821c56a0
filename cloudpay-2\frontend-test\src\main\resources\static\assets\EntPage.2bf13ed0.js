import{n as V,r as G,o as w,l as J,h as H}from"./manage.6e729324.js";import{d as A,g as j,r as N,h as I,aE as p,w as u,bZ as K,o as _,b as a,j as d,I as Z,m as U,F as q,O as z,i as y,a as Q,aw as D,aF as O,B as W}from"./index.8746381c.js";import{R as X,_ as Y}from"./Group.170fc6be.js";import{S as ee,a as te}from"./index.08051bcd.js";import{_ as ae,a as ue}from"./index.7c25015e.js";import{C as ne}from"./Card.d6389e0b.js";import"./List.ee977be2.js";import"./TabPane.9792ea88.js";import"./useMergedState.8a9045a6.js";import"./useMemo.91f6d273.js";import"./index.8f4a8fa1.js";import"./index.4c901be3.js";const oe=A({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>{}}},setup(k,{expose:g}){const{$infoBox:c,$access:x,$hasAgentEnt:E}=j().appContext.config.globalProperties,m=k,e=N({confirmLoading:!1,isAdd:!0,isShow:!1,saveObject:{},recordId:null,sysType:"MGR",rules:{entName:[{required:!0,message:"\u8BF7\u8F93\u5165\u8D44\u6E90\u540D\u79F0",trigger:"blur"}]}}),l=I();function C(r,t){e.isAdd=!r,e.sysType=t,e.saveObject={},e.confirmLoading=!1,l.value!==void 0&&l.value.resetFields(),e.isAdd||(e.recordId=r,V(r,t).then(n=>{e.saveObject=n})),e.isShow=!0}function T(){l.value.validate().then(r=>{r&&(e.confirmLoading=!0,e.isAdd||G.updateById(w,e.recordId,e.saveObject).then(t=>{c.message.success("\u4FEE\u6539\u6210\u529F"),e.isShow=!1,m.callbackFunc()}).catch(t=>{e.confirmLoading=!1}))})}return g({show:C}),(r,t)=>{const n=Z,s=U,i=X,f=Y,F=q,b=K;return _(),p(b,{open:e.isShow,"onUpdate:open":t[5]||(t[5]=o=>e.isShow=o),title:e.isAdd?"\u65B0\u589E\u83DC\u5355":"\u4FEE\u6539\u83DC\u5355",onOk:T,confirmLoading:e.confirmLoading},{default:u(()=>[a(F,{ref_key:"infoFormModel",ref:l,model:e.saveObject,"label-col":{span:6},"wrapper-col":{span:15},rules:e.rules},{default:u(()=>[a(s,{label:"\u8D44\u6E90\u540D\u79F0\uFF1A",name:"entName"},{default:u(()=>[a(n,{value:e.saveObject.entName,"onUpdate:value":t[0]||(t[0]=o=>e.saveObject.entName=o)},null,8,["value"])]),_:1}),a(s,{label:"\u8DEF\u5F84\u5730\u5740\uFF1A",name:"menuUri"},{default:u(()=>[a(n,{value:e.saveObject.menuUri,"onUpdate:value":t[1]||(t[1]=o=>e.saveObject.menuUri=o)},null,8,["value"])]),_:1}),a(s,{label:"\u6392\u5E8F\uFF08\u6B63\u5E8F\u663E\u793A\uFF09\uFF1A",name:"entSort"},{default:u(()=>[a(n,{value:e.saveObject.entSort,"onUpdate:value":t[2]||(t[2]=o=>e.saveObject.entSort=o)},null,8,["value"])]),_:1}),a(s,{label:"\u5FEB\u901F\u5F00\u59CB\uFF1A",name:"quickJump"},{default:u(()=>[a(f,{value:e.saveObject.quickJump,"onUpdate:value":t[3]||(t[3]=o=>e.saveObject.quickJump=o),disabled:e.saveObject.menuType=="PB"||!e.saveObject.menuUri},{default:u(()=>[a(i,{value:1},{default:u(()=>t[6]||(t[6]=[d("\u662F")])),_:1,__:[6]}),a(i,{value:0},{default:u(()=>t[7]||(t[7]=[d("\u5426")])),_:1,__:[7]})]),_:1},8,["value","disabled"])]),_:1}),a(s,{label:"\u72B6\u6001\uFF1A",name:"state"},{default:u(()=>[a(f,{value:e.saveObject.state,"onUpdate:value":t[4]||(t[4]=o=>e.saveObject.state=o)},{default:u(()=>[a(i,{value:1},{default:u(()=>t[8]||(t[8]=[d("\u542F\u7528")])),_:1,__:[8]}),a(i,{value:0},{default:u(()=>t[9]||(t[9]=[d("\u505C\u7528")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["open","title","confirmLoading"])}}}),le={class:"table-page-search-wrapper"},ye=A({__name:"EntPage",setup(k){const{$infoBox:g,$access:c}=j().appContext.config.globalProperties,x=[{title:"\u8D44\u6E90\u6743\u9650ID",dataIndex:"entId"},{title:"\u8D44\u6E90\u540D\u79F0",dataIndex:"entName"},{title:"\u56FE\u6807",dataIndex:"menuIcon"},{title:"\u8DEF\u5F84",dataIndex:"menuUri"},{title:"\u7EC4\u4EF6\u540D\u79F0",dataIndex:"componentName"},{title:"\u7C7B\u578B",dataIndex:"entType"},{title:"\u72B6\u6001",key:"state",align:"center"},{title:"\u6392\u5E8F",dataIndex:"entSort"},{title:"\u4FEE\u6539\u65F6\u95F4",dataIndex:"updatedAt"},{title:"\u64CD\u4F5C",width:"100px",fixed:"right",align:"center",key:"op"}],E=I(),m=I(),e=N({querySysType:"MGR",tableColumns:x,dataSource:[],loading:!1});function l(){e.loading=!0,J(e.querySysType).then(t=>{e.dataSource=t,e.loading=!1,m.value.refTable(!0)})}function C(){return Promise.resolve({current:0,total:0,records:e.dataSource,hasNext:!1})}z(()=>l());function T(t,n){return H.updateById(w,t,{state:n,sysType:e.querySysType}).then(s=>{g.message.success("\u66F4\u65B0\u6210\u529F"),l()})}function r(t){E.value.show(t,e.querySysType)}return(t,n)=>{const s=ee,i=te,f=U,F=ae,b=ue,o=q,L=y("cloudpayTableColState"),R=W,h=y("cloudpayTableColumns"),M=y("cloudpayTable"),$=ne,P=y("page-header-wrapper");return _(),p(P,null,{default:u(()=>[a($,null,{default:u(()=>[Q("div",le,[a(o,{layout:"inline",style:{"margin-bottom":"30px"}},{default:u(()=>[a(b,{gutter:16},{default:u(()=>[a(F,{sm:18},{default:u(()=>[a(b,{gutter:16},{default:u(()=>[a(F,{md:6},{default:u(()=>[a(f,{label:""},{default:u(()=>[a(i,{value:e.querySysType,"onUpdate:value":n[0]||(n[0]=v=>e.querySysType=v),placeholder:"\u9009\u62E9\u7CFB\u7EDF\u83DC\u5355",onChange:l,class:"table-head-layout"},{default:u(()=>[a(s,{value:"MGR"},{default:u(()=>n[1]||(n[1]=[d("\u663E\u793A\u83DC\u5355\uFF1A\u8FD0\u8425\u5E73\u53F0")])),_:1,__:[1]}),a(s,{value:"MCH"},{default:u(()=>n[2]||(n[2]=[d("\u663E\u793A\u83DC\u5355\uFF1A\u5546\u6237\u7CFB\u7EDF")])),_:1,__:[2]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),a(M,{ref_key:"infoTable",ref:m,"init-data":!1,"table-columns":e.tableColumns,"req-table-data-func":C,pagination:!1,loading:e.loading,rowKey:"entId",scroll:{x:1450}},{bodyCell:u(({column:v,record:S})=>[v.key==="state"?(_(),p(L,{key:0,state:S.state,showSwitchType:D(c)("ENT_UR_ROLE_ENT_EDIT"),onChange:B=>T(S.entId,B)},null,8,["state","showSwitchType","onChange"])):O("",!0),v.key==="op"?(_(),p(h,{key:1},{default:u(()=>[D(c)("ENT_UR_ROLE_ENT_EDIT")?(_(),p(R,{key:0,type:"link",onClick:B=>r(S.entId)},{default:u(()=>n[3]||(n[3]=[d(" \u4FEE\u6539 ")])),_:2,__:[3]},1032,["onClick"])):O("",!0)]),_:2},1024)):O("",!0)]),_:1},8,["table-columns","loading"])]),_:1}),a(oe,{ref_key:"infoAddOrEdit",ref:E,callbackFunc:l},null,512)]),_:1})}}});export{ye as default};
