import{r as Y,o as oe,u as ce,p as me,q as se,s as ve,t as be,v as he}from"./manage.2dfb5a24.js";import{e as W,g as Q,f as M,r as G,o as m,C as P,w as t,b as u,E as D,d as s,a as h,I as ee,j as ae,l as ue,R as re,k as ie,a2 as pe,m as K,B as X,n as te,F as Z,V as le,a3 as xe,t as R,c as V,K as de,J as fe,M as L,_ as _e,U as ye,W as Fe,a4 as Ie,a5 as J,a6 as Se,G as Oe,a7 as we,a8 as Ue,a9 as je,aa as Te,ab as Ce,$ as ge,ac as Ne,ad as Me}from"./index.fba97cfa.js";const $e={class:"drawer-btn-center"},Ke=W({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>{}}},setup(q,{expose:$}){const{$infoBox:E,$access:j,$hasAgentEnt:U,$SYS_NAME_MAP:e}=Q().appContext.config.globalProperties,x=q,b=M(),d=G({isAdd:!0,open:!1,appId:"",saveObject:{},rules:{appName:[{required:!0,message:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D\u79F0",trigger:"blur"}]}});function w(B){d.isAdd=!B,d.saveObject={state:1,appSecret:"",appSecret_ph:"\u8BF7\u8F93\u5165"},b.value&&b.value.resetFields(),d.rules.appSecret=[],d.isAdd?(d.rules.appSecret.push({required:!0,message:"\u8BF7\u8F93\u5165\u79C1\u94A5\u6216\u70B9\u51FB\u968F\u673A\u751F\u6210\u79C1\u94A5",trigger:"blur"}),d.open=!0):(d.appId=B,Y.getById(oe,B).then(r=>{d.saveObject=r,d.saveObject.appSecret_ph=r.appSecret,d.saveObject.appSecret=""}),d.open=!0)}function F(){b.value.validate().then(B=>{B&&(delete d.saveObject.appSecret_ph,d.isAdd?Y.add(oe,d.saveObject).then(r=>{E.message.success("\u65B0\u589E\u6210\u529F"),d.open=!1,x.callbackFunc()}):(d.saveObject.appSecret===""&&delete d.saveObject.appSecret,Y.updateById(oe,d.appId,d.saveObject).then(r=>{E.message.success("\u4FEE\u6539\u6210\u529F"),d.open=!1,x.callbackFunc()})))})}function y(B,r,l){let a="",p=r;const A=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];B&&(p=Math.round(Math.random()*(l-r))+r);for(var c=0;c<p;c++){var o=Math.round(Math.random()*(A.length-1));a+=A[o]}d.saveObject.appSecret=a}function i(){d.open=!1}return $({show:w}),(B,r)=>{const l=ee,a=ae,p=ue,A=re,c=ie,o=pe,C=K("a-icon"),O=X,_=te,f=Z,H=le;return m(),P(H,{open:d.open,"onUpdate:open":r[6]||(r[6]=g=>d.open=g),title:d.isAdd?"\u65B0\u589E\u5E94\u7528":"\u4FEE\u6539\u5E94\u7528",width:"40%",maskClosable:!1,onClose:i},{default:t(()=>[u(f,{ref_key:"infoFormModel",ref:b,model:d.saveObject,layout:"vertical",rules:d.rules},{default:t(()=>[u(_,{gutter:16},{default:t(()=>[d.isAdd?D("",!0):(m(),P(p,{key:0,span:12},{default:t(()=>[u(a,{label:"\u5E94\u7528 AppId",prop:"appId"},{default:t(()=>[u(l,{value:d.saveObject.appId,"onUpdate:value":r[0]||(r[0]=g=>d.saveObject.appId=g),placeholder:"\u8BF7\u8F93\u5165",disabled:!d.isAdd},null,8,["value","disabled"])]),_:1})]),_:1})),u(p,{span:12},{default:t(()=>[u(a,{label:"\u5E94\u7528\u540D\u79F0",prop:"appName"},{default:t(()=>[u(l,{value:d.saveObject.appName,"onUpdate:value":r[1]||(r[1]=g=>d.saveObject.appName=g),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(p,{span:12},{default:t(()=>[u(a,{label:"\u72B6\u6001",prop:"state"},{default:t(()=>[u(c,{value:d.saveObject.state,"onUpdate:value":r[2]||(r[2]=g=>d.saveObject.state=g)},{default:t(()=>[u(A,{value:1},{default:t(()=>r[7]||(r[7]=[s("\u542F\u7528")])),_:1}),u(A,{value:0},{default:t(()=>r[8]||(r[8]=[s("\u505C\u7528")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(p,{span:24},{default:t(()=>[u(a,{label:"\u79C1\u94A5 AppSecret",prop:"appSecret"},{default:t(()=>[u(o,{value:d.saveObject.appSecret,"onUpdate:value":r[3]||(r[3]=g=>d.saveObject.appSecret=g),placeholder:d.saveObject.appSecret_ph},null,8,["value","placeholder"]),u(O,{type:"primary",ghost:"",onClick:r[4]||(r[4]=g=>y(!1,128,0))},{default:t(()=>[u(C,{type:"file-sync"}),r[9]||(r[9]=s(" \u968F\u673A\u751F\u6210\u79C1\u94A5 "))]),_:1})]),_:1})]),_:1}),u(p,{span:12},{default:t(()=>[u(a,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[u(l,{value:d.saveObject.remark,"onUpdate:value":r[5]||(r[5]=g=>d.saveObject.remark=g),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),h("div",$e,[u(O,{onClick:i,style:{marginRight:"8px"}},{default:t(()=>r[10]||(r[10]=[s("\u53D6\u6D88")])),_:1}),u(O,{type:"primary",onClick:F},{default:t(()=>r[11]||(r[11]=[s("\u4FDD\u5B58")])),_:1})])]),_:1},8,["open","title"])}}});const Re={key:0,class:"drawer-btn-center"},Ve=W({__name:"MchPayConfigAddOrEdit",props:{callbackFunc:{type:Function,default:()=>({})}},setup(q,{expose:$}){const{$infoBox:E,$access:j}=Q().appContext.config.globalProperties,U=q,e=G({btnLoading:!1,open:!1,appId:null,ifCode:null,mchType:null,action:ce.cert,mchParams:{},saveObject:{},ifParams:{},rules:{infoId:[{required:!0,trigger:"blur"}],ifCode:[{required:!0,trigger:"blur"}]},ifParamsRules:{}}),x=M(),b=M();xe(()=>e.ifParams,()=>{});function d(r,l){e.appId=r,e.ifCode=l.ifCode,e.mchType=l.mchType,e.saveObject={},e.ifParams={},e.mchParams={},e.saveObject.infoId=r,e.saveObject.ifCode=l.ifCode,e.saveObject.state=l.ifConfigState===0?0:1,x.value!==void 0&&x.value.resetFields(),w(l)}function w(r){me(e.saveObject.infoId,e.saveObject.ifCode).then(l=>{l&&l.ifParams&&(e.saveObject=l,e.ifParams=JSON.parse(l.ifParams));const a=[];let p=[];const A=e.mchType==1?r.normalMchParams:r.isvsubMchParams;try{const c=JSON.parse(A);Array.isArray(c)?(c.forEach(o=>{if(p=[],o.type==="radio"){const C=o.values.split(","),O=o.titles.split(",");for(const _ in C){let f=C[_];isNaN(f)||(f=Number(f)),p.push({value:f,title:O[_]})}}o.star==="1"&&(e.ifParams[o.name+"_ph"]=e.ifParams[o.name]?e.ifParams[o.name]:"\u8BF7\u8F93\u5165",e.ifParams[o.name]&&(e.ifParams[o.name]="")),a.push({name:o.name,desc:o.desc,type:o.type,verify:o.verify,values:p,star:o.star})}),e.mchParams=a,e.open=!0,i()):E.message.error("\u8BE5\u6E20\u9053\u65E0\u6CD5\u8FDB\u884C\u5546\u6237\u53C2\u6570\u914D\u7F6E")}catch{E.message.error("\u8BE5\u6E20\u9053\u65E0\u6CD5\u8FDB\u884C\u5546\u6237\u53C2\u6570\u914D\u7F6E")}})}function F(){b.value.validate().then(r=>{x.value.validate().then(l=>{if(r&&l){e.btnLoading=!0;const a={};if(a.infoId=e.saveObject.infoId,a.ifCode=e.saveObject.ifCode,a.state=e.saveObject.state,a.remark=e.saveObject.remark,Object.keys(e.ifParams).length===0){E.message.error("\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}if(e.mchParams.forEach(p=>{p.star==="1"&&e.ifParams[p.name]===""&&(e.ifParams[p.name]=void 0),e.ifParams[p.name+"_ph"]=void 0}),a.ifParams=JSON.stringify(e.ifParams),Object.keys(a).length===0){E.message.error("\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}Y.add(se,a).then(p=>{E.message.success("\u4FDD\u5B58\u6210\u529F"),e.open=!1,e.btnLoading=!1,U.callbackFunc()})}})})}function y(r,l){e.ifParams[l]=r}function i(){const r={};let l=[];e.mchParams.forEach(a=>{l=[],a.verify==="required"&&a.star!=="1"&&(l.push({required:!0,message:"\u8BF7\u8F93\u5165"+a.desc,trigger:"blur"}),r[a.name]=l)}),e.ifParamsRules=r}function B(){e.open=!1}return $({show:d}),(r,l)=>{const a=re,p=ie,A=ae,c=ue,o=pe,C=te,O=Z,_=ye,f=Fe,H=ee,g=K("a-icon"),T=X,k=K("cloudpayUpload"),I=le;return m(),P(I,{title:"\u586B\u5199\u53C2\u6570",width:"40%",closable:!0,maskClosable:!1,open:e.open,"onUpdate:open":l[2]||(l[2]=v=>e.open=v),"body-style":{paddingBottom:"80px"},onClose:B},{default:t(()=>[u(O,{ref_key:"infoFormModel",ref:b,model:e.saveObject,layout:"vertical",rules:e.rules},{default:t(()=>[u(C,{gutter:16},{default:t(()=>[u(c,{span:12},{default:t(()=>[u(A,{label:"\u72B6\u6001",name:"state"},{default:t(()=>[u(p,{value:e.saveObject.state,"onUpdate:value":l[0]||(l[0]=v=>e.saveObject.state=v)},{default:t(()=>[u(a,{value:1},{default:t(()=>l[3]||(l[3]=[s("\u542F\u7528")])),_:1}),u(a,{value:0},{default:t(()=>l[4]||(l[4]=[s("\u505C\u7528")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(c,{span:24},{default:t(()=>[u(A,{label:"\u5907\u6CE8",name:"remark"},{default:t(()=>[u(o,{value:e.saveObject.remark,"onUpdate:value":l[1]||(l[1]=v=>e.saveObject.remark=v),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),u(f,{orientation:"left"},{default:t(()=>[u(_,{color:"#FF4B33"},{default:t(()=>[s(R(e.saveObject.ifCode)+" \u5546\u6237\u53C2\u6570\u914D\u7F6E",1)]),_:1})]),_:1}),u(O,{ref_key:"mchParamFormModel",ref:x,model:e.ifParams,layout:"vertical",rules:e.ifParamsRules},{default:t(()=>[u(C,{gutter:16},{default:t(()=>[(m(!0),V(fe,null,de(e.mchParams,(v,n)=>(m(),P(c,{key:n,span:v.type==="text"?12:24},{default:t(()=>[v.type==="text"||v.type==="textarea"?(m(),P(A,{key:0,label:v.desc,name:v.name},{default:t(()=>[u(H,{value:e.ifParams[v.name],"onUpdate:value":N=>e.ifParams[v.name]=N,placeholder:v.star==="1"?e.ifParams[v.name+"_ph"]:"\u8BF7\u8F93\u5165",type:v.type},null,8,["value","onUpdate:value","placeholder","type"])]),_:2},1032,["label","name"])):v.type==="radio"?(m(),P(A,{key:1,label:v.desc,name:v.name},{default:t(()=>[u(p,{value:e.ifParams[v.name],"onUpdate:value":N=>e.ifParams[v.name]=N},{default:t(()=>[(m(!0),V(fe,null,de(v.values,(N,z)=>(m(),P(a,{key:z,value:N.value},{default:t(()=>[s(R(N.title),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["label","name"])):v.type==="file"?(m(),P(A,{key:2,label:v.desc,name:v.name},{default:t(()=>[u(H,{value:e.ifParams[v.name],"onUpdate:value":N=>e.ifParams[v.name]=N,disabled:"disabled"},null,8,["value","onUpdate:value"]),u(k,{action:e.action,fileUrl:e.ifParams[v.name],onUploadSuccess:N=>y(N,v.name)},{uploadSlot:t(({loading:N})=>[u(T,{style:{"margin-top":"5px"}},{default:t(()=>[u(g,{type:N?"loading":"upload"},null,8,["type"]),s(" "+R(N?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:2},1032,["action","fileUrl","onUploadSuccess"])]),_:2},1032,["label","name"])):D("",!0)]),_:2},1032,["span"]))),128))]),_:1})]),_:1},8,["model","rules"]),L(j)("ENT_MCH_PAY_CONFIG_ADD")?(m(),V("div",Re,[u(T,{style:{marginRight:"8px"},onClick:B},{default:t(()=>l[5]||(l[5]=[s("\u53D6\u6D88")])),_:1}),u(T,{type:"primary",onClick:F,loading:e.btnLoading},{default:t(()=>l[6]||(l[6]=[s("\u4FDD\u5B58")])),_:1},8,["loading"])])):D("",!0)]),_:1},8,["open"])}}});var Le=_e(Ve,[["__scopeId","data-v-92f29b9e"]]);const qe={key:1},He=["src"],Ye={class:"cloudpay-card-ops"},Ge={style:{position:"absolute",right:0,bottom:0,width:"100%",borderTop:"1px solid #e9e9e9",padding:"10px 16px",background:"#fff",textAlign:"center",boxSizing:"border-box",zIndex:1}},Je=W({__name:"MchPayPassageAddOrEdit",props:{callbackFunc:{type:Function,default:()=>({})}},setup(q,{expose:$}){const{$infoBox:E,$access:j}=Q().appContext.config.globalProperties,U=q,e=G({cardList:[],appId:null,wayCode:null,open:!1,cloudpayCard:{height:300,span:{xxl:3,xl:2,lg:2,md:1,sm:1,xs:1}}});function x(F,y){e.appId=F,e.wayCode=y,e.open=!0,e.cardList=[],b()}function b(){ve(e.appId,e.wayCode).then(F=>{if(F===void 0||F.length===0){e.cardList=[];return}const y=[];F.forEach(i=>{y.push({passageId:i.passageId?i.passageId:"",ifCode:i.ifCode,ifName:i.ifName,icon:i.icon,bgColor:i.bgColor,rate:i.rate,state:i.state===1})}),e.cardList=y})}function d(){const F=[];try{e.cardList.forEach(y=>{y.error="",y.help="";const i=/^(([1-9]{1}\d{0,1})|(0{1}))(\.\d{1,4})?$/;if(y.state){if(!y.rate)throw y.error="error",y.help="\u8BF7\u8F93\u5165\u8D39\u7387",new Error("error");if(!i.test(y.rate)||y.rate>100)throw y.error="error",y.help="\u6700\u591A\u56DB\u4F4D\u5C0F\u6570",new Error("error")}F.push({id:y.passageId,appId:e.appId,wayCode:e.wayCode,ifCode:y.ifCode,rate:y.rate,state:y.state?1:0})})}catch(y){if(y.message==="error")return}Y.add(be,{reqParams:JSON.stringify(F)}).then(y=>{E.message.success("\u4FDD\u5B58\u6210\u529F"),e.open=!1,U.callbackFunc()})}function w(){e.open=!1}return $({show:x}),(F,y)=>{const i=Ie,B=ee,r=ae,l=Z,a=Se,p=ue,A=te,c=X,o=le;return m(),P(o,{open:e.open,"onUpdate:open":y[0]||(y[0]=C=>e.open=C),title:"\u914D\u7F6E\u652F\u4ED8\u901A\u9053",closable:!0,maskClosable:!1,"body-style":{paddingBottom:"80px"},"drawer-style":{backgroundColor:"#f0f2f5"},width:"40%",onClose:w},{default:t(()=>[e.cardList.length===0?(m(),P(i,{key:0,"data-source":[]})):(m(),V("div",qe,[u(A,{gutter:[24,24],style:{width:"100%"}},{default:t(()=>[(m(!0),V(fe,null,de(e.cardList,(C,O)=>(m(),P(p,{key:O,xxl:24/e.cloudpayCard.span.xxl,xl:24/e.cloudpayCard.span.xl,lg:24/e.cloudpayCard.span.lg,md:24/e.cloudpayCard.span.md,sm:24/e.cloudpayCard.span.sm,xs:24/e.cloudpayCard.span.xs},{default:t(()=>[h("div",{style:J({height:e.cloudpayCard.height+"px"}),class:"cloudpay-card-content"},[h("div",{class:"cloudpay-card-content-header",style:J({backgroundColor:C.bgColor,height:(e.cloudpayCard.height-50)/2+"px"})},[C.icon?(m(),V("img",{key:0,src:C.icon,style:J({height:(e.cloudpayCard.height-50)/5+"px"})},null,12,He)):D("",!0)],4),h("div",{class:"cloudpay-card-content-body",style:J({height:(e.cloudpayCard.height-50)/2+"px"})},[h("div",{class:"title",style:J({height:(e.cloudpayCard.height-50)/4+"px",lineHeight:(e.cloudpayCard.height-50)/4+"px"})},R(C.ifName),5),u(l,{labelCol:{span:8},wrapperCol:{span:14}},{default:t(()=>[u(r,{label:"\u8D39\u7387\uFF1A","validate-status":C.error,help:C.help},{default:t(()=>[u(B,{value:C.rate,"onUpdate:value":_=>C.rate=_,disabled:!C.state&&C.passageId!="",suffix:"%"},null,8,["value","onUpdate:value","disabled"])]),_:2},1032,["validate-status","help"])]),_:2},1024)],4),h("div",Ye,[u(a,{checked:C.state,"onUpdate:checked":_=>C.state=_,"checked-children":"\u542F\u7528","un-checked-children":"\u505C\u7528"},null,8,["checked","onUpdate:checked"])])],4)]),_:2},1032,["xxl","xl","lg","md","sm","xs"]))),128))]),_:1}),h("div",Ge,[u(c,{style:{marginRight:"8px"},onClick:w},{default:t(()=>y[1]||(y[1]=[s("\u53D6\u6D88")])),_:1}),L(j)("ENT_MCH_PAY_PASSAGE_ADD")?(m(),P(c,{key:0,type:"primary",onClick:d},{default:t(()=>y[2]||(y[2]=[s(" \u4FDD\u5B58 ")])),_:1})):D("",!0)])]))]),_:1},8,["open"])}}});var ze=_e(Je,[["__scopeId","data-v-29bb93be"]]);const We={key:0,class:"drawer-btn-center"},Qe=W({__name:"WxpayPayConfig",props:{callbackFunc:{type:Function,default:()=>({})}},setup(q,{expose:$}){const{$infoBox:E,$access:j}=Q().appContext.config.globalProperties,U=q,e=G({btnLoading:!1,open:!1,isAdd:!0,mchType:1,action:ce.cert,saveObject:{},ifParams:{apiVersion:"V2"}}),x=M(),b=M(),d=G({mchId:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5FAE\u4FE1\u652F\u4ED8\u5546\u6237\u53F7"):Promise.resolve()}],appId:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5E94\u7528AppID"):Promise.resolve()}],appSecret:[{trigger:"blur",validator:(l,a)=>e.isAdd&&e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5E94\u7528AppSecret"):Promise.resolve()}],key:[{trigger:"blur",validator:(l,a)=>e.ifParams.apiVersion==="V2"&&e.isAdd&&e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165API\u5BC6\u94A5"):Promise.resolve()}],apiV3Key:[{trigger:"blur",validator:(l,a)=>e.ifParams.apiVersion==="V3"&&e.isAdd&&e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165API V3\u79D8\u94A5"):Promise.resolve()}],serialNo:[{trigger:"blur",validator:(l,a)=>e.ifParams.apiVersion==="V3"&&e.isAdd&&e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5E8F\u5217\u53F7"):Promise.resolve()}],cert:[{trigger:"blur",validator:(l,a)=>e.ifParams.apiVersion==="V3"&&e.isAdd&&!a?Promise.reject("\u8BF7\u4E0A\u4F20API\u8BC1\u4E66(apiclient_cert.p12)"):Promise.resolve()}],apiClientCert:[{trigger:"blur",validator:(l,a)=>e.ifParams.apiVersion==="V3"&&e.isAdd&&!a?Promise.reject("\u8BF7\u4E0A\u4F20\u8BC1\u4E66\u6587\u4EF6(apiclient_cert.pem)"):Promise.resolve()}],apiClientKey:[{trigger:"blur",validator:(l,a)=>e.ifParams.apiVersion==="V3"&&e.mchType===1&&!e.ifParams.apiClientKey?Promise.reject("\u8BF7\u4E0A\u4F20\u79C1\u94A5\u6587\u4EF6(apiclient_key.pem)"):Promise.resolve()}],subMchId:[{trigger:"blur",validator:(l,a)=>e.mchType===2&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5B50\u5546\u6237ID"):Promise.resolve()}]});function w(l,a){x.value&&x.value.resetFields(),b.value!==void 0&&b.value.resetFields(),e.mchType=a.mchType,e.saveObject={infoId:l,ifCode:a.ifCode,state:a.ifConfigState===0?0:1},e.ifParams={apiVersion:"V2",appSecret:"",appSecret_ph:"\u8BF7\u8F93\u5165",key:"",key_ph:"\u8BF7\u8F93\u5165",apiV3Key:"",apiV3Key_ph:"\u8BF7\u8F93\u5165",serialNo:"",serialNo_ph:"\u8BF7\u8F93\u5165",wxpayPublicKeyId:"",wxpayPublicKey:"",transferVersion:""},e.open=!0,F()}function F(){me(e.saveObject.infoId,e.saveObject.ifCode).then(l=>{l&&l.ifParams?(e.saveObject=l,e.ifParams=JSON.parse(l.ifParams),e.ifParams.appSecret_ph=e.ifParams.appSecret,e.ifParams.appSecret="",e.ifParams.key_ph=e.ifParams.key,e.ifParams.key="",e.ifParams.apiV3Key_ph=e.ifParams.apiV3Key,e.ifParams.apiV3Key="",e.ifParams.serialNo_ph=e.ifParams.serialNo,e.ifParams.serialNo="",e.isAdd=!1):l===void 0&&(e.isAdd=!0)})}function y(){x.value.validate().then(l=>{b.value.validate().then(a=>{if(l&&a){e.btnLoading=!0;const p={};if(p.infoId=e.saveObject.infoId,p.ifCode=e.saveObject.ifCode,p.state=e.saveObject.state,p.remark=e.saveObject.remark,Object.keys(e.ifParams).length===0){E.message.error("\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}if(i("appSecret"),i("key"),i("apiV3Key"),i("serialNo"),p.ifParams=JSON.stringify(e.ifParams),Object.keys(p).length===0){E.message.error("\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}Y.add(se,p).then(A=>{E.message.success("\u4FDD\u5B58\u6210\u529F"),e.open=!1,U.callbackFunc()}).catch(()=>{E.message.error("\u4FDD\u5B58\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}).finally(()=>{e.btnLoading=!1})}})})}function i(l){e.ifParams[l]||(e.ifParams[l]=void 0),e.ifParams[l+"_ph"]=void 0}function B(l,a){e.ifParams[a]=l}function r(){e.open=!1}return $({show:w}),(l,a)=>{const p=re,A=ie,c=ae,o=ue,C=pe,O=te,_=Z,f=ye,H=Fe,g=ee,T=K("a-icon"),k=X,I=K("cloudpayUpload"),v=le;return m(),P(v,{open:e.open,"onUpdate:open":a[22]||(a[22]=n=>e.open=n),title:"\u586B\u5199\u53C2\u6570",width:"40%",closable:!0,maskClosable:!1,"body-style":{paddingBottom:"80px"},onClose:r},{default:t(()=>[u(_,{ref_key:"infoFormModel",ref:x,model:e.saveObject,layout:"vertical"},{default:t(()=>[u(O,{gutter:16},{default:t(()=>[u(o,{span:12},{default:t(()=>[u(c,{label:"\u72B6\u6001",name:"state"},{default:t(()=>[u(A,{value:e.saveObject.state,"onUpdate:value":a[0]||(a[0]=n=>e.saveObject.state=n)},{default:t(()=>[u(p,{value:1},{default:t(()=>a[23]||(a[23]=[s("\u542F\u7528")])),_:1}),u(p,{value:0},{default:t(()=>a[24]||(a[24]=[s("\u505C\u7528")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(o,{span:24},{default:t(()=>[u(c,{label:"\u5907\u6CE8",name:"remark"},{default:t(()=>[u(C,{value:e.saveObject.remark,"onUpdate:value":a[1]||(a[1]=n=>e.saveObject.remark=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),u(H,{orientation:"left"},{default:t(()=>[u(f,{color:"#FF4B33"},{default:t(()=>[s(R(e.saveObject.ifCode)+" \u5546\u6237\u53C2\u6570\u914D\u7F6E",1)]),_:1})]),_:1}),u(_,{ref_key:"mchParamFormModel",ref:b,model:e.ifParams,layout:"vertical",rules:d},{default:t(()=>[e.mchType===1?(m(),P(O,{key:0,gutter:16},{default:t(()=>[u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5FAE\u4FE1\u652F\u4ED8\u5546\u6237\u53F7",name:"mchId"},{default:t(()=>[u(g,{value:e.ifParams.mchId,"onUpdate:value":a[2]||(a[2]=n=>e.ifParams.mchId=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5E94\u7528AppID",name:"appId"},{default:t(()=>[u(g,{value:e.ifParams.appId,"onUpdate:value":a[3]||(a[3]=n=>e.ifParams.appId=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5E94\u7528AppSecret",name:"appSecret"},{default:t(()=>[u(g,{value:e.ifParams.appSecret,"onUpdate:value":a[4]||(a[4]=n=>e.ifParams.appSecret=n),placeholder:e.ifParams.appSecret_ph},null,8,["value","placeholder"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"oauth2\u5730\u5740\uFF08\u7F6E\u7A7A\u5C06\u4F7F\u7528\u5B98\u65B9\uFF09",name:"oauth2Url"},{default:t(()=>[u(g,{value:e.ifParams.oauth2Url,"onUpdate:value":a[5]||(a[5]=n=>e.ifParams.oauth2Url=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5FAE\u4FE1\u652F\u4ED8API\u7248\u672C",name:"apiVersion"},{default:t(()=>[u(A,{value:e.ifParams.apiVersion,"onUpdate:value":a[6]||(a[6]=n=>e.ifParams.apiVersion=n),defaultValue:"V2"},{default:t(()=>[u(p,{value:"V2"},{default:t(()=>a[25]||(a[25]=[s("V2")])),_:1}),u(p,{value:"V3"},{default:t(()=>a[26]||(a[26]=[s("V3")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"APIv2\u5BC6\u94A5",name:"key"},{default:t(()=>[u(C,{value:e.ifParams.key,"onUpdate:value":a[7]||(a[7]=n=>e.ifParams.key=n),placeholder:e.ifParams.key_ph},null,8,["value","placeholder"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"APIv3\u79D8\u94A5",name:"apiV3Key"},{default:t(()=>[u(C,{value:e.ifParams.apiV3Key,"onUpdate:value":a[8]||(a[8]=n=>e.ifParams.apiV3Key=n),placeholder:e.ifParams.apiV3Key_ph},null,8,["value","placeholder"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u5E8F\u5217\u53F7",name:"serialNo"},{default:t(()=>[u(C,{value:e.ifParams.serialNo,"onUpdate:value":a[9]||(a[9]=n=>e.ifParams.serialNo=n),placeholder:e.ifParams.serialNo_ph},null,8,["value","placeholder"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"API\u8BC1\u4E66(apiclient_cert.p12)",name:"cert"},{default:t(()=>[u(g,{value:e.ifParams.cert,"onUpdate:value":a[10]||(a[10]=n=>e.ifParams.cert=n),disabled:"disabled"},null,8,["value"]),u(I,{action:e.action,fileUrl:e.ifParams.cert,onUploadSuccess:a[11]||(a[11]=n=>B(n,"cert"))},{uploadSlot:t(({loading:n})=>[u(k,{style:{"margin-top":"5px"}},{default:t(()=>[u(T,{type:n?"loading":"upload"},null,8,["type"]),s(" "+R(n?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u8BC1\u4E66\u6587\u4EF6(apiclient_cert.pem)",name:"apiClientCert"},{default:t(()=>[u(g,{value:e.ifParams.apiClientCert,"onUpdate:value":a[12]||(a[12]=n=>e.ifParams.apiClientCert=n),disabled:"disabled"},null,8,["value"]),u(I,{action:e.action,fileUrl:e.ifParams.apiClientCert,onUploadSuccess:a[13]||(a[13]=n=>B(n,"apiClientCert"))},{uploadSlot:t(({loading:n})=>[u(k,{style:{"margin-top":"5px"}},{default:t(()=>[u(T,{type:n?"loading":"upload"},null,8,["type"]),s(" "+R(n?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u79C1\u94A5\u6587\u4EF6(apiclient_key.pem)",name:"apiClientKey"},{default:t(()=>[u(g,{value:e.ifParams.apiClientKey,"onUpdate:value":a[14]||(a[14]=n=>e.ifParams.apiClientKey=n),disabled:"disabled"},null,8,["value"]),u(I,{action:e.action,fileUrl:e.ifParams.apiClientKey,onUploadSuccess:a[15]||(a[15]=n=>B(n,"apiClientKey"))},{uploadSlot:t(({loading:n})=>[u(k,{style:{"margin-top":"5px"}},{default:t(()=>[u(T,{type:n?"loading":"upload"},null,8,["type"]),s(" "+R(n?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u5FAE\u4FE1\u4FA7\u516C\u94A5ID",name:"wxpayPublicKeyId"},{default:t(()=>[u(g,{value:e.ifParams.wxpayPublicKeyId,"onUpdate:value":a[16]||(a[16]=n=>e.ifParams.wxpayPublicKeyId=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u5FAE\u4FE1\u4FA7\u516C\u94A5\u8BC1\u4E66\uFF08pub_key.pem\uFF09",name:"wxpayPublicKey"},{default:t(()=>[u(g,{value:e.ifParams.wxpayPublicKey,"onUpdate:value":a[17]||(a[17]=n=>e.ifParams.wxpayPublicKey=n),disabled:"disabled"},null,8,["value"]),u(I,{action:e.action,fileUrl:e.ifParams.wxpayPublicKey,onUploadSuccess:a[18]||(a[18]=n=>B(n,"wxpayPublicKey"))},{uploadSlot:t(({loading:n})=>[u(k,{style:{"margin-top":"5px"}},{default:t(()=>[u(T,{type:e.loading?"loading":"upload"},null,8,["type"]),s(" "+R(e.loading?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5FAE\u4FE1\u8F6C\u8D26\u7248\u672C",name:"transferVersion"},{default:t(()=>[u(A,{value:e.ifParams.transferVersion,"onUpdate:value":a[19]||(a[19]=n=>e.ifParams.transferVersion=n),defaultValue:"old"},{default:t(()=>[u(p,{value:"old"},{default:t(()=>a[27]||(a[27]=[s("\u65E7\u7248")])),_:1}),u(p,{value:"new202501"},{default:t(()=>a[28]||(a[28]=[s("\u65B0\u7248202501")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})):e.mchType===2?(m(),P(O,{key:1,gutter:16},{default:t(()=>[u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5B50\u5546\u6237ID",name:"subMchId"},{default:t(()=>[u(g,{value:e.ifParams.subMchId,"onUpdate:value":a[20]||(a[20]=n=>e.ifParams.subMchId=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5B50\u8D26\u6237appID(\u7EBF\u4E0A\u652F\u4ED8\u5FC5\u586B)",name:"subMchAppId"},{default:t(()=>[u(g,{value:e.ifParams.subMchAppId,"onUpdate:value":a[21]||(a[21]=n=>e.ifParams.subMchAppId=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})]),_:1})):D("",!0)]),_:1},8,["model","rules"]),L(j)("ENT_MCH_PAY_CONFIG_ADD")?(m(),V("div",We,[u(k,{style:{marginRight:"8px"},onClick:r},{default:t(()=>a[29]||(a[29]=[s("\u53D6\u6D88")])),_:1}),u(k,{type:"primary",loading:e.btnLoading,onClick:y},{default:t(()=>a[30]||(a[30]=[s("\u4FDD\u5B58")])),_:1},8,["loading"])])):D("",!0)]),_:1},8,["open"])}}}),Xe={key:0,class:"drawer-btn-center"},Ze=W({__name:"AlipayPayConfig",props:{callbackFunc:{type:Function,default:()=>({})}},setup(q,{expose:$}){const{$infoBox:E,$access:j}=Q().appContext.config.globalProperties,U=q,e=G({btnLoading:!1,open:!1,isAdd:!0,mchType:1,action:ce.cert,saveObject:{},ifParams:{}}),x=G({appId:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5E94\u7528AppID"):Promise.resolve()}],privateKey:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&e.isAdd&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5E94\u7528\u79C1\u94A5"):Promise.resolve()}],alipayPublicKey:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&e.isAdd&&e.ifParams.useCert===0&&!a?Promise.reject("\u8BF7\u8F93\u5165\u652F\u4ED8\u5B9D\u516C\u94A5"):Promise.resolve()}],appPublicCert:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&e.ifParams.useCert===1&&!e.ifParams.appPublicCert?Promise.reject("\u8BF7\u4E0A\u4F20\u5E94\u7528\u516C\u94A5\u8BC1\u4E66\uFF08.crt\u683C\u5F0F\uFF09"):Promise.resolve()}],alipayPublicCert:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&e.ifParams.useCert===1&&!e.ifParams.alipayPublicCert?Promise.reject("\u8BF7\u4E0A\u4F20\u652F\u4ED8\u5B9D\u516C\u94A5\u8BC1\u4E66\uFF08.crt\u683C\u5F0F\uFF09"):Promise.resolve()}],alipayRootCert:[{trigger:"blur",validator:(l,a)=>e.mchType===1&&e.ifParams.useCert===1&&!e.ifParams.alipayRootCert?Promise.reject("\u8BF7\u4E0A\u4F20\u652F\u4ED8\u5B9D\u6839\u8BC1\u4E66\uFF08.crt\u683C\u5F0F\uFF09"):Promise.resolve()}],appAuthToken:[{trigger:"blur",validator:(l,a)=>e.mchType===2&&!a?Promise.reject("\u8BF7\u8F93\u5165\u5B50\u5546\u6237app_auth_token"):Promise.resolve()}]}),b=M(),d=M();function w(l,a){b.value&&b.value.resetFields(),d.value&&d.value.resetFields(),e.mchType=a.mchType,e.saveObject={infoId:l,ifCode:a.ifCode,state:a.ifConfigState===0?0:1},e.ifParams={sandbox:0,signType:"RSA2",useCert:0,privateKey:"",privateKey_ph:"\u8BF7\u8F93\u5165",alipayPublicKey:"",alipayPublicKey_ph:"\u8BF7\u8F93\u5165",appPublicCert:"",alipayPublicCert:"",alipayRootCert:""},e.open=!0,F()}function F(){me(e.saveObject.infoId,e.saveObject.ifCode).then(l=>{l&&l.ifParams?(e.saveObject=l,e.ifParams=JSON.parse(l.ifParams),e.ifParams.privateKey_ph=e.ifParams.privateKey,e.ifParams.privateKey="",e.ifParams.alipayPublicKey_ph=e.ifParams.alipayPublicKey,e.ifParams.alipayPublicKey="",e.isAdd=!1):l===void 0&&(e.isAdd=!0)})}function y(){b.value.validate().then(l=>{d.value.validate().then(a=>{if(l&&a){e.btnLoading=!0;const p={};if(p.infoId=e.saveObject.infoId,p.ifCode=e.saveObject.ifCode,p.state=e.saveObject.state,p.remark=e.saveObject.remark,Object.keys(e.ifParams).length===0){E.message.error("\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}if(i("privateKey"),i("alipayPublicKey"),p.ifParams=JSON.stringify(e.ifParams),Object.keys(p).length===0){E.message.error("\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}Y.add(se,p).then(A=>{E.message.success("\u4FDD\u5B58\u6210\u529F"),e.open=!1,U.callbackFunc()}).catch(()=>{E.message.error("\u4FDD\u5B58\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}).finally(()=>{e.btnLoading=!1})}})})}function i(l){e.ifParams[l]||(e.ifParams[l]=void 0),e.ifParams[l+"_ph"]=void 0}function B(l,a){e.ifParams[a]=l}function r(){e.open=!1}return $({show:w}),(l,a)=>{const p=re,A=ie,c=ae,o=ue,C=pe,O=te,_=Z,f=ye,H=Fe,g=ee,T=K("a-icon"),k=X,I=K("cloudpayUpload"),v=le;return m(),P(v,{open:e.open,"onUpdate:open":a[15]||(a[15]=n=>e.open=n),title:"\u586B\u5199\u53C2\u6570",width:"40%",closable:!0,maskClosable:!1,"body-style":{paddingBottom:"80px"},onClose:r},{default:t(()=>[u(_,{ref_key:"infoFormModel",ref:b,model:e.saveObject,layout:"vertical"},{default:t(()=>[u(O,{gutter:16},{default:t(()=>[u(o,{span:12},{default:t(()=>[u(c,{label:"\u72B6\u6001",name:"state"},{default:t(()=>[u(A,{value:e.saveObject.state,"onUpdate:value":a[0]||(a[0]=n=>e.saveObject.state=n)},{default:t(()=>[u(p,{value:1},{default:t(()=>a[16]||(a[16]=[s("\u542F\u7528")])),_:1}),u(p,{value:0},{default:t(()=>a[17]||(a[17]=[s("\u505C\u7528")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(o,{span:24},{default:t(()=>[u(c,{label:"\u5907\u6CE8",name:"remark"},{default:t(()=>[u(C,{value:e.saveObject.remark,"onUpdate:value":a[1]||(a[1]=n=>e.saveObject.remark=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),u(H,{orientation:"left"},{default:t(()=>[u(f,{color:"#FF4B33"},{default:t(()=>[s(R(e.saveObject.ifCode)+" \u5546\u6237\u53C2\u6570\u914D\u7F6E",1)]),_:1})]),_:1}),u(_,{ref_key:"mchParamFormModel",ref:d,model:e.ifParams,layout:"vertical",rules:x},{default:t(()=>[e.mchType===1?(m(),P(O,{key:0,gutter:16},{default:t(()=>[u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u73AF\u5883\u914D\u7F6E",name:"sandbox"},{default:t(()=>[u(A,{value:e.ifParams.sandbox,"onUpdate:value":a[2]||(a[2]=n=>e.ifParams.sandbox=n)},{default:t(()=>[u(p,{value:1},{default:t(()=>a[18]||(a[18]=[s("\u6C99\u7BB1\u73AF\u5883")])),_:1}),u(p,{value:0},{default:t(()=>a[19]||(a[19]=[s("\u751F\u4EA7\u73AF\u5883")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5E94\u7528AppID",name:"appId"},{default:t(()=>[u(g,{value:e.ifParams.appId,"onUpdate:value":a[3]||(a[3]=n=>e.ifParams.appId=n),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u5E94\u7528\u79C1\u94A5",name:"privateKey"},{default:t(()=>[u(C,{value:e.ifParams.privateKey,"onUpdate:value":a[4]||(a[4]=n=>e.ifParams.privateKey=n),placeholder:e.ifParams.privateKey_ph},null,8,["value","placeholder"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u652F\u4ED8\u5B9D\u516C\u94A5",name:"alipayPublicKey"},{default:t(()=>[u(C,{value:e.ifParams.alipayPublicKey,"onUpdate:value":a[5]||(a[5]=n=>e.ifParams.alipayPublicKey=n),placeholder:e.ifParams.alipayPublicKey_ph},null,8,["value","placeholder"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u63A5\u53E3\u7B7E\u540D\u65B9\u5F0F(\u63A8\u8350\u4F7F\u7528RSA2)",name:"signType"},{default:t(()=>[u(A,{value:e.ifParams.signType,"onUpdate:value":a[6]||(a[6]=n=>e.ifParams.signType=n),defaultValue:"RSA"},{default:t(()=>[u(p,{value:"RSA"},{default:t(()=>a[20]||(a[20]=[s("RSA")])),_:1}),u(p,{value:"RSA2"},{default:t(()=>a[21]||(a[21]=[s("RSA2")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u516C\u94A5\u8BC1\u4E66",name:"useCert"},{default:t(()=>[u(A,{value:e.ifParams.useCert,"onUpdate:value":a[7]||(a[7]=n=>e.ifParams.useCert=n),defaultValue:"1"},{default:t(()=>[u(p,{value:1},{default:t(()=>a[22]||(a[22]=[s("\u4F7F\u7528\u8BC1\u4E66\uFF08\u8BF7\u4F7F\u7528RSA2\u79C1\u94A5\uFF09")])),_:1}),u(p,{value:0},{default:t(()=>a[23]||(a[23]=[s("\u4E0D\u4F7F\u7528\u8BC1\u4E66")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u5E94\u7528\u516C\u94A5\u8BC1\u4E66\uFF08.crt\u683C\u5F0F\uFF09",name:"appPublicCert",class:"margin-botomt-5"},{default:t(()=>[u(g,{value:e.ifParams.appPublicCert,"onUpdate:value":a[8]||(a[8]=n=>e.ifParams.appPublicCert=n),disabled:"disabled"},null,8,["value"])]),_:1}),u(I,{style:{"margin-bottom":"20px"},action:e.action,fileUrl:e.ifParams.appPublicCert,onUploadSuccess:a[9]||(a[9]=n=>B(n,"appPublicCert"))},{uploadSlot:t(({loading:n})=>[u(k,null,{default:t(()=>[u(T,{type:n?"loading":"upload"},null,8,["type"]),s(" "+R(n?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u652F\u4ED8\u5B9D\u516C\u94A5\u8BC1\u4E66\uFF08.crt\u683C\u5F0F\uFF09",name:"alipayPublicCert",class:"margin-botomt-5"},{default:t(()=>[u(g,{value:e.ifParams.alipayPublicCert,"onUpdate:value":a[10]||(a[10]=n=>e.ifParams.alipayPublicCert=n),disabled:"disabled"},null,8,["value"])]),_:1}),u(I,{style:{"margin-bottom":"20px"},action:e.action,fileUrl:e.ifParams.alipayPublicCert,onUploadSuccess:a[11]||(a[11]=n=>B(n,"alipayPublicCert"))},{uploadSlot:t(({loading:n})=>[u(k,null,{default:t(()=>[u(T,{type:n?"loading":"upload"},null,8,["type"]),s(" "+R(n?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1}),u(o,{span:"24"},{default:t(()=>[u(c,{label:"\u652F\u4ED8\u5B9D\u6839\u8BC1\u4E66\uFF08.crt\u683C\u5F0F\uFF09",name:"alipayRootCert",class:"margin-botomt-5"},{default:t(()=>[u(g,{value:e.ifParams.alipayRootCert,"onUpdate:value":a[12]||(a[12]=n=>e.ifParams.alipayRootCert=n),disabled:"disabled"},null,8,["value"])]),_:1}),u(I,{action:e.action,style:{"margin-bottom":"20px"},fileUrl:e.ifParams.alipayRootCert,onUploadSuccess:a[13]||(a[13]=n=>B(n,"alipayRootCert"))},{uploadSlot:t(({loading:n})=>[u(k,null,{default:t(()=>[u(T,{type:n?"loading":"upload"},null,8,["type"]),s(" "+R(n?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action","fileUrl"])]),_:1})]),_:1})):e.mchType===2?(m(),P(O,{key:1,gutter:16},{default:t(()=>[u(o,{span:"12"},{default:t(()=>[u(c,{label:"\u5B50\u5546\u6237app_auth_token",name:"appAuthToken"},{default:t(()=>[u(g,{value:e.ifParams.appAuthToken,"onUpdate:value":a[14]||(a[14]=n=>e.ifParams.appAuthToken=n),placeholder:"\u8BF7\u8F93\u5165\u5B50\u5546\u6237app_auth_token"},null,8,["value"])]),_:1})]),_:1})]),_:1})):D("",!0)]),_:1},8,["model","rules"]),L(j)("ENT_MCH_PAY_CONFIG_ADD")?(m(),V("div",Xe,[u(k,{style:{marginRight:"8px"},onClick:r},{default:t(()=>a[24]||(a[24]=[s("\u53D6\u6D88")])),_:1}),u(k,{type:"primary",loading:e.btnLoading,onClick:y},{default:t(()=>a[25]||(a[25]=[s("\u4FDD\u5B58")])),_:1},8,["loading"])])):D("",!0)]),_:1},8,["open"])}}}),ea={style:{"text-align":"center"}},aa=["src"],ua={style:{"margin-top":"10px"}},ta=["href"],la=W({__name:"AlipayAuth",props:{callbackFunc:{type:Function,default:()=>{}}},setup(q,{expose:$}){const{$infoBox:E,$access:j}=Q().appContext.config.globalProperties,U=q,e=G({isShow:!1,appId:"",apiResData:{}});function x(w){e.apiResData={},e.appId=w,he(w).then(F=>{e.apiResData=F,e.isShow=!0})}function b(){E.message.success("\u590D\u5236\u6210\u529F")}function d(){e.isShow=!1,U.callbackFunc&&U.callbackFunc()}return $({show:x}),(w,F)=>{const y=X,i=we,B=Ue("clipboard");return m(),P(i,{open:e.isShow,"onUpdate:open":F[0]||(F[0]=r=>e.isShow=r),title:"\u652F\u4ED8\u5B9D\u5B50\u5546\u6237\u626B\u7801\u6388\u6743",onOk:d,onCancel:d},{default:t(()=>[h("div",ea,[F[5]||(F[5]=h("p",null,[s(" \u65B9\u5F0F1\uFF1A "),h("br"),s(" \u8BF7\u5546\u5BB6\u767B\u5F55\u3010\u652F\u4ED8\u5B9D\u3011APP, \u626B\u63CF\u5982\u4E0B\u4E8C\u7EF4\u7801, \u6309\u63D0\u793A\u6388\u6743\uFF1A ")],-1)),h("img",{style:{"margin-bottom":"10px"},src:e.apiResData.authQrImgUrl},null,8,aa),F[6]||(F[6]=h("hr",null,null,-1)),h("p",ua,[F[2]||(F[2]=s(" \u65B9\u5F0F2\uFF1A ")),F[3]||(F[3]=h("br",null,null,-1)),Oe((m(),P(y,{size:"small",class:"copy-btn"},{default:t(()=>F[1]||(F[1]=[s(" \u70B9\u51FB\u590D\u5236 ")])),_:1})),[[B,e.apiResData.authUrl,"copy"],[B,b,"success"]]),F[4]||(F[4]=s(" \u94FE\u63A5\u5E76\u53D1\u9001\u7ED9\u5546\u6237\uFF0C\u5546\u6237\u8FDB\u5165\u94FE\u63A5\uFF0C\u6309\u7167\u9875\u9762\u63D0\u793A\u81EA\u4E3B\u6388\u6743\uFF1A "))]),h("a",{target:"_blank",href:e.apiResData.authUrl},R(e.apiResData.authUrl),9,ta)])]),_:1},8,["open"])}}});const na={key:0},oa=["src"],sa={class:"title"},ra={class:"cloudpay-card-ops"},ia=["onClick"],pa=["onClick"],da={key:2},fa={key:1},ca={class:"table-page-search-wrapper"},ma={class:"table-page-search-submitButtons"},_a={class:"drawer-btn-center"},ya=W({__name:"MchPayIfConfigList",setup(q,{expose:$}){const{$infoBox:E,$access:j}=Q().appContext.config.globalProperties,U=[{key:"wayCode",title:"\u652F\u4ED8\u65B9\u5F0F\u4EE3\u7801",dataIndex:"wayCode"},{key:"wayName",title:"\u652F\u4ED8\u65B9\u5F0F\u540D\u79F0",dataIndex:"wayName"},{key:"passageState",title:"\u72B6\u6001",scopedSlots:{customRender:"stateSlot"}},{key:"op",title:"\u64CD\u4F5C",width:"200px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],e=M(),x=M(),b=M(),d=M(),w=M(),F=M(),y=M(),i=G({currentStep:0,btnLoading:!1,appId:null,open:!1,cloudpayCard:{height:300,span:{xxl:6,xl:4,lg:4,md:3,sm:2,xs:1}},tableColumns:U,searchData2:{}});function B(_){i.appId=_,i.ifCode=null,i.currentStep=0,i.open=!0,a()}function r(_){i.currentStep=_}function l(){return Y.list(se,{appId:i.appId})}function a(){e.value&&e.value.refCardList()}function p(_){return Y.list(be,Object.assign(_,{appId:i.appId}))}function A(_=!1){x.value.refTable(_)}function c(_){!_||(console.log(_.configPageType,"record.configPageType",_.ifCode),_.subMchIsvConfig===0?E.message.error({title:"\u63D0\u793A",content:"\u5F53\u524D\u5E94\u7528\u6240\u5C5E\u5546\u6237\u4E3A\u7279\u7EA6\u5546\u6237\uFF0C\u8BF7\u5148\u914D\u7F6E\u670D\u52A1\u5546\u652F\u4ED8\u53C2\u6570\uFF01"}):_.configPageType===1?b.value.show(i.appId,_):_.configPageType===2&&(_.ifCode=="wxpay"?w.value.show(i.appId,_):_.ifCode=="alipay"&&F.value.show(i.appId,_)))}function o(_){ve(i.appId,_.wayCode).then(f=>{!f||f.length===0?E.message.error({title:"\u63D0\u793A",content:"\u6682\u65E0\u53EF\u7528\u652F\u4ED8\u63A5\u53E3\u914D\u7F6E"}):d.value.show(i.appId,_.wayCode)})}function C(){i.open=!1}function O(_){if(!!_){if(_.subMchIsvConfig===0)return E.message.error({title:"\u63D0\u793A",content:"\u5F53\u524D\u5E94\u7528\u6240\u5C5E\u5546\u6237\u4E3A\u7279\u7EA6\u5546\u6237\uFF0C\u8BF7\u5148\u914D\u7F6E\u670D\u52A1\u5546\u652F\u4ED8\u53C2\u6570\uFF01"});y.value.show(i.appId)}}return $({show:B}),(_,f)=>{const H=je,g=Te,T=Ce,k=K("a-icon"),I=K("cloudpayCard"),v=ee,n=ae,N=ue,z=X,Pe=te,Ee=Z,Ae=K("cloudpayTableColumns"),Be=K("cloudpayTable"),ke=ge,De=le;return m(),P(De,{open:i.open,"onUpdate:open":f[8]||(f[8]=S=>i.open=S),closable:!0,"body-style":{paddingBottom:"80px"},"drawer-style":{backgroundColor:"#f0f2f5"},width:"80%",onClose:C},{title:t(()=>[u(g,{current:i.currentStep,type:"navigation",style:{width:"80%"}},{default:t(()=>[u(H,{title:"\u652F\u4ED8\u53C2\u6570\u914D\u7F6E",onClick:f[0]||(f[0]=S=>r(0))}),u(H,{title:"\u652F\u4ED8\u901A\u9053\u914D\u7F6E",onClick:f[1]||(f[1]=S=>r(1))})]),_:1},8,["current"])]),default:t(()=>[i.currentStep===0?(m(),V("div",na,[u(I,{ref_key:"infoCard",ref:e,"req-card-list-func":l,span:i.cloudpayCard.span,height:i.cloudpayCard.height},{cardContentSlot:t(({record:S})=>[h("div",{style:J({height:i.cloudpayCard.height+"px"}),class:"cloudpay-card-content"},[h("div",{class:"cloudpay-card-content-header",style:J({backgroundColor:S.bgColor,height:i.cloudpayCard.height/2+"px"})},[S.icon?(m(),V("img",{key:0,src:S.icon,style:J({height:i.cloudpayCard.height/5+"px"})},null,12,oa)):D("",!0)],4),h("div",{class:"cloudpay-card-content-body",style:J({height:i.cloudpayCard.height/2-50+"px"})},[h("div",sa,R(S.ifName),1),u(T,{status:S.ifConfigState===1?"processing":"error",text:S.ifConfigState===1?"\u542F\u7528":"\u672A\u5F00\u901A"},null,8,["status","text"])],4),h("div",ra,[S.mchType==2&&S.ifCode=="alipay"&&L(j)("ENT_MCH_PAY_CONFIG_ADD")?(m(),V("a",{key:0,onClick:ne=>O(S)},[f[9]||(f[9]=s(" \u626B\u7801\u6388\u6743 ")),u(k,{key:"right",type:"right",style:{"font-size":"13px"}})],8,ia)):D("",!0),L(j)("ENT_MCH_PAY_CONFIG_ADD")?(m(),V("a",{key:1,onClick:ne=>c(S)},[f[10]||(f[10]=s(" \u586B\u5199\u53C2\u6570 ")),u(k,{key:"right",type:"right",style:{"font-size":"13px"}})],8,pa)):(m(),V("a",da,"\u6682\u65E0\u64CD\u4F5C"))])],4)]),_:1},8,["span","height"])])):i.currentStep===1?(m(),V("div",fa,[u(ke,null,{default:t(()=>[h("div",ca,[u(Ee,{layout:"inline"},{default:t(()=>[u(Pe,{gutter:4},{default:t(()=>[u(N,{md:9},{default:t(()=>[u(n,{label:""},{default:t(()=>[u(v,{value:i.searchData2.wayCode,"onUpdate:value":f[2]||(f[2]=S=>i.searchData2.wayCode=S),placeholder:"\u652F\u4ED8\u65B9\u5F0F\u4EE3\u7801"},null,8,["value"])]),_:1})]),_:1}),u(N,{md:9},{default:t(()=>[u(n,{label:""},{default:t(()=>[u(v,{value:i.searchData2.wayName,"onUpdate:value":f[3]||(f[3]=S=>i.searchData2.wayName=S),placeholder:"\u652F\u4ED8\u65B9\u5F0F\u540D\u79F0"},null,8,["value"])]),_:1})]),_:1}),u(N,{sm:6},{default:t(()=>[h("span",ma,[u(z,{type:"primary",onClick:f[4]||(f[4]=S=>A(!0))},{default:t(()=>f[11]||(f[11]=[s("\u67E5\u8BE2")])),_:1}),u(z,{style:{"margin-left":"8px"},onClick:f[5]||(f[5]=()=>i.searchData2={})},{default:t(()=>f[12]||(f[12]=[s(" \u91CD\u7F6E ")])),_:1})])]),_:1})]),_:1})]),_:1})]),u(Be,{ref_key:"infoTable",ref:x,"init-data":!0,"req-table-data-func":p,"table-columns":U,"search-data":i.searchData2,"row-key":"wayCode"},{bodyCell:t(({column:S,record:ne})=>[S.key==="passageState"?(m(),P(T,{key:0,status:ne.passageState===0?"error":"processing",text:ne.passageState===0?"\u7981\u7528":"\u542F\u7528"},null,8,["status","text"])):D("",!0),S.key==="op"?(m(),P(Ae,{key:1},{default:t(()=>[L(j)("ENT_MCH_PAY_PASSAGE_CONFIG")?(m(),P(z,{key:0,type:"link",onClick:Pa=>o(ne)},{default:t(()=>f[13]||(f[13]=[s(" \u914D\u7F6E ")])),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)):D("",!0)]),_:1},8,["search-data"])]),_:1})])):D("",!0),h("div",_a,[u(z,{style:{marginRight:"8px"},onClick:C},{default:t(()=>f[14]||(f[14]=[s("\u5173\u95ED")])),_:1}),L(j)("ENT_MCH_PAY_CONFIG_LIST")&&i.currentStep===1?(m(),P(z,{key:0,type:"primary",onClick:f[6]||(f[6]=S=>r(0))},{default:t(()=>f[15]||(f[15]=[s(" \u4E0A\u4E00\u6B65 ")])),_:1})):D("",!0),L(j)("ENT_MCH_PAY_PASSAGE_LIST")&&i.currentStep===0?(m(),P(z,{key:1,type:"primary",onClick:f[7]||(f[7]=S=>r(1))},{default:t(()=>f[16]||(f[16]=[s(" \u4E0B\u4E00\u6B65 ")])),_:1})):D("",!0)]),u(Le,{ref_key:"mchPayConfigAddOrEdit",ref:b,"callback-func":a},null,512),u(Qe,{ref_key:"wxpayPayConfig",ref:w,"callback-func":a},null,512),u(Ze,{ref_key:"alipayPayConfig",ref:F,"callback-func":a},null,512),u(ze,{ref_key:"mchPayPassageAddOrEdit",ref:d,"callback-func":A},null,512),u(la,{ref_key:"alipayAuthPage",ref:y,"callback-func":a},null,512)]),_:1},8,["open"])}}});var Fa=_e(ya,[["__scopeId","data-v-18e9f04c"]]);const va={class:"table-page-search-wrapper"},ba={class:"table-layer"},Ca={class:"table-page-search-submitButtons",style:{"flex-grow":"0","flex-shrink":"0"}},ga={key:0},Ba=W({__name:"List",setup(q){const{$infoBox:$,$access:E}=Q().appContext.config.globalProperties,j=[{key:"appId",fixed:"left",width:"320px",title:"\u5E94\u7528AppId"},{key:"appName",title:"\u5E94\u7528\u540D\u79F0",dataIndex:"appName"},{key:"state",title:"\u72B6\u6001",scopedSlots:{customRender:"stateSlot"}},{key:"createdAt",dataIndex:"createdAt",title:"\u521B\u5EFA\u65E5\u671F"},{key:"op",title:"\u64CD\u4F5C",width:"260px",fixed:"right",align:"center"}],U=M(),e=M(),x=M(),b=G({btnLoading:!1,tableColumns:j,searchData:{}});function d(){b.btnLoading=!0,U.value.refTable(!0)}function w(l){return Y.list(oe,l)}function F(){U.value.refTable(!0)}function y(){e.value.show()}function i(l){e.value.show(l)}function B(l){$.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>{Y.delById(oe,l).then(a=>{$.message.success("\u5220\u9664\u6210\u529F\uFF01"),F()})})}function r(l){x.value.show(l)}return(l,a)=>{const p=K("cloudpay-text-up"),A=Ne,c=Me,o=X,C=Z,O=Ce,_=K("router-link"),f=K("cloudpayTableColumns"),H=K("cloudpayTable"),g=ge,T=K("page-header-wrapper");return m(),P(T,null,{default:t(()=>[u(g,null,{default:t(()=>[h("div",va,[u(C,{layout:"inline",class:"table-head-ground"},{default:t(()=>[h("div",ba,[u(p,{placeholder:"\u5E94\u7528AppId",value:b.searchData.appId,"onUpdate:value":a[0]||(a[0]=k=>b.searchData.appId=k)},null,8,["value"]),u(p,{placeholder:"\u5E94\u7528\u540D\u79F0",value:b.searchData.appName,"onUpdate:value":a[1]||(a[1]=k=>b.searchData.appName=k)},null,8,["value"]),u(c,{value:b.searchData.state,"onUpdate:value":a[2]||(a[2]=k=>b.searchData.state=k),placeholder:"\u72B6\u6001",class:"table-head-layout"},{default:t(()=>[u(A,{value:""},{default:t(()=>a[5]||(a[5]=[s("\u5168\u90E8")])),_:1}),u(A,{value:"0"},{default:t(()=>a[6]||(a[6]=[s("\u7981\u7528")])),_:1}),u(A,{value:"1"},{default:t(()=>a[7]||(a[7]=[s("\u542F\u7528")])),_:1})]),_:1},8,["value"]),h("span",Ca,[u(o,{type:"primary",onClick:d,loading:b.btnLoading},{default:t(()=>a[8]||(a[8]=[s(" \u67E5\u8BE2 ")])),_:1},8,["loading"]),u(o,{style:{"margin-left":"8px"},onClick:a[3]||(a[3]=()=>b.searchData={})},{default:t(()=>a[9]||(a[9]=[s(" \u91CD\u7F6E ")])),_:1})])])]),_:1})]),u(H,{onBtnLoadClose:a[4]||(a[4]=k=>b.btnLoading=!1),ref_key:"infoTable",ref:U,initData:!0,reqTableDataFunc:w,tableColumns:b.tableColumns,searchData:b.searchData,rowKey:"appId"},{opRow:t(()=>[L(E)("ENT_MCH_APP_ADD")?(m(),P(o,{key:0,type:"primary",onClick:y},{default:t(()=>a[10]||(a[10]=[s(" \u65B0\u5EFA ")])),_:1})):D("",!0)]),bodyCell:t(({column:k,record:I})=>[k.key==="appId"?(m(),V("b",ga,R(I.appId),1)):D("",!0),k.key==="state"?(m(),P(O,{key:1,status:I.state===0?"error":"processing",text:I.state===0?"\u7981\u7528":"\u542F\u7528"},null,8,["status","text"])):D("",!0),k.key==="op"?(m(),P(f,{key:2},{default:t(()=>[L(E)("ENT_MCH_APP_EDIT")?(m(),P(o,{key:0,type:"link",onClick:v=>i(I.appId)},{default:t(()=>a[11]||(a[11]=[s(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])):D("",!0),L(E)("ENT_MCH_PAY_CONFIG_LIST")&&I.state?(m(),P(o,{key:1,type:"link",onClick:v=>r(I.appId)},{default:t(()=>a[12]||(a[12]=[s(" \u652F\u4ED8\u914D\u7F6E ")])),_:2},1032,["onClick"])):D("",!0),L(E)("ENT_MCH_PAY_TEST")&&I.state?(m(),P(o,{key:2,type:"link"},{default:t(()=>[u(_,{to:{name:"ENT_MCH_PAY_TEST",params:{appId:I.appId}}},{default:t(()=>a[13]||(a[13]=[s(" \u652F\u4ED8\u6D4B\u8BD5 ")])),_:2},1032,["to"])]),_:2},1024)):D("",!0),L(E)("ENT_MCH_TRANSFER")&&I.state?(m(),P(o,{key:3,type:"link"},{default:t(()=>[u(_,{to:{name:"ENT_MCH_TRANSFER",params:{appId:I.appId}}},{default:t(()=>a[14]||(a[14]=[s(" \u53D1\u8D77\u8F6C\u8D26 ")])),_:2},1032,["to"])]),_:2},1024)):D("",!0),L(E)("ENT_MCH_APP_DEL")?(m(),P(o,{key:4,type:"link",style:{color:"red"},onClick:v=>B(I.appId)},{default:t(()=>a[15]||(a[15]=[s(" \u5220\u9664 ")])),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)):D("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),u(Ke,{ref_key:"mchAppAddOrEditRef",ref:e,callbackFunc:F},null,512),u(Fa,{ref_key:"mchPayIfConfigList",ref:x},null,512)]),_:1})}}});export{Ba as default};
