import{s as o}from"./index.fba97cfa.js";const i={list:(e,r)=>o.request({url:e,method:"GET",params:r},!0,!0,!1),add:(e,r)=>o.request({url:e,method:"POST",data:r},!0,!0,!1),getById:(e,r)=>o.request({url:e+"/"+r,method:"GET"},!0,!0,!1),updateById:(e,r,s)=>o.request({url:e+"/"+r,method:"PUT",data:s},!0,!0,!1),delById:(e,r)=>o.request({url:e+"/"+r,method:"DELETE"},!0,!0,!1)},t={list:(e,r)=>o.request({url:e,method:"GET",params:r},!0,!0,!0),add:(e,r)=>o.request({url:e,method:"POST",data:r},!0,!0,!0),getById:(e,r)=>o.request({url:e+"/"+r,method:"GET"},!0,!0,!0),updateById:(e,r,s)=>o.request({url:e+"/"+r,method:"PUT",data:s},!0,!0,!0),delById:(e,r)=>o.request({url:e+"/"+r,method:"DELETE"},!0,!0,!0)},d="/api/sysRoles",p="/api/sysRoleEntRelas",l="/api/sysUsers",m="/api/sysUserRoleRelas",n="api/mainChart",_="/api/mchApps",c="/api/payOrder",u="/api/refundOrder",C="/api/payWays",E="/api/mch/payConfigs",S="/api/mch/payPassages",P="/api/transferOrders",D="/api/divisionReceiverGroups",R="/api/divisionReceivers",I="/api/division/records",A={avatar:o.baseUrl+"/api/ossFiles/avatar",cert:o.baseUrl+"/api/ossFiles/cert"};function f(){return o.request({url:"/api/sysEnts/showTree",method:"GET"})}function O(e,r){return o.request({url:"api/sysUserRoleRelas/relas/"+e,method:"POST",data:{roleIdListStr:JSON.stringify(r)}})}function g(e,r){return o.request({url:"/api/mch/payConfigs/"+e+"/"+r,method:"get"})}function U(e){return o.request({url:"api/paytest/payways/"+e,method:"GET"})}function y(e){return o.request({url:"/api/paytest/payOrders",method:"POST",data:e})}function T(e,r){return o.request({url:"/api/mch/payPassages/availablePayInterface/"+e+"/"+r,method:"GET"})}function N(){return o.request({url:n+"/payAmountWeek",method:"GET"})}function w(){return o.request({url:n+"/numCount",method:"GET"})}function h(e){return o.request({url:n+"/payCount",method:"GET",params:e})}function L(e){return o.request({url:n+"/payTypeCount",method:"GET",params:e})}function M(){return o.request({url:n,method:"GET"})}function F(e){return o.request({url:"/api/current/modifyPwd",method:"put",data:e})}function W(e){return o.request({url:"/api/current/user",method:"put",data:e})}function b(){return o.request({url:"/api/current/user",method:"get"})}function G(){let e=document.location.protocol+"//"+document.location.host;return{ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133960787392575276",ChocolateyToolsLocation:"C:\\tools",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_13148_RKYDILSLPDZYPHAZ",CLAUDE_CODE_SSE_PORT:"51783",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"WINDFRED",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11832_1592913036:"1",ENABLE_IDE_INTEGRATION:"true",ERLANG_HOME:"C:\\Program Files\\Erlang OTP","export GEMINI_API_KEY":"AIzaSyDYfNZ2N47r6qGBQllkkEecVUD1m6Nxfsc",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",GOPATH:"C:\\Users\\<USER>\\go",HOME:"C:\\Users\\<USER>\\Users\\winfred",INIT_CWD:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant",JAVA_HOME:"C:\\\\Program Files\\\\Microsoft\\\\jdk-*********-hotspot",LANG:"zh_CN.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\WINDFRED",NODE:"D:\\node.js\\node.exe",NODE_ENV:"production",npm_command:"run-script",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_local_prefix:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant",npm_config_node_gyp:"D:\\node.js\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"D:\\node.js\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"vite build",npm_node_execpath:"D:\\node.js\\node.exe",npm_package_json:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant\\package.json",npm_package_name:"cloudpay-ui-merchant",npm_package_version:"3.1.0",NUMBER_OF_PROCESSORS:"8",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;D:\\node.js\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;d:\\cursor\\resources\\app\\bin;D:\\node.js\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;C:\\Program Files\\Multipass\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Oracle\\VirtualBox;C:\\Program Files\\Go\\bin;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Git\\cmd;d:\\Trae\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;C:\\Program Files\\Multipass\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\Windsurf\\bin;C:\\Users\\<USER>\\go\\bin;C:\\tools\\mysql\\current\\bin;D:\\Kiro\\bin;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 140 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"8c02",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.98.2",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"windfred",USERDOMAIN_ROAMINGPROFILE:"windfred",USERNAME:"winfred",USERPROFILE:"C:\\Users\\<USER>\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-2a09466a4f-sock",VSCODE_INJECTION:"1",windir:"C:\\WINDOWS",ZES_ENABLE_SYSMAN:"1",VITE_APP_BASE_URL:"/",VITE_API_BASE_URL:"",VITE_USER_NODE_ENV:"production"}.VUE_APP_API_BASE_URL&&{ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133960787392575276",ChocolateyToolsLocation:"C:\\tools",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_13148_RKYDILSLPDZYPHAZ",CLAUDE_CODE_SSE_PORT:"51783",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"WINDFRED",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11832_1592913036:"1",ENABLE_IDE_INTEGRATION:"true",ERLANG_HOME:"C:\\Program Files\\Erlang OTP","export GEMINI_API_KEY":"AIzaSyDYfNZ2N47r6qGBQllkkEecVUD1m6Nxfsc",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",GOPATH:"C:\\Users\\<USER>\\go",HOME:"C:\\Users\\<USER>\\Users\\winfred",INIT_CWD:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant",JAVA_HOME:"C:\\\\Program Files\\\\Microsoft\\\\jdk-*********-hotspot",LANG:"zh_CN.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\WINDFRED",NODE:"D:\\node.js\\node.exe",NODE_ENV:"production",npm_command:"run-script",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_local_prefix:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant",npm_config_node_gyp:"D:\\node.js\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"D:\\node.js\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"vite build",npm_node_execpath:"D:\\node.js\\node.exe",npm_package_json:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant\\package.json",npm_package_name:"cloudpay-ui-merchant",npm_package_version:"3.1.0",NUMBER_OF_PROCESSORS:"8",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;D:\\node.js\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;d:\\cursor\\resources\\app\\bin;D:\\node.js\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;C:\\Program Files\\Multipass\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Oracle\\VirtualBox;C:\\Program Files\\Go\\bin;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Git\\cmd;d:\\Trae\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;C:\\Program Files\\Multipass\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\Windsurf\\bin;C:\\Users\\<USER>\\go\\bin;C:\\tools\\mysql\\current\\bin;D:\\Kiro\\bin;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 140 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"8c02",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.98.2",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"windfred",USERDOMAIN_ROAMINGPROFILE:"windfred",USERNAME:"winfred",USERPROFILE:"C:\\Users\\<USER>\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-2a09466a4f-sock",VSCODE_INJECTION:"1",windir:"C:\\WINDOWS",ZES_ENABLE_SYSMAN:"1",VITE_APP_BASE_URL:"/",VITE_API_BASE_URL:"",VITE_USER_NODE_ENV:"production"}.VUE_APP_API_BASE_URL!=="/"&&(e={ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133960787392575276",ChocolateyToolsLocation:"C:\\tools",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_13148_RKYDILSLPDZYPHAZ",CLAUDE_CODE_SSE_PORT:"51783",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"WINDFRED",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11832_1592913036:"1",ENABLE_IDE_INTEGRATION:"true",ERLANG_HOME:"C:\\Program Files\\Erlang OTP","export GEMINI_API_KEY":"AIzaSyDYfNZ2N47r6qGBQllkkEecVUD1m6Nxfsc",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",GIT_ASKPASS:"d:\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",GOPATH:"C:\\Users\\<USER>\\go",HOME:"C:\\Users\\<USER>\\Users\\winfred",INIT_CWD:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant",JAVA_HOME:"C:\\\\Program Files\\\\Microsoft\\\\jdk-*********-hotspot",LANG:"zh_CN.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\WINDFRED",NODE:"D:\\node.js\\node.exe",NODE_ENV:"production",npm_command:"run-script",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_local_prefix:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant",npm_config_node_gyp:"D:\\node.js\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"D:\\node.js\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"vite build",npm_node_execpath:"D:\\node.js\\node.exe",npm_package_json:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant\\package.json",npm_package_name:"cloudpay-ui-merchant",npm_package_version:"3.1.0",NUMBER_OF_PROCESSORS:"8",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\cloudpay-ui-merchant\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\cloudpay-ui\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\cloudpay-2\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\cloudpay-2\\node_modules\\.bin;C:\\Users\\<USER>\\Downloads\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;D:\\node.js\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;d:\\cursor\\resources\\app\\bin;D:\\node.js\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;C:\\Program Files\\Multipass\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Oracle\\VirtualBox;C:\\Program Files\\Go\\bin;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Git\\cmd;d:\\Trae\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\Microsoft VS Code\\bin;D:\\cursor\\resources\\app\\bin;C:\\Program Files\\Multipass\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\Windsurf\\bin;C:\\Users\\<USER>\\go\\bin;C:\\tools\\mysql\\current\\bin;D:\\Kiro\\bin;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 140 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"8c02",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.98.2",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"windfred",USERDOMAIN_ROAMINGPROFILE:"windfred",USERNAME:"winfred",USERPROFILE:"C:\\Users\\<USER>\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"D:\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-2a09466a4f-sock",VSCODE_INJECTION:"1",windir:"C:\\WINDOWS",ZES_ENABLE_SYSMAN:"1",VITE_APP_BASE_URL:"/",VITE_API_BASE_URL:"",VITE_USER_NODE_ENV:"production"}.VUE_APP_API_BASE_URL),e.startsWith("https:")?"wss://"+e.replace("https://",""):"ws://"+e.replace("http://","")}function V(e){return o.request({url:"/api/mch/payConfigs/alipayIsvsubMchAuthUrls/"+e,method:"GET"})}function x(e){return o.request({url:"api/mchTransfers/ifCodes/"+e,method:"GET"})}function v(e,r,s){return o.request({url:"/api/mchTransfers/channelUserId",method:"GET",params:{ifCode:e,appId:r,extParam:s}})}function B(e){return o.request({url:"/api/mchTransfers/doTransfer",method:"POST",data:e},!0,!0,!0)}function H(e){return o.request({url:"/api/mch/payConfigs/ifCodes/"+e,method:"GET"},!0,!0,!0)}function k(e,r,s){return o.request({url:"/api/payOrder/refunds/"+e,method:"POST",data:{refundAmount:r,refundReason:s}})}function q(e){return o.request({url:"/api/division/records/resend/"+e,method:"POST"})}export{l as A,B,v as C,c as D,k as E,C as F,u as G,P as H,D as I,H as J,R as K,I as L,q as M,W as a,F as b,N as c,w as d,h as e,L as f,b as g,M as h,t as i,d as j,m as k,O as l,f as m,p as n,_ as o,g as p,E as q,i as r,T as s,S as t,A as u,V as v,G as w,U as x,y,x as z};
