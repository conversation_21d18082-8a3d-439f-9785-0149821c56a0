import{r as R,A as L,h as Q,i as ae,j as ue,k as ne}from"./manage.6e729324.js";import{d as j,u as W,bs as G,b as t,az as H,H as le,g as K,r as q,h as M,aE as w,w as a,l as oe,o as m,j as d,aF as b,a as B,c as X,aG as re,aH as de,I as ie,m as ce,bz as ee,n as pe,B as J,F as te,bA as fe,i as $,aw as I,bB as _e}from"./index.8746381c.js";import{_ as me}from"./index.5e527ed3.js";import{_ as se}from"./index.9b74c380.js";import{_ as ve,a as Fe}from"./index.7c25015e.js";import{R as ge,_ as we}from"./Group.170fc6be.js";import{_ as be}from"./index.54e910b7.js";import{C as O}from"./Card.d6389e0b.js";import"./index.8f4a8fa1.js";import"./TabPane.9792ea88.js";import"./useMergedState.8a9045a6.js";import"./index.4c901be3.js";const ye=()=>({prefixCls:String,title:H(),description:H(),avatar:H()});var Y=j({compatConfig:{MODE:3},name:"ACardMeta",props:ye(),slots:Object,setup(c,y){let{slots:v}=y;const{prefixCls:p}=W("card",c);return()=>{const U={[`${p.value}-meta`]:!0},r=G(v,c,"avatar"),e=G(v,c,"title"),F=G(v,c,"description"),P=r?t("div",{class:`${p.value}-meta-avatar`},[r]):null,D=e?t("div",{class:`${p.value}-meta-title`},[e]):null,_=F?t("div",{class:`${p.value}-meta-description`},[F]):null,n=D||_?t("div",{class:`${p.value}-meta-detail`},[D,_]):null;return t("div",{class:U},[P,n])}}});const Pe=()=>({prefixCls:String,hoverable:{type:Boolean,default:!0}});var Z=j({compatConfig:{MODE:3},name:"ACardGrid",__ANT_CARD_GRID:!0,props:Pe(),setup(c,y){let{slots:v}=y;const{prefixCls:p}=W("card",c),U=le(()=>({[`${p.value}-grid`]:!0,[`${p.value}-grid-hoverable`]:c.hoverable}));return()=>{var r;return t("div",{class:U.value},[(r=v.default)===null||r===void 0?void 0:r.call(v)])}}});O.Meta=Y;O.Grid=Z;O.install=function(c){return c.component(O.name,O),c.component(Y.name,Y),c.component(Z.name,Z),c};const Ce={style:{display:"flex","flex-direction":"row"}},Ee={key:1},ke={class:"drawer-btn-center"},Be=j({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>({})}},setup(c,{expose:y}){const{$infoBox:v,$access:p,$hasAgentEnt:U}=K().appContext.config.globalProperties,r=c,e=q({newPwd:"",resetIsShow:!1,sysPassword:{resetPass:!1,defaultPass:!0,confirmPwd:""},loading:!1,value:1,confirmLoading:!1,isAdd:!0,isShow:!1,saveObject:{},recordId:null}),F=M(),P=q({realname:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u59D3\u540D",trigger:"blur"}],telphone:[{required:!0,pattern:/^[1][0-9]{10}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}],userNo:[{required:!0,message:"\u8BF7\u8F93\u5165\u7F16\u53F7",trigger:"blur"}],loginUsername:[],newPwd:[{required:!1,trigger:"blur"},{validator:(g,s)=>!e.sysPassword.defaultPass&&(e.newPwd.length<6||e.newPwd.length>12)?Promise.reject("\u8BF7\u8F93\u51656-12\u4F4D\u65B0\u5BC6\u7801"):Promise.resolve()}],confirmPwd:[{required:!1,trigger:"blur"},{validator:(g,s)=>e.sysPassword.defaultPass||e.newPwd===e.sysPassword.confirmPwd?Promise.resolve():Promise.reject("\u65B0\u5BC6\u7801\u4E0E\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4")}]});function D(g){F.value&&F.value.resetFields(),e.isAdd=!g,e.saveObject={isAdmin:1,state:1,sex:1},P.loginUsername=[],e.confirmLoading=!1,e.isAdd&&P.loginUsername.push({required:!0,pattern:/^[a-zA-Z][a-zA-Z0-9]{5,17}$/,message:"\u8BF7\u8F93\u5165\u5B57\u6BCD\u5F00\u5934\uFF0C\u957F\u5EA6\u4E3A6-18\u4F4D\u7684\u767B\u5F55\u540D",trigger:"blur"}),e.isAdd||(e.resetIsShow=!0,e.recordId=g,R.getById(L,g).then(s=>{e.saveObject=s})),e.isShow=!0}function _(){F.value.validate().then(g=>{g&&(e.loading=!0,e.confirmLoading=!0,e.isAdd?R.add(L,e.saveObject).then(s=>{v.message.success("\u65B0\u589E\u6210\u529F"),e.isShow=!1,e.loading=!1,r.callbackFunc()}).catch(s=>{e.confirmLoading=!1}):(e.sysPassword.confirmPwd=oe.encode(e.sysPassword.confirmPwd),Object.assign(e.saveObject,e.sysPassword),R.updateById(L,e.recordId,e.saveObject).then(s=>{v.message.success("\u4FEE\u6539\u6210\u529F"),e.isShow=!1,r.callbackFunc(),e.resetIsShow=!1,e.sysPassword.resetPass=!1,e.sysPassword.defaultPass=!0,k()}).catch(s=>{e.confirmLoading=!1,e.resetIsShow=!1,e.sysPassword.resetPass=!1,e.sysPassword.defaultPass=!0,k()})))})}function n(){e.isShow=!1,e.resetIsShow=!1,k(),e.sysPassword.resetPass=!1,e.sysPassword.defaultPass=!0}function C(){e.sysPassword.defaultPass||(e.newPwd="",e.sysPassword.confirmPwd="")}function k(){e.newPwd="",e.sysPassword.confirmPwd=""}return y({show:D}),(g,s)=>{const o=ie,u=ce,i=ve,f=ge,S=we,A=Fe,x=be,z=me,T=ee,N=pe,V=J,E=te,h=se;return m(),w(h,{title:e.isAdd?"\u65B0\u589E\u64CD\u4F5C\u5458":"\u4FEE\u6539\u64CD\u4F5C\u5458",placement:"right",closable:!0,onOk:_,open:e.isShow,"onUpdate:open":s[11]||(s[11]=l=>e.isShow=l),width:"600",onClose:n,maskClosable:!1},{default:a(()=>[t(E,{ref_key:"infoFormModel",ref:F,model:e.saveObject,layout:"vertical",rules:P,style:{"padding-bottom":"50px"}},{default:a(()=>[t(A,{justify:"space-between",type:"flex"},{default:a(()=>[t(i,{span:10},{default:a(()=>[t(u,{label:"\u7528\u6237\u767B\u5F55\u540D:",name:"loginUsername"},{default:a(()=>[t(o,{value:e.saveObject.loginUsername,"onUpdate:value":s[0]||(s[0]=l=>e.saveObject.loginUsername=l),disabled:!e.isAdd},null,8,["value","disabled"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u7528\u6237\u59D3\u540D\uFF1A",name:"realname"},{default:a(()=>[t(o,{value:e.saveObject.realname,"onUpdate:value":s[1]||(s[1]=l=>e.saveObject.realname=l)},null,8,["value"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u624B\u673A\u53F7\uFF1A",name:"telphone"},{default:a(()=>[t(o,{value:e.saveObject.telphone,"onUpdate:value":s[2]||(s[2]=l=>e.saveObject.telphone=l)},null,8,["value"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u7F16\u53F7\uFF1A",name:"userNo"},{default:a(()=>[t(o,{value:e.saveObject.userNo,"onUpdate:value":s[3]||(s[3]=l=>e.saveObject.userNo=l)},null,8,["value"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u8BF7\u9009\u62E9\u6027\u522B\uFF1A",name:"sex"},{default:a(()=>[t(S,{value:e.saveObject.sex,"onUpdate:value":s[4]||(s[4]=l=>e.saveObject.sex=l)},{default:a(()=>[t(f,{value:1},{default:a(()=>s[12]||(s[12]=[d("\u7537")])),_:1,__:[12]}),t(f,{value:2},{default:a(()=>s[13]||(s[13]=[d("\u5973")])),_:1,__:[13]})]),_:1},8,["value"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u662F\u5426\u4E3A\u8D85\u7EA7\u7BA1\u7406\u5458\uFF1A",name:"isAdmin"},{default:a(()=>[t(S,{value:e.saveObject.isAdmin,"onUpdate:value":s[5]||(s[5]=l=>e.saveObject.isAdmin=l)},{default:a(()=>[t(f,{value:1},{default:a(()=>s[14]||(s[14]=[d("\u662F")])),_:1,__:[14]}),t(f,{value:0},{default:a(()=>s[15]||(s[15]=[d("\u5426")])),_:1,__:[15]})]),_:1},8,["value"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u72B6\u6001\uFF1A",name:"state"},{default:a(()=>[t(S,{value:e.saveObject.state,"onUpdate:value":s[6]||(s[6]=l=>e.saveObject.state=l)},{default:a(()=>[t(f,{value:1},{default:a(()=>s[16]||(s[16]=[d("\u542F\u7528")])),_:1,__:[16]}),t(f,{value:0},{default:a(()=>s[17]||(s[17]=[d("\u505C\u7528")])),_:1,__:[17]})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e.resetIsShow?(m(),w(z,{key:0,orientation:"left"},{default:a(()=>[t(x,{color:"#FF4B33"},{default:a(()=>s[18]||(s[18]=[d("\u8D26\u6237\u5B89\u5168")])),_:1,__:[18]})]),_:1})):b("",!0),B("div",Ce,[t(A,{justify:"space-between",type:"flex",style:{width:"100%"}},{default:a(()=>[t(i,{span:10},{default:a(()=>[e.resetIsShow?(m(),w(u,{key:0,label:""},{default:a(()=>[s[19]||(s[19]=d(" \u91CD\u7F6E\u5BC6\u7801\uFF1A ")),t(T,{checked:e.sysPassword.resetPass,"onUpdate:checked":s[7]||(s[7]=l=>e.sysPassword.resetPass=l)},null,8,["checked"])]),_:1,__:[19]})):b("",!0)]),_:1}),t(i,{span:10},{default:a(()=>[e.sysPassword.resetPass?(m(),w(u,{key:0,label:""},{default:a(()=>[s[20]||(s[20]=d(" \u6062\u590D\u9ED8\u8BA4\u5BC6\u7801\uFF1A ")),t(T,{checked:e.sysPassword.defaultPass,"onUpdate:checked":s[8]||(s[8]=l=>e.sysPassword.defaultPass=l),onClick:C},null,8,["checked"])]),_:1,__:[20]})):b("",!0)]),_:1})]),_:1})]),e.sysPassword.resetPass?(m(),X("div",Ee,[re(B("div",null,[t(A,{justify:"space-between",type:"flex"},{default:a(()=>[t(i,{span:10},{default:a(()=>[t(u,{label:"\u65B0\u5BC6\u7801\uFF1A",name:"newPwd"},{default:a(()=>[t(N,{autocomplete:"new-password",value:e.newPwd,"onUpdate:value":s[9]||(s[9]=l=>e.newPwd=l),disabled:e.sysPassword.defaultPass},null,8,["value","disabled"])]),_:1})]),_:1}),t(i,{span:10},{default:a(()=>[t(u,{label:"\u786E\u8BA4\u65B0\u5BC6\u7801\uFF1A",name:"confirmPwd"},{default:a(()=>[t(N,{autocomplete:"new-password",value:e.sysPassword.confirmPwd,"onUpdate:value":s[10]||(s[10]=l=>e.sysPassword.confirmPwd=l),disabled:e.sysPassword.defaultPass},null,8,["value","disabled"])]),_:1})]),_:1})]),_:1})],512),[[de,!e.sysPassword.defaultPass]])])):b("",!0),B("div",ke,[t(V,{style:{marginRight:"8px"},onClick:n},{default:a(()=>s[21]||(s[21]=[d("\u53D6\u6D88")])),_:1,__:[21]}),t(V,{type:"primary",onClick:_,loading:e.confirmLoading},{default:a(()=>s[22]||(s[22]=[d(" \u4FDD\u5B58 ")])),_:1,__:[22]},8,["loading"])])]),_:1},8,["model","rules"])]),_:1},8,["title","open"])}}}),De={style:{borderBottom:"1px solid #E9E9E9"}},Ae={class:"drawer-btn-center"},he=j({__name:"RoleDist",props:{callbackFunc:{type:Function,default:()=>{}}},setup(c,{expose:y}){const{$infoBox:v,$access:p,$hasAgentEnt:U}=K().appContext.config.globalProperties,r=c,e=q({confirmLoading:!1,isShow:!1,recordId:null,allRoleList:[],checkedVal:[]});function F(_){e.allRoleList=[],e.checkedVal=[],e.confirmLoading=!1,e.recordId=_,Q.list(ae,{pageSize:-1}).then(n=>{if(n.total<=0)return v.message.error("\u5F53\u524D\u6682\u65E0\u89D2\u8272\uFF0C\u8BF7\u5148\u884C\u6DFB\u52A0");e.allRoleList=[],n.records.map(C=>{e.allRoleList.push({label:C.roleName,value:C.roleId})}),e.isShow=!0,R.list(ue,{pageSize:-1,userId:_}).then(C=>{C.records.map(k=>{e.checkedVal.push(k.roleId)})})})}function P(){e.confirmLoading=!0,ne(e.recordId,e.checkedVal).then(_=>{v.message.success("\u66F4\u65B0\u6210\u529F\uFF01"),e.isShow=!1,e.callbackFunc!==void 0&&r.callbackFunc()}).catch(()=>{e.confirmLoading=!1})}function D(_){e.checkedVal=[],_.target.checked&&e.allRoleList.map(n=>{e.checkedVal.push(n.value)})}return y({show:F}),(_,n)=>{const C=ee,k=fe,g=J,s=se;return m(),w(s,{open:e.isShow,"onUpdate:open":n[2]||(n[2]=o=>e.isShow=o),title:"\u5206\u914D\u89D2\u8272",width:"30%",maskClosable:!0,onClose:n[3]||(n[3]=o=>e.isShow=!1)},{default:a(()=>[B("div",null,[B("div",De,[t(C,{indeterminate:e.checkedVal.length&&e.allRoleList.length!=e.checkedVal.length,checked:e.checkedVal.length&&e.allRoleList.length==e.checkedVal.length,onChange:D},{default:a(()=>n[4]||(n[4]=[d(" \u5168\u9009 ")])),_:1,__:[4]},8,["indeterminate","checked"])]),n[5]||(n[5]=B("br",null,null,-1)),t(k,{value:e.checkedVal,"onUpdate:value":n[0]||(n[0]=o=>e.checkedVal=o),options:e.allRoleList},null,8,["value","options"])]),B("div",Ae,[t(g,{style:{marginRight:"8px"},onClick:n[1]||(n[1]=o=>e.isShow=!1)},{default:a(()=>n[6]||(n[6]=[d("\u53D6\u6D88")])),_:1,__:[6]}),t(g,{type:"primary",onClick:P,loading:e.confirmLoading},{default:a(()=>n[7]||(n[7]=[d("\u4FDD\u5B58")])),_:1,__:[7]},8,["loading"])])]),_:1},8,["open"])}}}),Se={key:0,class:"table-page-search-wrapper"},xe={class:"table-layer"},Ue={class:"table-page-search-submitButtons"},Ge=j({__name:"SysUserPage",setup(c){const{$infoBox:y,$SYS_NAME_MAP:v,$access:p}=K().appContext.config.globalProperties,r=q({tableColumns:[{title:"\u7528\u6237ID",dataIndex:"sysUserId",fixed:"left"},{title:"\u59D3\u540D",dataIndex:"realname"},{title:"\u6027\u522B",dataIndex:"sex",customRender:({text:o,record:u,index:i,column:f})=>u.sex===1?"\u7537":u.sex===2?"\u5973":"\u672A\u77E5"},{title:"\u5934\u50CF",key:"avatarUrl",scopedSlots:{customRender:"avatarSlot"}},{title:"\u7F16\u53F7",dataIndex:"userNo"},{title:"\u624B\u673A\u53F7",dataIndex:"telphone"},{title:"\u8D85\u7BA1",dataIndex:"isAdmin",customRender:({text:o,record:u,index:i,column:f})=>u.isAdmin===1?"\u662F":"\u5426"},{title:"\u72B6\u6001",key:"state",align:"center"},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt"},{title:"\u4FEE\u6539\u65F6\u95F4",dataIndex:"updatedAt"},{key:"op",title:"\u64CD\u4F5C",width:200,fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],searchData:{},btnLoading:!1}),e=M(),F=M(),P=M();function D(o){return R.list(L,o)}function _(){r.btnLoading=!0,e.value.refTable(!0)}function n(){F.value.show()}function C(o){F.value.show(o)}function k(o){y.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>R.delById(L,o).then(u=>{y.message.success("\u5220\u9664\u6210\u529F\uFF01"),e.value.refTable(!1)}))}function g(o){P.value.show(o)}function s(o,u){const i=u===1?"\u786E\u8BA4[\u542F\u7528]\u8BE5\u7528\u6237\uFF1F":"\u786E\u8BA4[\u505C\u7528]\u8BE5\u7528\u6237\uFF1F",f=u===1?"\u542F\u7528\u540E\u7528\u6237\u53EF\u8FDB\u884C\u767B\u9646\u7B49\u4E00\u7CFB\u5217\u64CD\u4F5C":"\u505C\u7528\u540E\u8BE5\u7528\u6237\u5C06\u7ACB\u5373\u9000\u51FA\u7CFB\u7EDF\u5E76\u4E0D\u53EF\u518D\u6B21\u767B\u9646";return new Promise((S,A)=>{y.confirmDanger(i,f,()=>Q.updateById(L,o,{state:u}).then(x=>{_(),S(x)}).catch(x=>A(x)),()=>{A(new Error)})})}return(o,u)=>{const i=$("cloudpay-text-up"),f=J,S=te,A=_e,x=$("cloudpayTableColState"),z=$("cloudpayTableColumns"),T=$("cloudpayTable"),N=O,V=$("page-header-wrapper");return m(),w(V,null,{default:a(()=>[t(N,null,{default:a(()=>[I(p)("ENT_UR_USER_SEARCH")?(m(),X("div",Se,[t(S,{layout:"inline",class:"table-head-ground"},{default:a(()=>[B("div",xe,[t(i,{placeholder:"\u7528\u6237ID",value:r.searchData.sysUserId,"onUpdate:value":u[0]||(u[0]=E=>r.searchData.sysUserId=E)},null,8,["value"]),t(i,{placeholder:"\u7528\u6237\u59D3\u540D",value:r.searchData.realname,"onUpdate:value":u[1]||(u[1]=E=>r.searchData.realname=E)},null,8,["value"]),B("span",Ue,[t(f,{type:"primary",onClick:_,loading:r.btnLoading},{default:a(()=>u[4]||(u[4]=[d(" \u67E5\u8BE2 ")])),_:1,__:[4]},8,["loading"]),t(f,{style:{"margin-left":"8px"},onClick:u[2]||(u[2]=()=>r.searchData={})},{default:a(()=>u[5]||(u[5]=[d(" \u91CD\u7F6E ")])),_:1,__:[5]})])])]),_:1})])):b("",!0),t(T,{onBtnLoadClose:u[3]||(u[3]=E=>r.btnLoading=!1),ref_key:"infoTable",ref:e,initData:!0,reqTableDataFunc:D,tableColumns:r.tableColumns,searchData:r.searchData,rowKey:"sysUserId"},{opRow:a(()=>[I(p)("ENT_UR_USER_ADD")?(m(),w(f,{key:0,type:"primary",onClick:n,class:"mg-b-30"},{default:a(()=>u[6]||(u[6]=[d(" \u65B0\u5EFA ")])),_:1,__:[6]})):b("",!0)]),bodyCell:a(({column:E,record:h})=>[E.key==="avatarUrl"?(m(),w(A,{key:0,size:"default",src:h.avatarUrl},null,8,["src"])):b("",!0),E.key==="state"?(m(),w(x,{key:1,state:h.state,showSwitchType:I(p)("ENT_UR_USER_EDIT"),onChange:l=>s(h.sysUserId,l)},null,8,["state","showSwitchType","onChange"])):b("",!0),E.key==="op"?(m(),w(z,{key:2},{default:a(()=>[I(p)("ENT_UR_USER_UPD_ROLE")?(m(),w(f,{key:0,type:"link",onClick:l=>g(h.sysUserId)},{default:a(()=>u[7]||(u[7]=[d(" \u53D8\u66F4\u89D2\u8272 ")])),_:2,__:[7]},1032,["onClick"])):b("",!0),I(p)("ENT_UR_USER_EDIT")?(m(),w(f,{key:1,type:"link",onClick:l=>C(h.sysUserId)},{default:a(()=>u[8]||(u[8]=[d(" \u4FEE\u6539 ")])),_:2,__:[8]},1032,["onClick"])):b("",!0),I(p)("ENT_UR_USER_DELETE")?(m(),w(f,{key:2,danger:"",type:"link",onClick:l=>k(h.sysUserId)},{default:a(()=>u[9]||(u[9]=[d(" \u5220\u9664 ")])),_:2,__:[9]},1032,["onClick"])):b("",!0)]),_:2},1024)):b("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),t(Be,{ref_key:"infoAddOrEdit",ref:F,callbackFunc:_},null,512),t(he,{ref_key:"roleDistRef",ref:P},null,512)]),_:1})}}});export{Ge as default};
