import{R as m}from"./reconnecting-websocket.9353e695.js";import{C as f,w as x}from"./manage.2dfb5a24.js";import{_ as b,e as h,r as g,o as y,c as U,b as u,w as k,a as o,t as v,m as B,a7 as O}from"./index.fba97cfa.js";const C={style:{width:"100%","margin-bottom":"20px","text-align":"center"}},F={style:{display:"flex","justify-content":"center"},class:"qrcode",id:"qrCodeUrl"},S=h({__name:"ChannelUserModal",emits:["changeChannelUserId"],setup(w,{expose:c,emit:d}){const i=d,e=g({open:!1,qrImgUrl:"",payText:"",transferOrderWebSocket:null,extObject:null});function l(n){e.transferOrderWebSocket&&e.transferOrderWebSocket.close(),e.open=!1}function p(n,t,a){e.extObject=a,e.transferOrderWebSocket&&e.transferOrderWebSocket.close(),e.payText="",t==="wxpay"?e.payText='\u8BF7\u4F7F\u7528\u5FAE\u4FE1\u5BA2\u6237\u7AEF"\u626B\u4E00\u626B"':t==="alipay"&&(e.payText='\u8BF7\u4F7F\u7528\u652F\u4ED8\u5B9D\u5BA2\u6237\u7AEF"\u626B\u4E00\u626B"');const r=n+new Date().getTime();f(t,n,r).then(s=>{e.qrImgUrl=s,e.open=!0,e.transferOrderWebSocket=new m(x()+"/api/anon/ws/channelUserId/"+n+"/"+r),e.transferOrderWebSocket.onopen=()=>{},e.transferOrderWebSocket.onmessage=_=>{i("changeChannelUserId",{channelUserId:_.data,extObject:e.extObject}),l()}})}return c({showModal:p}),(n,t)=>{const a=B("a-qrcode"),r=O;return y(),U("div",null,[u(r,{open:e.open,"onUpdate:open":t[0]||(t[0]=s=>e.open=s),title:"\u81EA\u52A8\u83B7\u53D6\u6E20\u9053\u7528\u6237ID",onOk:l,footer:null,width:300},{default:k(()=>[o("div",C,[o("div",F,[u(a,{value:e.qrImgUrl,size:"200"},null,8,["value"])]),t[1]||(t[1]=o("hr",null,null,-1)),o("span",null,v(e.payText),1)])]),_:1},8,["open"])])}}});var q=b(S,[["__scopeId","data-v-499b7bdc"]]);export{q as C};
