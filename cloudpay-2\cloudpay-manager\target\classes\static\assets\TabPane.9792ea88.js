import{q as Ke,s as Qe,v as pt,x as bt,y as ft,z as mt,A as gt,C as ht,D as z,E as Ae,G as ge,d as ae,h as V,H as W,b as g,K as J,J as le,L as we,M as C,N as A,O as Se,P as oe,Q as $t,R as yt,S as xt,T as _t,U as wt,V as Ce,W as F,X as St,Y as Ct,Z as Tt,$ as Pt,a0 as me,a1 as Ge,e as te,a2 as he,a3 as Rt,a4 as Xe,a5 as kt,a6 as It,a7 as et,a8 as Et,a9 as tt,aa as at,ab as Bt,ac as Lt,ad as xe,ae as At,af as Dt,ag as Mt,ah as Nt,ai as Ot,aj as Le,u as zt,ak as Wt,al as Ht}from"./index.8746381c.js";import{u as je}from"./useMergedState.8a9045a6.js";var Kt=()=>{if(typeof navigator=="undefined"||typeof window=="undefined")return!1;const e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(e==null?void 0:e.substring(0,4))};function Gt(e,t,a,i){if(!Ke(e))return e;t=Qe(t,e);for(var o=-1,l=t.length,n=l-1,r=e;r!=null&&++o<l;){var d=pt(t[o]),c=a;if(d==="__proto__"||d==="constructor"||d==="prototype")return e;if(o!=n){var b=r[d];c=i?i(b,d,r):void 0,c===void 0&&(c=Ke(b)?b:bt(t[o+1])?[]:{})}ft(r,d,c),r=r[d]}return e}function Xt(e,t,a){for(var i=-1,o=t.length,l={};++i<o;){var n=t[i],r=mt(e,n);a(r,n)&&Gt(l,Qe(n,e),r)}return l}function jt(e,t){return Xt(e,t,function(a,i){return gt(e,i)})}var Ft=ht(function(e,t){return e==null?{}:jt(e,t)}),nt=Ft;function Vt(e){const t=z(),a=z(!1);function i(){for(var o=arguments.length,l=new Array(o),n=0;n<o;n++)l[n]=arguments[n];a.value||(ge.cancel(t.value),t.value=ge(()=>{e(...l)}))}return Ae(()=>{a.value=!0,ge.cancel(t.value)}),i}function Yt(e){const t=z([]),a=z(typeof e=="function"?e():e),i=Vt(()=>{let l=a.value;t.value.forEach(n=>{l=n(l)}),t.value=[],a.value=l});function o(l){t.value.push(l),i()}return[a,o]}var Ut=ae({compatConfig:{MODE:3},name:"TabNode",props:{id:{type:String},prefixCls:{type:String},tab:{type:Object},active:{type:Boolean},closable:{type:Boolean},editable:{type:Object},onClick:{type:Function},onResize:{type:Function},renderWrapper:{type:Function},removeAriaLabel:{type:String},onFocus:{type:Function}},emits:["click","resize","remove","focus"],setup(e,t){let{expose:a,attrs:i}=t;const o=V();function l(d){var c;!((c=e.tab)===null||c===void 0)&&c.disabled||e.onClick(d)}a({domRef:o});function n(d){var c;d.preventDefault(),d.stopPropagation(),e.editable.onEdit("remove",{key:(c=e.tab)===null||c===void 0?void 0:c.key,event:d})}const r=W(()=>{var d;return e.editable&&e.closable!==!1&&!(!((d=e.tab)===null||d===void 0)&&d.disabled)});return()=>{var d;const{prefixCls:c,id:b,active:w,tab:{key:m,tab:u,disabled:y,closeIcon:x},renderWrapper:T,removeAriaLabel:S,editable:D,onFocus:K}=e,M=`${c}-tab`,s=g("div",{key:m,ref:o,class:le(M,{[`${M}-with-remove`]:r.value,[`${M}-active`]:w,[`${M}-disabled`]:y}),style:i.style,onClick:l},[g("div",{role:"tab","aria-selected":w,id:b&&`${b}-tab-${m}`,class:`${M}-btn`,"aria-controls":b&&`${b}-panel-${m}`,"aria-disabled":y,tabindex:y?null:0,onClick:h=>{h.stopPropagation(),l(h)},onKeydown:h=>{[J.SPACE,J.ENTER].includes(h.which)&&(h.preventDefault(),l(h))},onFocus:K},[typeof u=="function"?u():u]),r.value&&g("button",{type:"button","aria-label":S||"remove",tabindex:0,class:`${M}-remove`,onClick:h=>{h.stopPropagation(),n(h)}},[(x==null?void 0:x())||((d=D.removeIcon)===null||d===void 0?void 0:d.call(D))||"\xD7"])]);return T?T(s):s}}});const Fe={width:0,height:0,left:0,top:0};function qt(e,t){const a=V(new Map);return we(()=>{var i,o;const l=new Map,n=e.value,r=t.value.get((i=n[0])===null||i===void 0?void 0:i.key)||Fe,d=r.left+r.width;for(let c=0;c<n.length;c+=1){const{key:b}=n[c];let w=t.value.get(b);w||(w=t.value.get((o=n[c-1])===null||o===void 0?void 0:o.key)||Fe);const m=l.get(b)||C({},w);m.right=d-m.left-m.width,l.set(b,m)}a.value=new Map(l)}),a}var it=ae({compatConfig:{MODE:3},name:"AddButton",inheritAttrs:!1,props:{prefixCls:String,editable:{type:Object},locale:{type:Object,default:void 0}},setup(e,t){let{expose:a,attrs:i}=t;const o=V();return a({domRef:o}),()=>{const{prefixCls:l,editable:n,locale:r}=e;return!n||n.showAdd===!1?null:g("button",{ref:o,type:"button",class:`${l}-nav-add`,style:i.style,"aria-label":(r==null?void 0:r.addAriaLabel)||"Add tab",onClick:d=>{n.onEdit("add",{event:d})}},[n.addIcon?n.addIcon():"+"])}}});const Zt={prefixCls:{type:String},id:{type:String},tabs:{type:Object},rtl:{type:Boolean},tabBarGutter:{type:Number},activeKey:{type:[String,Number]},mobile:{type:Boolean},moreIcon:Ce.any,moreTransitionName:{type:String},editable:{type:Object},locale:{type:Object,default:void 0},removeAriaLabel:String,onTabClick:{type:Function},popupClassName:String,getPopupContainer:F()};var Jt=ae({compatConfig:{MODE:3},name:"OperationNode",inheritAttrs:!1,props:Zt,emits:["tabClick"],slots:Object,setup(e,t){let{attrs:a,slots:i}=t;const[o,l]=A(!1),[n,r]=A(null),d=u=>{const y=e.tabs.filter(S=>!S.disabled);let x=y.findIndex(S=>S.key===n.value)||0;const T=y.length;for(let S=0;S<T;S+=1){x=(x+u+T)%T;const D=y[x];if(!D.disabled){r(D.key);return}}},c=u=>{const{which:y}=u;if(!o.value){[J.DOWN,J.SPACE,J.ENTER].includes(y)&&(l(!0),u.preventDefault());return}switch(y){case J.UP:d(-1),u.preventDefault();break;case J.DOWN:d(1),u.preventDefault();break;case J.ESC:l(!1);break;case J.SPACE:case J.ENTER:n.value!==null&&e.onTabClick(n.value,u);break}},b=W(()=>`${e.id}-more-popup`),w=W(()=>n.value!==null?`${b.value}-${n.value}`:null),m=(u,y)=>{u.preventDefault(),u.stopPropagation(),e.editable.onEdit("remove",{key:y,event:u})};return Se(()=>{oe(n,()=>{const u=document.getElementById(w.value);u&&u.scrollIntoView&&u.scrollIntoView(!1)},{flush:"post",immediate:!0})}),oe(o,()=>{o.value||r(null)}),$t({}),()=>{var u;const{prefixCls:y,id:x,tabs:T,locale:S,mobile:D,moreIcon:K=((u=i.moreIcon)===null||u===void 0?void 0:u.call(i))||g(yt,null,null),moreTransitionName:M,editable:s,tabBarGutter:h,rtl:v,onTabClick:$,popupClassName:I}=e;if(!T.length)return null;const R=`${y}-dropdown`,G=S==null?void 0:S.dropdownAriaLabel,re={[v?"marginRight":"marginLeft"]:h};T.length||(re.visibility="hidden",re.order=1);const se=le({[`${R}-rtl`]:v,[`${I}`]:!0}),ce=D?null:g(wt,{prefixCls:R,trigger:["hover"],visible:o.value,transitionName:M,onVisibleChange:l,overlayClassName:se,mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:e.getPopupContainer},{overlay:()=>g(xt,{onClick:k=>{let{key:Q,domEvent:N}=k;$(Q,N),l(!1)},id:b.value,tabindex:-1,role:"listbox","aria-activedescendant":w.value,selectedKeys:[n.value],"aria-label":G!==void 0?G:"expanded dropdown"},{default:()=>[T.map(k=>{var Q,N;const Y=s&&k.closable!==!1&&!k.disabled;return g(_t,{key:k.key,id:`${b.value}-${k.key}`,role:"option","aria-controls":x&&`${x}-panel-${k.key}`,disabled:k.disabled},{default:()=>[g("span",null,[typeof k.tab=="function"?k.tab():k.tab]),Y&&g("button",{type:"button","aria-label":e.removeAriaLabel||"remove",tabindex:0,class:`${R}-menu-item-remove`,onClick:U=>{U.stopPropagation(),m(U,k.key)}},[((Q=k.closeIcon)===null||Q===void 0?void 0:Q.call(k))||((N=s.removeIcon)===null||N===void 0?void 0:N.call(s))||"\xD7"])]})})]}),default:()=>g("button",{type:"button",class:`${y}-nav-more`,style:re,tabindex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":b.value,id:`${x}-more`,"aria-expanded":o.value,onKeydown:c},[K])});return g("div",{class:le(`${y}-nav-operations`,a.class),style:a.style},[ce,g(it,{prefixCls:y,locale:S,editable:s},null)])}}});const ot=Symbol("tabsContextKey"),lt=e=>{St(ot,e)},rt=()=>Ct(ot,{tabs:V([]),prefixCls:V()});ae({compatConfig:{MODE:3},name:"TabsContextProvider",inheritAttrs:!1,props:{tabs:{type:Object,default:void 0},prefixCls:{type:String,default:void 0}},setup(e,t){let{slots:a}=t;return lt(Tt(e)),()=>{var i;return(i=a.default)===null||i===void 0?void 0:i.call(a)}}});const Qt=.1,Ve=.01,_e=20,Ye=Math.pow(.995,_e);function ea(e,t){const[a,i]=A(),[o,l]=A(0),[n,r]=A(0),[d,c]=A(),b=V();function w(s){const{screenX:h,screenY:v}=s.touches[0];i({x:h,y:v}),clearInterval(b.value)}function m(s){if(!a.value)return;s.preventDefault();const{screenX:h,screenY:v}=s.touches[0],$=h-a.value.x,I=v-a.value.y;t($,I),i({x:h,y:v});const R=Date.now();r(R-o.value),l(R),c({x:$,y:I})}function u(){if(!a.value)return;const s=d.value;if(i(null),c(null),s){const h=s.x/n.value,v=s.y/n.value,$=Math.abs(h),I=Math.abs(v);if(Math.max($,I)<Qt)return;let R=h,G=v;b.value=setInterval(()=>{if(Math.abs(R)<Ve&&Math.abs(G)<Ve){clearInterval(b.value);return}R*=Ye,G*=Ye,t(R*_e,G*_e)},_e)}}const y=V();function x(s){const{deltaX:h,deltaY:v}=s;let $=0;const I=Math.abs(h),R=Math.abs(v);I===R?$=y.value==="x"?h:v:I>R?($=h,y.value="x"):($=v,y.value="y"),t(-$,-$)&&s.preventDefault()}const T=V({onTouchStart:w,onTouchMove:m,onTouchEnd:u,onWheel:x});function S(s){T.value.onTouchStart(s)}function D(s){T.value.onTouchMove(s)}function K(s){T.value.onTouchEnd(s)}function M(s){T.value.onWheel(s)}Se(()=>{var s,h;document.addEventListener("touchmove",D,{passive:!1}),document.addEventListener("touchend",K,{passive:!1}),(s=e.value)===null||s===void 0||s.addEventListener("touchstart",S,{passive:!1}),(h=e.value)===null||h===void 0||h.addEventListener("wheel",M,{passive:!1})}),Ae(()=>{document.removeEventListener("touchmove",D),document.removeEventListener("touchend",K)})}function Ue(e,t){const a=V(e);function i(o){const l=typeof o=="function"?o(a.value):o;l!==a.value&&t(l,a.value),a.value=l}return[a,i]}const ta=()=>{const e=V(new Map),t=a=>i=>{e.value.set(a,i)};return Pt(()=>{e.value=new Map}),[t,e]};var aa=ta;const qe={width:0,height:0,left:0,top:0,right:0},na=()=>({id:{type:String},tabPosition:{type:String},activeKey:{type:[String,Number]},rtl:{type:Boolean},animated:he(),editable:he(),moreIcon:Ce.any,moreTransitionName:{type:String},mobile:{type:Boolean},tabBarGutter:{type:Number},renderTabBar:{type:Function},locale:he(),popupClassName:String,getPopupContainer:F(),onTabClick:{type:Function},onTabScroll:{type:Function}}),ia=(e,t)=>{const{offsetWidth:a,offsetHeight:i,offsetTop:o,offsetLeft:l}=e,{width:n,height:r,x:d,y:c}=e.getBoundingClientRect();return Math.abs(n-a)<1?[n,r,d-t.x,c-t.y]:[a,i,l,o]};var Ze=ae({compatConfig:{MODE:3},name:"TabNavList",inheritAttrs:!1,props:na(),slots:Object,emits:["tabClick","tabScroll"],setup(e,t){let{attrs:a,slots:i}=t;const{tabs:o,prefixCls:l}=rt(),n=z(),r=z(),d=z(),c=z(),[b,w]=aa(),m=W(()=>e.tabPosition==="top"||e.tabPosition==="bottom"),[u,y]=Ue(0,(f,p)=>{m.value&&e.onTabScroll&&e.onTabScroll({direction:f>p?"left":"right"})}),[x,T]=Ue(0,(f,p)=>{!m.value&&e.onTabScroll&&e.onTabScroll({direction:f>p?"top":"bottom"})}),[S,D]=A(0),[K,M]=A(0),[s,h]=A(null),[v,$]=A(null),[I,R]=A(0),[G,re]=A(0),[se,ce]=Yt(new Map),k=qt(o,se),Q=W(()=>`${l.value}-nav-operations-hidden`),N=z(0),Y=z(0);we(()=>{m.value?e.rtl?(N.value=0,Y.value=Math.max(0,S.value-s.value)):(N.value=Math.min(0,s.value-S.value),Y.value=0):(N.value=Math.min(0,v.value-K.value),Y.value=0)});const U=f=>f<N.value?N.value:f>Y.value?Y.value:f,ue=z(),[H,ve]=A(),pe=()=>{ve(Date.now())},be=()=>{clearTimeout(ue.value)},$e=(f,p)=>{f(_=>U(_+p))};ea(n,(f,p)=>{if(m.value){if(s.value>=S.value)return!1;$e(y,f)}else{if(v.value>=K.value)return!1;$e(T,p)}return be(),pe(),!0}),oe(H,()=>{be(),H.value&&(ue.value=setTimeout(()=>{ve(0)},100))});const de=function(){let f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activeKey;const p=k.value.get(f)||{width:0,height:0,left:0,right:0,top:0};if(m.value){let _=u.value;e.rtl?p.right<u.value?_=p.right:p.right+p.width>u.value+s.value&&(_=p.right+p.width-s.value):p.left<-u.value?_=-p.left:p.left+p.width>-u.value+s.value&&(_=-(p.left+p.width-s.value)),T(0),y(U(_))}else{let _=x.value;p.top<-x.value?_=-p.top:p.top+p.height>-x.value+v.value&&(_=-(p.top+p.height-v.value)),y(0),T(U(_))}},Te=z(0),Pe=z(0);we(()=>{let f,p,_,P,B,E;const q=k.value;["top","bottom"].includes(e.tabPosition)?(f="width",P=s.value,B=S.value,E=I.value,p=e.rtl?"right":"left",_=Math.abs(u.value)):(f="height",P=v.value,B=S.value,E=G.value,p="top",_=-x.value);let O=P;B+E>P&&B<P&&(O=P-E);const j=o.value;if(!j.length)return[Te.value,Pe.value]=[0,0];const Z=j.length;let ie=Z;for(let X=0;X<Z;X+=1){const ee=q.get(j[X].key)||qe;if(ee[p]+ee[f]>_+O){ie=X-1;break}}let L=0;for(let X=Z-1;X>=0;X-=1)if((q.get(j[X].key)||qe)[p]<_){L=X+1;break}return[Te.value,Pe.value]=[L,ie]});const De=()=>{ce(()=>{var f;const p=new Map,_=(f=r.value)===null||f===void 0?void 0:f.getBoundingClientRect();return o.value.forEach(P=>{let{key:B}=P;const E=w.value.get(B),q=(E==null?void 0:E.$el)||E;if(q){const[O,j,Z,ie]=ia(q,_);p.set(B,{width:O,height:j,left:Z,top:ie})}}),p})};oe(()=>o.value.map(f=>f.key).join("%%"),()=>{De()},{flush:"post"});const Re=()=>{var f,p,_,P,B;const E=((f=n.value)===null||f===void 0?void 0:f.offsetWidth)||0,q=((p=n.value)===null||p===void 0?void 0:p.offsetHeight)||0,O=((_=c.value)===null||_===void 0?void 0:_.$el)||{},j=O.offsetWidth||0,Z=O.offsetHeight||0;h(E),$(q),R(j),re(Z);const ie=(((P=r.value)===null||P===void 0?void 0:P.offsetWidth)||0)-j,L=(((B=r.value)===null||B===void 0?void 0:B.offsetHeight)||0)-Z;D(ie),M(L),De()},Me=W(()=>[...o.value.slice(0,Te.value),...o.value.slice(Pe.value+1)]),[dt,ct]=A(),ne=W(()=>k.value.get(e.activeKey)),Ne=z(),Oe=()=>{ge.cancel(Ne.value)};oe([ne,m,()=>e.rtl],()=>{const f={};ne.value&&(m.value?(e.rtl?f.right=me(ne.value.right):f.left=me(ne.value.left),f.width=me(ne.value.width)):(f.top=me(ne.value.top),f.height=me(ne.value.height))),Oe(),Ne.value=ge(()=>{ct(f)})}),oe([()=>e.activeKey,ne,k,m],()=>{de()},{flush:"post"}),oe([()=>e.rtl,()=>e.tabBarGutter,()=>e.activeKey,()=>o.value],()=>{Re()},{flush:"post"});const ke=f=>{let{position:p,prefixCls:_,extra:P}=f;if(!P)return null;const B=P==null?void 0:P({position:p});return B?g("div",{class:`${_}-extra-content`},[B]):null};return Ae(()=>{be(),Oe()}),()=>{const{id:f,animated:p,activeKey:_,rtl:P,editable:B,locale:E,tabPosition:q,tabBarGutter:O,onTabClick:j}=e,{class:Z,style:ie}=a,L=l.value,X=!!Me.value.length,ee=`${L}-nav-wrap`;let Ie,Ee,ze,We;m.value?P?(Ee=u.value>0,Ie=u.value+s.value<S.value):(Ie=u.value<0,Ee=-u.value+s.value<S.value):(ze=x.value<0,We=-x.value+v.value<K.value);const ye={};q==="top"||q==="bottom"?ye[P?"marginRight":"marginLeft"]=typeof O=="number"?`${O}px`:O:ye.marginTop=typeof O=="number"?`${O}px`:O;const He=o.value.map((Be,ut)=>{const{key:fe}=Be;return g(Ut,{id:f,prefixCls:L,key:fe,tab:Be,style:ut===0?void 0:ye,closable:Be.closable,editable:B,active:fe===_,removeAriaLabel:E==null?void 0:E.removeAriaLabel,ref:b(fe),onClick:vt=>{j(fe,vt)},onFocus:()=>{de(fe),pe(),n.value&&(P||(n.value.scrollLeft=0),n.value.scrollTop=0)}},i)});return g("div",{role:"tablist",class:le(`${L}-nav`,Z),style:ie,onKeydown:()=>{pe()}},[g(ke,{position:"left",prefixCls:L,extra:i.leftExtra},null),g(Ge,{onResize:Re},{default:()=>[g("div",{class:le(ee,{[`${ee}-ping-left`]:Ie,[`${ee}-ping-right`]:Ee,[`${ee}-ping-top`]:ze,[`${ee}-ping-bottom`]:We}),ref:n},[g(Ge,{onResize:Re},{default:()=>[g("div",{ref:r,class:`${L}-nav-list`,style:{transform:`translate(${u.value}px, ${x.value}px)`,transition:H.value?"none":void 0}},[He,g(it,{ref:c,prefixCls:L,locale:E,editable:B,style:C(C({},He.length===0?void 0:ye),{visibility:X?"hidden":null})},null),g("div",{class:le(`${L}-ink-bar`,{[`${L}-ink-bar-animated`]:p.inkBar}),style:dt.value},null)])]})])]}),g(Jt,te(te({},e),{},{removeAriaLabel:E==null?void 0:E.removeAriaLabel,ref:d,prefixCls:L,tabs:Me.value,class:!X&&Q.value}),nt(i,["moreIcon"])),g(ke,{position:"right",prefixCls:L,extra:i.rightExtra},null),g(ke,{position:"right",prefixCls:L,extra:i.tabBarExtraContent},null)])}}}),oa=ae({compatConfig:{MODE:3},name:"TabPanelList",inheritAttrs:!1,props:{activeKey:{type:[String,Number]},id:{type:String},rtl:{type:Boolean},animated:{type:Object,default:void 0},tabPosition:{type:String},destroyInactiveTabPane:{type:Boolean}},setup(e){const{tabs:t,prefixCls:a}=rt();return()=>{const{id:i,activeKey:o,animated:l,tabPosition:n,rtl:r,destroyInactiveTabPane:d}=e,c=l.tabPane,b=a.value,w=t.value.findIndex(m=>m.key===o);return g("div",{class:`${b}-content-holder`},[g("div",{class:[`${b}-content`,`${b}-content-${n}`,{[`${b}-content-animated`]:c}],style:w&&c?{[r?"marginRight":"marginLeft"]:`-${w}00%`}:null},[t.value.map(m=>Rt(m.node,{key:m.key,prefixCls:b,tabKey:m.key,id:i,animated:c,active:m.key===o,destroyInactiveTabPane:d}))])])}}});const la=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[Xe(e,"slide-up"),Xe(e,"slide-down")]]};var ra=la;const sa=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeadBackground:i,tabsCardGutter:o,colorSplit:l}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:i,border:`${e.lineWidth}px ${e.lineType} ${l}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:e.colorPrimary,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:`${o}px`}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:`${o}px`}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadiusLG}px 0 0 ${e.borderRadiusLG}px`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},da=e=>{const{componentCls:t,tabsHoverColor:a,dropdownEdgeChildVerticalPadding:i}=e;return{[`${t}-dropdown`]:C(C({},et(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${i}px 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":C(C({},Et),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${e.paddingXXS}px ${e.paddingSM}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},ca=e=>{const{componentCls:t,margin:a,colorSplit:i}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:`0 0 ${a}px 0`,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${e.lineWidth}px ${e.lineType} ${i}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:`${a}px`,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:e.controlHeight*1.25,[`${t}-tab`]:{padding:`${e.paddingXS}px ${e.paddingLG}px`,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:`${e.margin}px 0 0 0`},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:`-${e.lineWidth}px`},borderLeft:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:-e.lineWidth},borderRight:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},ua=e=>{const{componentCls:t,padding:a}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px 0`,fontSize:e.fontSize}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${a}px 0`,fontSize:e.fontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXXS*1.5}px ${a}px`}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${e.borderRadius}px ${e.borderRadius}px`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${e.borderRadius}px ${e.borderRadius}px 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadius}px ${e.borderRadius}px 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadius}px 0 0 ${e.borderRadius}px`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px ${a}px ${e.paddingXXS*1.5}px`}}}}}},va=e=>{const{componentCls:t,tabsActiveColor:a,tabsHoverColor:i,iconCls:o,tabsHorizontalGutter:l}=e,n=`${t}-tab`;return{[n]:{position:"relative",display:"inline-flex",alignItems:"center",padding:`${e.paddingSM}px 0`,fontSize:`${e.fontSize}px`,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":C({"&:focus:not(:focus-visible), &:active":{color:a}},tt(e)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-e.marginXXS},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:i},[`&${n}-active ${n}-btn`]:{color:e.colorPrimary,textShadow:e.tabsActiveTextShadow},[`&${n}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${n}-disabled ${n}-btn, &${n}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${n}-remove ${o}`]:{margin:0},[o]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${n} + ${n}`]:{margin:{_skip_check_:!0,value:`0 0 0 ${l}px`}}}},pa=e=>{const{componentCls:t,tabsHorizontalGutter:a,iconCls:i,tabsCardGutter:o}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:`0 0 0 ${a}px`},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[i]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:`${e.marginSM}px`}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:`${e.marginXS}px`},marginLeft:{_skip_check_:!0,value:`-${e.marginXXS}px`},[i]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:`${o}px`},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ba=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeight:i,tabsCardGutter:o,tabsHoverColor:l,tabsActiveColor:n,colorSplit:r}=e;return{[t]:C(C(C(C({},et(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:C({minWidth:`${i}px`,marginLeft:{_skip_check_:!0,value:`${o}px`},padding:`0 ${e.paddingXS}px`,background:"transparent",border:`${e.lineWidth}px ${e.lineType} ${r}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:l},"&:active, &:focus:not(:focus-visible)":{color:n}},tt(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.colorPrimary,pointerEvents:"none"}}),va(e)),{[`${t}-content`]:{position:"relative",display:"flex",width:"100%",["&-animated"]:{transition:"margin 0.3s"}},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none",flex:"none",width:"100%"}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}};var fa=kt("Tabs",e=>{const t=e.controlHeightLG,a=It(e,{tabsHoverColor:e.colorPrimaryHover,tabsActiveColor:e.colorPrimaryActive,tabsCardHorizontalPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,tabsCardHeight:t,tabsCardGutter:e.marginXXS/2,tabsHorizontalGutter:32,tabsCardHeadBackground:e.colorFillAlter,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120});return[ua(a),pa(a),ca(a),da(a),sa(a),ba(a),ra(a)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));let Je=0;const st=()=>({prefixCls:{type:String},id:{type:String},popupClassName:String,getPopupContainer:F(),activeKey:{type:[String,Number]},defaultActiveKey:{type:[String,Number]},direction:xe(),animated:At([Boolean,Object]),renderTabBar:F(),tabBarGutter:{type:Number},tabBarStyle:he(),tabPosition:xe(),destroyInactiveTabPane:Dt(),hideAdd:Boolean,type:xe(),size:xe(),centered:Boolean,onEdit:F(),onChange:F(),onTabClick:F(),onTabScroll:F(),"onUpdate:activeKey":F(),locale:he(),onPrevClick:F(),onNextClick:F(),tabBarExtraContent:Ce.any});function ma(e){return e.map(t=>{if(Mt(t)){const a=C({},t.props||{});for(const[m,u]of Object.entries(a))delete a[m],a[Nt(m)]=u;const i=t.children||{},o=t.key!==void 0?t.key:void 0,{tab:l=i.tab,disabled:n,forceRender:r,closable:d,animated:c,active:b,destroyInactiveTabPane:w}=a;return C(C({key:o},a),{node:t,closeIcon:i.closeIcon,tab:l,disabled:n===""||n,forceRender:r===""||r,closable:d===""||d,animated:c===""||c,active:b===""||b,destroyInactiveTabPane:w===""||w})}return null}).filter(t=>t)}const ga=ae({compatConfig:{MODE:3},name:"InternalTabs",inheritAttrs:!1,props:C(C({},at(st(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}})),{tabs:Ot()}),slots:Object,setup(e,t){let{attrs:a,slots:i}=t;Le(e.onPrevClick===void 0&&e.onNextClick===void 0,"Tabs","`onPrevClick / @prevClick` and `onNextClick / @nextClick` has been removed. Please use `onTabScroll / @tabScroll` instead."),Le(e.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` prop has been removed. Please use `rightExtra` slot instead."),Le(i.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` slot is deprecated. Please use `rightExtra` slot instead.");const{prefixCls:o,direction:l,size:n,rootPrefixCls:r,getPopupContainer:d}=zt("tabs",e),[c,b]=fa(o),w=W(()=>l.value==="rtl"),m=W(()=>{const{animated:v,tabPosition:$}=e;return v===!1||["left","right"].includes($)?{inkBar:!1,tabPane:!1}:v===!0?{inkBar:!0,tabPane:!0}:C({inkBar:!0,tabPane:!1},typeof v=="object"?v:{})}),[u,y]=A(!1);Se(()=>{y(Kt())});const[x,T]=je(()=>{var v;return(v=e.tabs[0])===null||v===void 0?void 0:v.key},{value:W(()=>e.activeKey),defaultValue:e.defaultActiveKey}),[S,D]=A(()=>e.tabs.findIndex(v=>v.key===x.value));we(()=>{var v;let $=e.tabs.findIndex(I=>I.key===x.value);$===-1&&($=Math.max(0,Math.min(S.value,e.tabs.length-1)),T((v=e.tabs[$])===null||v===void 0?void 0:v.key)),D($)});const[K,M]=je(null,{value:W(()=>e.id)}),s=W(()=>u.value&&!["left","right"].includes(e.tabPosition)?"top":e.tabPosition);Se(()=>{e.id||(M(`rc-tabs-${Je}`),Je+=1)});const h=(v,$)=>{var I,R;(I=e.onTabClick)===null||I===void 0||I.call(e,v,$);const G=v!==x.value;T(v),G&&((R=e.onChange)===null||R===void 0||R.call(e,v))};return lt({tabs:W(()=>e.tabs),prefixCls:o}),()=>{const{id:v,type:$,tabBarGutter:I,tabBarStyle:R,locale:G,destroyInactiveTabPane:re,renderTabBar:se=i.renderTabBar,onTabScroll:ce,hideAdd:k,centered:Q}=e,N={id:K.value,activeKey:x.value,animated:m.value,tabPosition:s.value,rtl:w.value,mobile:u.value};let Y;$==="editable-card"&&(Y={onEdit:(ve,pe)=>{let{key:be,event:$e}=pe;var de;(de=e.onEdit)===null||de===void 0||de.call(e,ve==="add"?$e:be,ve)},removeIcon:()=>g(Wt,null,null),addIcon:i.addIcon?i.addIcon:()=>g(Ht,null,null),showAdd:k!==!0});let U;const ue=C(C({},N),{moreTransitionName:`${r.value}-slide-up`,editable:Y,locale:G,tabBarGutter:I,onTabClick:h,onTabScroll:ce,style:R,getPopupContainer:d.value,popupClassName:le(e.popupClassName,b.value)});se?U=se(C(C({},ue),{DefaultTabBar:Ze})):U=g(Ze,ue,nt(i,["moreIcon","leftExtra","rightExtra","tabBarExtraContent"]));const H=o.value;return c(g("div",te(te({},a),{},{id:v,class:le(H,`${H}-${s.value}`,{[b.value]:!0,[`${H}-${n.value}`]:n.value,[`${H}-card`]:["card","editable-card"].includes($),[`${H}-editable-card`]:$==="editable-card",[`${H}-centered`]:Q,[`${H}-mobile`]:u.value,[`${H}-editable`]:$==="editable-card",[`${H}-rtl`]:w.value},a.class)}),[U,g(oa,te(te({destroyInactiveTabPane:re},N),{},{animated:m.value}),null)]))}}});var xa=ae({compatConfig:{MODE:3},name:"ATabs",inheritAttrs:!1,props:at(st(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}}),slots:Object,setup(e,t){let{attrs:a,slots:i,emit:o}=t;const l=n=>{o("update:activeKey",n),o("change",n)};return()=>{var n;const r=ma(Bt((n=i.default)===null||n===void 0?void 0:n.call(i)));return g(ga,te(te(te({},Lt(e,["onUpdate:activeKey"])),a),{},{onChange:l,tabs:r}),i)}}});const ha=()=>({tab:Ce.any,disabled:{type:Boolean},forceRender:{type:Boolean},closable:{type:Boolean},animated:{type:Boolean},active:{type:Boolean},destroyInactiveTabPane:{type:Boolean},prefixCls:{type:String},tabKey:{type:[String,Number]},id:{type:String}});var _a=ae({compatConfig:{MODE:3},name:"ATabPane",inheritAttrs:!1,__ANT_TAB_PANE:!0,props:ha(),slots:Object,setup(e,t){let{attrs:a,slots:i}=t;const o=V(e.forceRender);oe([()=>e.active,()=>e.destroyInactiveTabPane],()=>{e.active?o.value=!0:e.destroyInactiveTabPane&&(o.value=!1)},{immediate:!0});const l=W(()=>e.active?{}:e.animated?{visibility:"hidden",height:0,overflowY:"hidden"}:{display:"none"});return()=>{var n;const{prefixCls:r,forceRender:d,id:c,active:b,tabKey:w}=e;return g("div",{id:c&&`${c}-panel-${w}`,role:"tabpanel",tabindex:b?0:-1,"aria-labelledby":c&&`${c}-tab-${w}`,"aria-hidden":!b,style:[l.value,a.style],class:[`${r}-tabpane`,b&&`${r}-tabpane-active`,a.class]},[(b||o.value||d)&&((n=i.default)===null||n===void 0?void 0:n.call(i))])}}});export{xa as T,_a as _,Kt as i,aa as u};
