import{d as m,b as r,J as g,a5 as Q,a6 as U,am as Y,M as o,aa as N,u as C,H as x,e as f,ac as W}from"./index.8746381c.js";const Z=()=>({prefixCls:String,width:{type:[Number,String]}}),ee=m({compatConfig:{MODE:3},name:"SkeletonTitle",props:Z(),setup(e){return()=>{const{prefixCls:t,width:n}=e,l=typeof n=="number"?`${n}px`:n;return r("h3",{class:t,style:{width:l}},null)}}});var O=ee;const te=()=>({prefixCls:String,width:{type:[Number,String,Array]},rows:Number}),ne=m({compatConfig:{MODE:3},name:"SkeletonParagraph",props:te(),setup(e){const t=n=>{const{width:l,rows:a=2}=e;if(Array.isArray(l))return l[n];if(a-1===n)return l};return()=>{const{prefixCls:n,rows:l}=e,a=[...Array(l)].map((i,s)=>{const d=t(s);return r("li",{key:s,style:{width:typeof d=="number"?`${d}px`:d}},null)});return r("ul",{class:n},[a])}}});var le=ne;const A=()=>({prefixCls:String,size:[String,Number],shape:String,active:{type:Boolean,default:void 0}}),X=e=>{const{prefixCls:t,size:n,shape:l}=e,a=g({[`${t}-lg`]:n==="large",[`${t}-sm`]:n==="small"}),i=g({[`${t}-circle`]:l==="circle",[`${t}-square`]:l==="square",[`${t}-round`]:l==="round"}),s=typeof n=="number"?{width:`${n}px`,height:`${n}px`,lineHeight:`${n}px`}:{};return r("span",{class:g(t,a,i),style:s},null)};X.displayName="SkeletonElement";var P=X;const oe=new Y("ant-skeleton-loading",{"0%":{transform:"translateX(-37.5%)"},"100%":{transform:"translateX(37.5%)"}}),E=e=>({height:e,lineHeight:`${e}px`}),S=e=>o({width:e},E(e)),ae=e=>({position:"relative",zIndex:0,overflow:"hidden",background:"transparent","&::after":{position:"absolute",top:0,insetInlineEnd:"-150%",bottom:0,insetInlineStart:"-150%",background:e.skeletonLoadingBackground,animationName:oe,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite",content:'""'}}),T=e=>o({width:e*5,minWidth:e*5},E(e)),re=e=>{const{skeletonAvatarCls:t,color:n,controlHeight:l,controlHeightLG:a,controlHeightSM:i}=e;return{[`${t}`]:o({display:"inline-block",verticalAlign:"top",background:n},S(l)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:o({},S(a)),[`${t}${t}-sm`]:o({},S(i))}},ie=e=>{const{controlHeight:t,borderRadiusSM:n,skeletonInputCls:l,controlHeightLG:a,controlHeightSM:i,color:s}=e;return{[`${l}`]:o({display:"inline-block",verticalAlign:"top",background:s,borderRadius:n},T(t)),[`${l}-lg`]:o({},T(a)),[`${l}-sm`]:o({},T(i))}},G=e=>o({width:e},E(e)),se=e=>{const{skeletonImageCls:t,imageSizeBase:n,color:l,borderRadiusSM:a}=e;return{[`${t}`]:o(o({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:l,borderRadius:a},G(n*2)),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:o(o({},G(n)),{maxWidth:n*4,maxHeight:n*4}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},L=(e,t,n)=>{const{skeletonButtonCls:l}=e;return{[`${n}${l}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${n}${l}-round`]:{borderRadius:t}}},q=e=>o({width:e*2,minWidth:e*2},E(e)),ce=e=>{const{borderRadiusSM:t,skeletonButtonCls:n,controlHeight:l,controlHeightLG:a,controlHeightSM:i,color:s}=e;return o(o(o(o(o({[`${n}`]:o({display:"inline-block",verticalAlign:"top",background:s,borderRadius:t,width:l*2,minWidth:l*2},q(l))},L(e,l,n)),{[`${n}-lg`]:o({},q(a))}),L(e,a,`${n}-lg`)),{[`${n}-sm`]:o({},q(i))}),L(e,i,`${n}-sm`))},ue=e=>{const{componentCls:t,skeletonAvatarCls:n,skeletonTitleCls:l,skeletonParagraphCls:a,skeletonButtonCls:i,skeletonInputCls:s,skeletonImageCls:d,controlHeight:H,controlHeightLG:b,controlHeightSM:$,color:p,padding:M,marginSM:R,borderRadius:u,skeletonTitleHeight:v,skeletonBlockRadius:h,skeletonParagraphLineHeight:k,controlHeightXS:y,skeletonParagraphMarginTop:B}=e;return{[`${t}`]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:M,verticalAlign:"top",[`${n}`]:o({display:"inline-block",verticalAlign:"top",background:p},S(H)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:o({},S(b)),[`${n}-sm`]:o({},S($))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[`${l}`]:{width:"100%",height:v,background:p,borderRadius:h,[`+ ${a}`]:{marginBlockStart:$}},[`${a}`]:{padding:0,"> li":{width:"100%",height:k,listStyle:"none",background:p,borderRadius:h,"+ li":{marginBlockStart:y}}},[`${a}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${l}, ${a} > li`]:{borderRadius:u}}},[`${t}-with-avatar ${t}-content`]:{[`${l}`]:{marginBlockStart:R,[`+ ${a}`]:{marginBlockStart:B}}},[`${t}${t}-element`]:o(o(o(o({display:"inline-block",width:"auto"},ce(e)),re(e)),ie(e)),se(e)),[`${t}${t}-block`]:{width:"100%",[`${i}`]:{width:"100%"},[`${s}`]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${l},
        ${a} > li,
        ${n},
        ${i},
        ${s},
        ${d}
      `]:o({},ae(e))}}};var w=Q("Skeleton",e=>{const{componentCls:t}=e,n=U(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:e.controlHeight*1.5,skeletonTitleHeight:e.controlHeight/2,skeletonBlockRadius:e.borderRadiusSM,skeletonParagraphLineHeight:e.controlHeight/2,skeletonParagraphMarginTop:e.marginLG+e.marginXXS,borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.color} 25%, ${e.colorGradientEnd} 37%, ${e.color} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[ue(n)]},e=>{const{colorFillContent:t,colorFill:n}=e;return{color:t,colorGradientEnd:n}});const de=()=>({active:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},prefixCls:String,avatar:{type:[Boolean,Object],default:void 0},title:{type:[Boolean,Object],default:void 0},paragraph:{type:[Boolean,Object],default:void 0},round:{type:Boolean,default:void 0}});function D(e){return e&&typeof e=="object"?e:{}}function ge(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function pe(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function he(e,t){const n={};return(!e||!t)&&(n.width="61%"),!e&&t?n.rows=3:n.rows=2,n}const me=m({compatConfig:{MODE:3},name:"ASkeleton",props:N(de(),{avatar:!1,title:!0,paragraph:!0}),setup(e,t){let{slots:n}=t;const{prefixCls:l,direction:a}=C("skeleton",e),[i,s]=w(l);return()=>{var d;const{loading:H,avatar:b,title:$,paragraph:p,active:M,round:R}=e,u=l.value;if(H||e.loading===void 0){const v=!!b||b==="",h=!!$||$==="",k=!!p||p==="";let y;if(v){const I=o(o({prefixCls:`${u}-avatar`},ge(h,k)),D(b));y=r("div",{class:`${u}-header`},[r(P,I,null)])}let B;if(h||k){let I;if(h){const z=o(o({prefixCls:`${u}-title`},pe(v,k)),D($));I=r(O,z,null)}let j;if(k){const z=o(o({prefixCls:`${u}-paragraph`},he(v,h)),D(p));j=r(le,z,null)}B=r("div",{class:`${u}-content`},[I,j])}const V=g(u,{[`${u}-with-avatar`]:v,[`${u}-active`]:M,[`${u}-rtl`]:a.value==="rtl",[`${u}-round`]:R,[s.value]:!0});return i(r("div",{class:V},[y,B]))}return(d=n.default)===null||d===void 0?void 0:d.call(n)}}});var c=me;const $e=()=>o(o({},A()),{size:String,block:Boolean}),ve=m({compatConfig:{MODE:3},name:"ASkeletonButton",props:N($e(),{size:"default"}),setup(e){const{prefixCls:t}=C("skeleton",e),[n,l]=w(t),a=x(()=>g(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},l.value));return()=>n(r("div",{class:a.value},[r(P,f(f({},e),{},{prefixCls:`${t.value}-button`}),null)]))}});var _=ve;const ke=m({compatConfig:{MODE:3},name:"ASkeletonInput",props:o(o({},W(A(),["shape"])),{size:String,block:Boolean}),setup(e){const{prefixCls:t}=C("skeleton",e),[n,l]=w(t),a=x(()=>g(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},l.value));return()=>n(r("div",{class:a.value},[r(P,f(f({},e),{},{prefixCls:`${t.value}-input`}),null)]))}});var F=ke;const Se="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",fe=m({compatConfig:{MODE:3},name:"ASkeletonImage",props:W(A(),["size","shape","active"]),setup(e){const{prefixCls:t}=C("skeleton",e),[n,l]=w(t),a=x(()=>g(t.value,`${t.value}-element`,l.value));return()=>n(r("div",{class:a.value},[r("div",{class:`${t.value}-image`},[r("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",class:`${t.value}-image-svg`},[r("path",{d:Se,class:`${t.value}-image-path`},null)])])]))}});var J=fe;const be=()=>o(o({},A()),{shape:String}),Ce=m({compatConfig:{MODE:3},name:"ASkeletonAvatar",props:N(be(),{size:"default",shape:"circle"}),setup(e){const{prefixCls:t}=C("skeleton",e),[n,l]=w(t),a=x(()=>g(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active},l.value));return()=>n(r("div",{class:a.value},[r(P,f(f({},e),{},{prefixCls:`${t.value}-avatar`}),null)]))}});var K=Ce;c.Button=_;c.Avatar=K;c.Input=F;c.Image=J;c.Title=O;c.install=function(e){return e.component(c.name,c),e.component(c.Button.name,_),e.component(c.Avatar.name,K),e.component(c.Input.name,F),e.component(c.Image.name,J),e.component(c.Title.name,O),e};export{c as S};
