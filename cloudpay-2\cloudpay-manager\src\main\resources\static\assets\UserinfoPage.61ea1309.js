import{u as q,g as R,a as G,b as N}from"./manage.6e729324.js";import{d as x,u as V,b as t,e as v,_ as H,g as J,f as K,r as Q,h as y,c as W,w as a,i as E,o as X,a as i,j as c,t as Y,k as Z,l as U,I as z,m as ee,F as ue,B as te,n as ae}from"./index.8746381c.js";import"./index.8f4a8fa1.js";import{T as oe,_ as ne}from"./TabPane.9792ea88.js";import{r as le,u as se,R as n,_ as A}from"./Group.170fc6be.js";import{_ as re,a as de}from"./index.7c25015e.js";import"./useMergedState.8a9045a6.js";var ie=x({compatConfig:{MODE:3},name:"ARadioButton",inheritAttrs:!1,props:le(),setup(s,r){let{slots:m,attrs:F}=r;const{prefixCls:_}=V("radio",s);return se("button"),()=>{var e;return t(n,v(v(v({},F),s),{},{prefixCls:_.value}),{default:()=>[(e=m.default)===null||e===void 0?void 0:e.call(m)]})}}});n.Group=A;n.Button=ie;n.install=function(s){return s.component(n.name,n),s.component(n.Group.name,n.Group),s.component(n.Button.name,n.Button),s};const pe={style:{background:"#fff","border-radius":"10px",padding:"0 20px 20px"}},ce={class:"account-settings-info-view"},_e={style:{display:"flex","justify-content":"center"}},me={class:"ant-upload-preview"},fe=["src"],ge={class:"account-settings-info-view"},be={style:{display:"flex","justify-content":"center"}},ve=x({__name:"UserinfoPage",setup(s){const{$infoBox:r,$access:m,$hasAgentEnt:F}=J().appContext.config.globalProperties,_=K(),e=Q({action:q.avatar,btnLoading:!1,saveObject:{loginUsername:"",realname:"",telphone:"",sex:"",avatarUrl:""},updateObject:{originalPwd:"",newPwd:"",confirmPwd:""},recordId:_.userInfo.sysUserId,rules:{realname:[{required:!0,message:"\u8BF7\u8F93\u5165\u771F\u5B9E\u59D3\u540D",trigger:"blur"}]},rulesPass:{originalPwd:[{required:!0,message:"\u8BF7\u8F93\u5165\u539F\u5BC6\u7801",trigger:"blur"}],newPwd:[{min:6,max:12,required:!0,message:"\u8BF7\u8F93\u51656-12\u4F4D\u65B0\u5BC6\u7801",trigger:"blur"}],confirmPwd:[{required:!0,message:"\u8BF7\u786E\u8BA4\u8F93\u5165\u65B0\u5BC6\u7801",trigger:"blur"},{validator:(l,u)=>e.updateObject.newPwd===u?Promise.resolve():Promise.reject("\u65B0\u5BC6\u7801\u4E0E\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4")}]}}),B=y(),w=y();function I(){R().then(l=>{e.saveObject=l,console.log(l)})}I();function k(){B.value.validate().then(l=>{l&&r.confirmPrimary("\u786E\u8BA4\u66F4\u65B0\u4FE1\u606F\u5417\uFF1F","",()=>{e.btnLoading=!0,G(e.saveObject).then(u=>(e.btnLoading=!1,Z())).then(u=>{console.log(u,"bizData"),u.avatarUrl=e.saveObject.avatarUrl,u.realname=e.saveObject.realname,r.message.success("\u4FEE\u6539\u6210\u529F")}).catch(u=>{e.btnLoading=!1,console.log(u),r.message.error("\u4FEE\u6539\u5931\u8D25")})})})}function D(l){w.value.validate().then(u=>{u&&r.confirmPrimary("\u786E\u8BA4\u66F4\u65B0\u5BC6\u7801\u5417\uFF1F","",()=>{e.btnLoading=!0,e.confirmLoading=!0,e.updateObject.recordId=e.recordId,e.updateObject.originalPwd=U.encode(e.updateObject.originalPwd),e.updateObject.confirmPwd=U.encode(e.updateObject.confirmPwd),e.updateObject.newPwd="",N(e.updateObject).then(p=>{r.message.success("\u4FEE\u6539\u6210\u529F, \u5373\u5C06\u91CD\u65B0\u767B\u5F55"),setTimeout(()=>{_.logout()},1e3)}).catch(p=>{e.confirmLoading=!1,e.btnLoading=!1})})})}function L(){e.updateObject.originalPwd="",e.updateObject.newPwd="",e.updateObject.confirmPwd=""}function h(l,u){e.saveObject.avatarUrl=l}return(l,u)=>{const p=z,d=ee,j=n,S=A,O=ue,f=te,g=re,T=E("a-icon"),M=E("cloudpayUpload"),P=de,C=ne,b=ae,$=oe;return X(),W("div",pe,[t($,{onChange:L},{default:a(()=>[t(C,{key:"1",tab:"\u57FA\u672C\u4FE1\u606F"},{default:a(()=>[i("div",ce,[t(P,{gutter:16},{default:a(()=>[t(g,{md:16,lg:16},{default:a(()=>[t(O,{ref_key:"infoFormModel",ref:B,model:e.saveObject,"label-col":{span:9},"wrapper-col":{span:10},rules:e.rules},{default:a(()=>[t(d,{label:"\u7528\u6237\u767B\u5F55\u540D:"},{default:a(()=>[t(p,{value:e.saveObject.loginUsername,"onUpdate:value":u[0]||(u[0]=o=>e.saveObject.loginUsername=o),disabled:""},null,8,["value"])]),_:1}),t(d,{label:"\u7528\u6237\u59D3\u540D\uFF1A",name:"realname"},{default:a(()=>[t(p,{value:e.saveObject.realname,"onUpdate:value":u[1]||(u[1]=o=>e.saveObject.realname=o)},null,8,["value"])]),_:1}),t(d,{label:"\u624B\u673A\u53F7\uFF1A",name:"telphone"},{default:a(()=>[t(p,{value:e.saveObject.telphone,"onUpdate:value":u[2]||(u[2]=o=>e.saveObject.telphone=o),disabled:""},null,8,["value"])]),_:1}),t(d,{label:"\u8BF7\u9009\u62E9\u6027\u522B\uFF1A"},{default:a(()=>[t(S,{value:e.saveObject.sex,"onUpdate:value":u[3]||(u[3]=o=>e.saveObject.sex=o)},{default:a(()=>[t(j,{value:1},{default:a(()=>u[8]||(u[8]=[c("\u7537")])),_:1,__:[8]}),t(j,{value:2},{default:a(()=>u[9]||(u[9]=[c("\u5973")])),_:1,__:[9]})]),_:1},8,["value"])]),_:1})]),_:1},8,["model","rules"]),i("div",_e,[t(f,{type:"primary",onClick:k,loading:e.btnLoading},{default:a(()=>u[10]||(u[10]=[c(" \u66F4\u65B0\u57FA\u672C\u4FE1\u606F ")])),_:1,__:[10]},8,["loading"])])]),_:1}),t(g,{md:8,lg:8,style:{minHeight:"180px",margin:"0 auto"}},{default:a(()=>[i("div",me,[i("img",{src:e.saveObject.avatarUrl,style:{border:"1px solid rgba(0, 0, 0, 0.08)"}},null,8,fe),t(M,{style:{"margin-top":"10px"},action:e.action,accept:".jpg, .jpeg, .png",onUploadSuccess:u[4]||(u[4]=o=>h(o,""))},{uploadSlot:a(({loading:o})=>[t(f,{style:{"margin-left":"5px"}},{default:a(()=>[t(T,{type:e.loading?"loading":"upload"},null,8,["type"]),c(" "+Y(e.loading?"\u6B63\u5728\u4E0A\u4F20":"\u66F4\u6362\u5934\u50CF"),1)]),_:2},1024)]),_:1},8,["action"])])]),_:1})]),_:1})])]),_:1}),t(C,{key:"2",tab:"\u5B89\u5168\u4FE1\u606F"},{default:a(()=>[i("div",ge,[t(P,{gutter:16},{default:a(()=>[t(g,{md:16,lg:16},{default:a(()=>[t(O,{ref_key:"pwdFormModel",ref:w,model:e.updateObject,"label-col":{span:9},"wrapper-col":{span:10},rules:e.rulesPass},{default:a(()=>[t(d,{label:"\u539F\u5BC6\u7801\uFF1A",name:"originalPwd"},{default:a(()=>[t(b,{autocomplete:"new-password",value:e.updateObject.originalPwd,"onUpdate:value":u[5]||(u[5]=o=>e.updateObject.originalPwd=o),placeholder:"\u8BF7\u8F93\u5165\u539F\u5BC6\u7801"},null,8,["value"])]),_:1}),t(d,{label:"\u65B0\u5BC6\u7801\uFF1A",name:"newPwd"},{default:a(()=>[t(b,{autocomplete:"new-password",value:e.updateObject.newPwd,"onUpdate:value":u[6]||(u[6]=o=>e.updateObject.newPwd=o),placeholder:"\u8BF7\u8F93\u5165\u65B0\u5BC6\u7801"},null,8,["value"])]),_:1}),t(d,{label:"\u786E\u8BA4\u65B0\u5BC6\u7801\uFF1A",name:"confirmPwd"},{default:a(()=>[t(b,{autocomplete:"new-password",value:e.updateObject.confirmPwd,"onUpdate:value":u[7]||(u[7]=o=>e.updateObject.confirmPwd=o),placeholder:"\u786E\u8BA4\u65B0\u5BC6\u7801"},null,8,["value"])]),_:1})]),_:1},8,["model","rules"]),i("div",be,[t(f,{type:"primary",onClick:D,loading:e.btnLoading},{default:a(()=>u[11]||(u[11]=[c(" \u66F4\u65B0\u5BC6\u7801 ")])),_:1,__:[11]},8,["loading"])])]),_:1})]),_:1})])]),_:1})]),_:1})])}}});var ye=H(ve,[["__scopeId","data-v-3b04c0c6"]]);export{ye as default};
