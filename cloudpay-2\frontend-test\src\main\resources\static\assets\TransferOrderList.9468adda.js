import{r as U,H as T}from"./manage.6e729324.js";import{d as H,r as S,aE as D,w as e,o as p,b as t,j as n,t as s,a as m,aF as b,c8 as K,m as W,F as R,_ as z,g as G,h as O,i as E,c as h,aw as X,B as Z,aI as tt}from"./index.8746381c.js";import{D as et,a as at}from"./index.cbe5d957.js";import{_ as ut}from"./index.9b74c380.js";import{_ as lt,a as nt}from"./index.7c25015e.js";import{_ as L}from"./index.54e910b7.js";import{_ as ot}from"./index.5e527ed3.js";import{h as st}from"./moment.40bc58bf.js";import{R as dt}from"./dayjs.1ec7c0a1.js";import{S as rt,a as _t}from"./index.08051bcd.js";import{C as it}from"./Card.d6389e0b.js";import"./useMergedState.8a9045a6.js";import"./useMemo.91f6d273.js";import"./List.ee977be2.js";import"./TabPane.9792ea88.js";import"./index.8f4a8fa1.js";import"./index.4c901be3.js";const ft={style:{width:"100%","text-align":"center"}},mt=["src"],pt=H({__name:"TransferOrderDetail",setup($,{expose:w}){const a=S({detailData:{},userH5ConfirmUrl:null,userH5ConfirmQrImgUrl:null,isShow:!1,recordId:null});function x(v){a.userH5ConfirmUrl=null,a.userH5ConfirmQrImgUrl=null,U.getById(T,v).then(_=>{if(a.detailData=_,_.channelResData&&_.ifCode==="wxpay"){let o=JSON.parse(_.channelResData);o&&o.userH5ConfirmQrImgUrl&&(a.userH5ConfirmUrl=o.userH5ConfirmUrl,a.userH5ConfirmQrImgUrl=o.userH5ConfirmQrImgUrl)}}),a.isShow=!0}return w({show:x}),(v,_)=>{const o=et,u=at,d=lt,F=L,C=ot,B=nt,I=K,g=W,i=R,l=ut;return p(),D(l,{width:"50%",placement:"right",closable:!0,open:a.isShow,"onUpdate:open":_[2]||(_[2]=c=>a.isShow=c),title:"\u8F6C\u8D26\u8BA2\u5355\u8BE6\u60C5",onClose:_[3]||(_[3]=c=>a.isShow=!1)},{default:e(()=>[t(B,{justify:"space-between",type:"flex"},{default:e(()=>[t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5546\u6237\u7C7B\u578B"},{default:e(()=>[n(s(a.detailData.mchType===1?"\u666E\u901A\u5546\u6237":a.detailData.mchType===2?"\u7279\u7EA6\u5546\u6237":"\u672A\u77E5"),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5546\u6237\u53F7"},{default:e(()=>[n(s(a.detailData.mchNo),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5546\u6237\u540D\u79F0"},{default:e(()=>[n(s(a.detailData.mchName),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5E94\u7528APPID"},{default:e(()=>[n(s(a.detailData.appId),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u670D\u52A1\u5546\u53F7"},{default:e(()=>[n(s(a.detailData.isvNo),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u8F6C\u8D26\u8BA2\u5355\u53F7"},{default:e(()=>[t(F,{color:"purple"},{default:e(()=>[n(s(a.detailData.transferId),1)]),_:1})]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5546\u6237\u8F6C\u8D26\u5355\u53F7"},{default:e(()=>[n(s(a.detailData.mchOrderNo),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u6E20\u9053\u8BA2\u5355\u53F7"},{default:e(()=>[n(s(a.detailData.channelOrderNo),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u91D1\u989D"},{default:e(()=>[t(F,{color:"green"},{default:e(()=>[n(s(a.detailData.amount/100),1)]),_:1})]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u8D27\u5E01\u4EE3\u7801"},{default:e(()=>[n(s(a.detailData.currency),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u6536\u6B3E\u8D26\u53F7"},{default:e(()=>[t(F,{color:"green"},{default:e(()=>[n(s(a.detailData.accountNo),1)]),_:1})]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u6536\u6B3E\u4EBA\u59D3\u540D"},{default:e(()=>[n(s(a.detailData.accountName),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u8F6C\u8D26\u5907\u6CE8"},{default:e(()=>[n(s(a.detailData.transferDesc),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u8BA2\u5355\u72B6\u6001"},{default:e(()=>[t(F,{color:a.detailData.state===0?"blue":a.detailData.state===1?"orange":a.detailData.state===2?"green":"volcano"},{default:e(()=>[n(s(a.detailData.state===0?"\u8BA2\u5355\u751F\u6210":a.detailData.state===1?"\u8F6C\u8D26\u4E2D":a.detailData.state===2?"\u8F6C\u8D26\u6210\u529F":a.detailData.state===3?"\u8F6C\u8D26\u5931\u8D25":a.detailData.state===4?"\u4EFB\u52A1\u5173\u95ED":"\u672A\u77E5"),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u8F6C\u8D26\u6210\u529F\u65F6\u95F4"},{default:e(()=>[n(s(a.detailData.successTime),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:e(()=>[n(s(a.detailData.createdAt),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:e(()=>[n(s(a.detailData.updatedAt),1)]),_:1})]),_:1})]),_:1}),t(C),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u63A5\u53E3\u4EE3\u7801"},{default:e(()=>[n(s(a.detailData.ifCode),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5165\u8D26\u7C7B\u578B"},{default:e(()=>[n(s(a.detailData.entryType),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5BA2\u6237\u7AEFIP"},{default:e(()=>[n(s(a.detailData.clientIp),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:24},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u5F02\u6B65\u901A\u77E5\u5730\u5740"},{default:e(()=>[n(s(a.detailData.notifyUrl),1)]),_:1})]),_:1})]),_:1})]),_:1}),a.userH5ConfirmUrl&&a.userH5ConfirmQrImgUrl?(p(),D(B,{key:0,justify:"space-between",type:"flex"},{default:e(()=>[t(C),t(d,{sm:24},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u9886\u53D6\u94FE\u63A5"},{default:e(()=>[n(s(a.userH5ConfirmUrl),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u9886\u53D6\u4E8C\u7EF4\u7801"},{default:e(()=>[m("div",ft,[m("img",{src:a.userH5ConfirmQrImgUrl,alt:""},null,8,mt),_[4]||(_[4]=m("p",null,"\u8BF7\u4F7F\u7528\u5FAE\u4FE1\u626B\u7801\u9886\u53D6",-1))])]),_:1})]),_:1})]),_:1})]),_:1})):b("",!0),t(C),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u6E20\u9053\u8BA2\u5355\u53F7"},{default:e(()=>[n(s(a.detailData.channelOrderNo),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u6E20\u9053\u9519\u8BEF\u7801"},{default:e(()=>[n(s(a.detailData.errCode),1)]),_:1})]),_:1})]),_:1}),t(d,{sm:12},{default:e(()=>[t(u,null,{default:e(()=>[t(o,{label:"\u6E20\u9053\u9519\u8BEF\u63CF\u8FF0"},{default:e(()=>[n(s(a.detailData.errMsg),1)]),_:1})]),_:1})]),_:1}),t(i,{layout:"vertical"},{default:e(()=>[t(d,{sm:24},{default:e(()=>[t(g,{label:"\u6E20\u9053\u989D\u5916\u53C2\u6570:"},{default:e(()=>[t(I,{disabled:"disabled",style:{height:"100px",color:"black"},value:a.detailData.channelExtra,"onUpdate:value":_[0]||(_[0]=c=>a.detailData.channelExtra=c)},null,8,["value"])]),_:1})]),_:1}),t(C),t(d,{sm:24},{default:e(()=>[t(g,{label:"\u6269\u5C55\u53C2\u6570:"},{default:e(()=>[t(I,{disabled:"disabled",style:{height:"100px",color:"black"},value:a.detailData.extParam,"onUpdate:value":_[1]||(_[1]=c=>a.detailData.extParam=c)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["open"])}}});const ct={class:"table-page-search-wrapper"},Dt={class:"table-layer"},bt={class:"table-page-search-submitButtons"},Ft={key:0},Ct={key:2,class:"order-list"},gt={slot:"title"},yt={key:1,style:{"font-weight":"normal"}},Et={key:0},ht={slot:"title"},vt={key:1,style:{"font-weight":"normal"}},Bt=H({__name:"TransferOrderList",setup($){const{$infoBox:w,$access:a,$hasAgentEnt:x}=G().appContext.config.globalProperties,v=[{title:"\u8F6C\u8D26\u91D1\u989D",key:"amount",width:108},{title:"\u5546\u6237\u540D\u79F0",dataIndex:"mchName"},{key:"orderNo",title:"\u8BA2\u5355\u53F7",scopedSlots:{customRender:"orderSlot"},width:260},{title:"\u6536\u6B3E\u8D26\u53F7",dataIndex:"accountNo",width:200},{title:"\u6536\u6B3E\u4EBA\u59D3\u540D",dataIndex:"accountName"},{title:"\u8F6C\u8D26\u5907\u6CE8",dataIndex:"transferDesc"},{title:"\u72B6\u6001",key:"state",width:100},{title:"\u521B\u5EFA\u65E5\u671F",dataIndex:"createdAt"},{title:"\u64CD\u4F5C",width:"100px",fixed:"right",align:"center",key:"op"}],_=O(),o=O(),u=S({date:"",btnLoading:!1,tableColumns:v,searchData:{},createdStart:"",createdEnd:""});function d(){u.btnLoading=!0,_.value.refTable(!0)}function F(i){return U.list(T,i)}function C(i){o.value.show(i)}function B(i,l){u.searchData.createdStart=l[0],u.searchData.createdEnd=l[1]}function I(i){return i&&i>st().endOf("day")}function g(i,l){const c=Math.floor(l/2);return i.substring(0,c-1)+"..."+i.substring(i.length-c,i.length)}return(i,l)=>{const c=E("a-icon"),P=dt,k=E("cloudpay-text-up"),y=rt,Q=_t,A=Z,V=R,M=L,N=tt,Y=E("cloudpayTableColumns"),j=E("cloudpayTable"),q=it,J=E("page-header-wrapper");return p(),D(J,null,{default:e(()=>[t(q,null,{default:e(()=>[m("div",ct,[t(V,{layout:"inline",class:"table-head-ground"},{default:e(()=>[m("div",Dt,[t(P,{class:"table-head-layout",onChange:B,value:u.date,"onUpdate:value":l[0]||(l[0]=f=>u.date=f),"show-time":{format:"HH:mm:ss"},format:"YYYY-MM-DD HH:mm:ss","disabled-date":I},{default:e(()=>[t(c,{slot:"suffixIcon",type:"sync"})]),_:1},8,["value"]),t(k,{placeholder:"\u8F6C\u8D26/\u5546\u6237/\u6E20\u9053\u8BA2\u5355\u53F7",value:u.searchData.unionOrderId,"onUpdate:value":l[1]||(l[1]=f=>u.searchData.unionOrderId=f)},null,8,["value"]),t(k,{placeholder:"\u5546\u6237\u53F7",value:u.searchData.mchNo,"onUpdate:value":l[2]||(l[2]=f=>u.searchData.mchNo=f)},null,8,["value"]),t(k,{placeholder:"\u5E94\u7528AppId",value:u.searchData.appId,"onUpdate:value":l[3]||(l[3]=f=>u.searchData.appId=f)},null,8,["value"]),t(Q,{value:u.searchData.state,"onUpdate:value":l[4]||(l[4]=f=>u.searchData.state=f),class:"table-head-layout",placeholder:"\u8F6C\u8D26\u72B6\u6001"},{default:e(()=>[t(y,{value:""},{default:e(()=>l[7]||(l[7]=[n("\u5168\u90E8")])),_:1,__:[7]}),t(y,{value:"0"},{default:e(()=>l[8]||(l[8]=[n("\u8BA2\u5355\u751F\u6210")])),_:1,__:[8]}),t(y,{value:"1"},{default:e(()=>l[9]||(l[9]=[n("\u8F6C\u8D26\u4E2D")])),_:1,__:[9]}),t(y,{value:"2"},{default:e(()=>l[10]||(l[10]=[n("\u8F6C\u8D26\u6210\u529F")])),_:1,__:[10]}),t(y,{value:"3"},{default:e(()=>l[11]||(l[11]=[n("\u8F6C\u8D26\u5931\u8D25")])),_:1,__:[11]})]),_:1},8,["value"]),m("span",bt,[t(A,{type:"primary",onClick:d,loading:u.btnLoading},{default:e(()=>l[12]||(l[12]=[n(" \u641C\u7D22 ")])),_:1,__:[12]},8,["loading"]),t(A,{style:{"margin-left":"8px"},onClick:l[5]||(l[5]=()=>{u.searchData={},u.date=""})},{default:e(()=>l[13]||(l[13]=[n(" \u91CD\u7F6E ")])),_:1,__:[13]})])])]),_:1})]),t(j,{onBtnLoadClose:l[6]||(l[6]=f=>u.btnLoading=!1),ref_key:"infoTable",ref:_,initData:!0,reqTableDataFunc:F,tableColumns:u.tableColumns,searchData:u.searchData,rowKey:"transferId",tableRowCrossColor:!0},{bodyCell:e(({column:f,record:r})=>[f.key==="amount"?(p(),h("b",Ft,"\uFFE5"+s(r.amount/100),1)):b("",!0),f.key==="state"?(p(),D(M,{key:r.state,color:r.state===0?"blue":r.state===1?"orange":r.state===2?"green":"volcano"},{default:e(()=>[n(s(r.state===0?"\u8BA2\u5355\u751F\u6210":r.state===1?"\u8F6C\u8D26\u4E2D":r.state===2?"\u8F6C\u8D26\u6210\u529F":r.state===3?"\u8F6C\u8D26\u5931\u8D25":r.state===4?"\u4EFB\u52A1\u5173\u95ED":"\u672A\u77E5"),1)]),_:2},1032,["color"])):b("",!0),f.key==="orderNo"?(p(),h("div",Ct,[m("p",null,[l[14]||(l[14]=m("span",{style:{color:"#729ed5",background:"#e7f5f7"}},"\u8F6C\u8D26",-1)),n(" "+s(r.transferId),1)]),m("p",null,[l[15]||(l[15]=m("span",{style:{color:"#56cf56",background:"#d8eadf"}},"\u5546\u6237",-1)),r.mchOrderNo.length>r.transferId.length?(p(),D(N,{key:0,placement:"bottom",style:{"font-weight":"normal"}},{default:e(()=>[m("template",gt,[m("span",null,s(r.mchOrderNo),1)]),n(" "+s(g(r.mchOrderNo,r.transferId.length)),1)]),_:2},1024)):(p(),h("span",yt,s(r.mchOrderNo),1))]),r.channelOrderNo?(p(),h("p",Et,[l[16]||(l[16]=m("span",{style:{color:"#fff",background:"#e09c4d"}},"\u6E20\u9053",-1)),r.channelOrderNo.length>r.transferId.length?(p(),D(N,{key:0,placement:"bottom",style:{"font-weight":"normal"}},{default:e(()=>[m("template",ht,[m("span",null,s(r.channelOrderNo),1)]),n(" "+s(g(r.channelOrderNo,r.transferId.length)),1)]),_:2},1024)):(p(),h("span",vt,s(r.channelOrderNo),1))])):b("",!0)])):b("",!0),f.key==="op"?(p(),D(Y,{key:3},{default:e(()=>[X(a)("ENT_TRANSFER_ORDER_VIEW")?(p(),D(A,{key:0,type:"link",onClick:It=>C(r.transferId)},{default:e(()=>l[17]||(l[17]=[n(" \u8BE6\u60C5 ")])),_:2,__:[17]},1032,["onClick"])):b("",!0)]),_:2},1024)):b("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),t(pt,{ref_key:"transferOrderDetail",ref:o},null,512)]),_:1})}}});var Yt=z(Bt,[["__scopeId","data-v-896b92ac"]]);export{Yt as default};
