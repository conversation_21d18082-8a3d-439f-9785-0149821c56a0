import{M as h,d as pe,V as I,H as E,h as ve,b as $,e as J,J as fe,aR as rt,bF as ut,bS as st,Y as Fe,b_ as ct,a3 as xn,D as q,L as xe,O as we,P as de,bK as Ge,j as He,b$ as dt,aJ as $e,K as M,E as Ue,X as Xe,c0 as ft,r as We,aa as Ye,Z as pt,ag as Qe,ax as $n,as as fn,ac as De,ab as mt,c1 as gt,aQ as me,N as pn,bd as vt,bN as ht,c2 as bt,ak as St,c3 as yt,c4 as wt,a7 as qe,b0 as It,a$ as Ct,b2 as xt,b1 as $t,a8 as Ke,a4 as mn,b4 as gn,a6 as ge,c5 as On,a5 as Ot,aX as Pt,ao as Tt,ap as Ft,u as Dt,b6 as Et,aq as Vt,c6 as Mt,ae as vn,ad as Ce,af as Rt,W as Nt,bc as Bt,bq as At,c7 as Lt,bb as _t}from"./index.8746381c.js";import{c as Je,L as zt}from"./List.ee977be2.js";import{i as Ht}from"./TabPane.9792ea88.js";import{u as Wt}from"./useMemo.91f6d273.js";import{u as hn}from"./useMergedState.8a9045a6.js";function bn(e,n){const{key:o}=e;let t;return"value"in e&&({value:t}=e),o!=null?o:t!==void 0?t:`rc-index-key-${n}`}function Pn(e,n){const{label:o,value:t,options:l}=e||{};return{label:o||(n?"children":"label"),value:t||"value",options:l||"options"}}function Kt(e){let{fieldNames:n,childrenAsData:o}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const t=[],{label:l,value:i,options:c}=Pn(n,!1);function d(m,p){m.forEach(u=>{const C=u[l];if(p||!(c in u)){const V=u[i];t.push({key:bn(u,t.length),groupOption:p,data:u,label:C,value:V})}else{let V=C;V===void 0&&o&&(V=u.label),t.push({key:bn(u,t.length),group:!0,data:u,label:V}),d(u[c],!0)}})}return d(e,!1),t}function je(e){const n=h({},e);return"props"in n||Object.defineProperty(n,"props",{get(){return n}}),n}function jt(e,n){if(!n||!n.length)return null;let o=!1;function t(i,c){let[d,...m]=c;if(!d)return[i];const p=i.split(d);return o=o||p.length>1,p.reduce((u,C)=>[...u,...t(C,m)],[]).filter(u=>u)}const l=t(e,n);return o?l:null}var Gt=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(o[t[l]]=e[t[l]]);return o};const Ut=e=>{const n=e===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1}},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1}},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1}},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1}}}},Xt=pe({name:"SelectTrigger",inheritAttrs:!1,props:{dropdownAlign:Object,visible:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},dropdownClassName:String,dropdownStyle:I.object,placement:String,empty:{type:Boolean,default:void 0},prefixCls:String,popupClassName:String,animation:String,transitionName:String,getPopupContainer:Function,dropdownRender:Function,containerWidth:Number,dropdownMatchSelectWidth:I.oneOfType([Number,Boolean]).def(!0),popupElement:I.any,direction:String,getTriggerDOMNode:Function,onPopupVisibleChange:Function,onPopupMouseEnter:Function,onPopupFocusin:Function,onPopupFocusout:Function},setup(e,n){let{slots:o,attrs:t,expose:l}=n;const i=E(()=>{const{dropdownMatchSelectWidth:d}=e;return Ut(d)}),c=ve();return l({getPopupElement:()=>c.value}),()=>{const d=h(h({},e),t),{empty:m=!1}=d,p=Gt(d,["empty"]),{visible:u,dropdownAlign:C,prefixCls:V,popupElement:T,dropdownClassName:g,dropdownStyle:b,direction:x="ltr",placement:S,dropdownMatchSelectWidth:D,containerWidth:L,dropdownRender:P,animation:f,transitionName:O,getPopupContainer:w,getTriggerDOMNode:F,onPopupVisibleChange:R,onPopupMouseEnter:W,onPopupFocusin:_,onPopupFocusout:ne}=p,K=`${V}-dropdown`;let j=T;P&&(j=P({menuNode:T,props:e}));const H=f?`${K}-${f}`:O,B=h({minWidth:`${L}px`},b);return typeof D=="number"?B.width=`${D}px`:D&&(B.width=`${L}px`),$(rt,J(J({},e),{},{showAction:R?["click"]:[],hideAction:R?["click"]:[],popupPlacement:S||(x==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:i.value,prefixCls:K,popupTransitionName:H,popupAlign:C,popupVisible:u,getPopupContainer:w,popupClassName:fe(g,{[`${K}-empty`]:m}),popupStyle:B,getTriggerDOMNode:F,onPopupVisibleChange:R}),{default:o.default,popup:()=>$("div",{ref:c,onMouseenter:W,onFocusin:_,onFocusout:ne},[j])})}}});var Yt=Xt;const Ee=(e,n)=>{let{slots:o}=n;var t;const{class:l,customizeIcon:i,customizeIconProps:c,onMousedown:d,onClick:m}=e;let p;return typeof i=="function"?p=i(c):p=ut(i)?st(i):i,$("span",{class:l,onMousedown:u=>{u.preventDefault(),d&&d(u)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:m,"aria-hidden":!0},[p!==void 0?p:$("span",{class:l.split(/\s+/).map(u=>`${u}-icon`)},[(t=o.default)===null||t===void 0?void 0:t.call(o)])])};Ee.inheritAttrs=!1;Ee.displayName="TransBtn";Ee.props={class:String,customizeIcon:I.any,customizeIconProps:I.any,onMousedown:Function,onClick:Function};var Te=Ee;const Qt={inputRef:I.any,prefixCls:String,id:String,inputElement:I.VueNode,disabled:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},autocomplete:String,editable:{type:Boolean,default:void 0},activeDescendantId:String,value:String,open:{type:Boolean,default:void 0},tabindex:I.oneOfType([I.number,I.string]),attrs:I.object,onKeydown:{type:Function},onMousedown:{type:Function},onChange:{type:Function},onPaste:{type:Function},onCompositionstart:{type:Function},onCompositionend:{type:Function},onFocus:{type:Function},onBlur:{type:Function}},qt=pe({compatConfig:{MODE:3},name:"SelectInput",inheritAttrs:!1,props:Qt,setup(e){let n=null;const o=Fe("VCSelectContainerEvent");return()=>{var t;const{prefixCls:l,id:i,inputElement:c,disabled:d,tabindex:m,autofocus:p,autocomplete:u,editable:C,activeDescendantId:V,value:T,onKeydown:g,onMousedown:b,onChange:x,onPaste:S,onCompositionstart:D,onCompositionend:L,onFocus:P,onBlur:f,open:O,inputRef:w,attrs:F}=e;let R=c||$(ct,null,null);const W=R.props||{},{onKeydown:_,onInput:ne,onFocus:K,onBlur:j,onMousedown:H,onCompositionstart:B,onCompositionend:ae,style:ie}=W;return R=xn(R,h(h(h(h(h({type:"search"},W),{id:i,ref:w,disabled:d,tabindex:m,lazy:!1,autocomplete:u||"off",autofocus:p,class:fe(`${l}-selection-search-input`,(t=R==null?void 0:R.props)===null||t===void 0?void 0:t.class),role:"combobox","aria-expanded":O,"aria-haspopup":"listbox","aria-owns":`${i}_list`,"aria-autocomplete":"list","aria-controls":`${i}_list`,"aria-activedescendant":V}),F),{value:C?T:"",readonly:!C,unselectable:C?null:"on",style:h(h({},ie),{opacity:C?null:0}),onKeydown:G=>{g(G),_&&_(G)},onMousedown:G=>{b(G),H&&H(G)},onInput:G=>{x(G),ne&&ne(G)},onCompositionstart(G){D(G),B&&B(G)},onCompositionend(G){L(G),ae&&ae(G)},onPaste:S,onFocus:function(){clearTimeout(n),K&&K(arguments.length<=0?void 0:arguments[0]),P&&P(arguments.length<=0?void 0:arguments[0]),o==null||o.focus(arguments.length<=0?void 0:arguments[0])},onBlur:function(){for(var G=arguments.length,te=new Array(G),Y=0;Y<G;Y++)te[Y]=arguments[Y];n=setTimeout(()=>{j&&j(te[0]),f&&f(te[0]),o==null||o.blur(te[0])},100)}}),R.type==="textarea"?{}:{type:"search"}),!0,!0),R}}});var Tn=qt;const Jt=Symbol("TreeSelectLegacyContextPropsKey");function Ze(){return Fe(Jt,{})}const Zt={id:String,prefixCls:String,values:I.array,open:{type:Boolean,default:void 0},searchValue:String,inputRef:I.any,placeholder:I.any,disabled:{type:Boolean,default:void 0},mode:String,showSearch:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},autocomplete:String,activeDescendantId:String,tabindex:I.oneOfType([I.number,I.string]),compositionStatus:Boolean,removeIcon:I.any,choiceTransitionName:String,maxTagCount:I.oneOfType([I.number,I.string]),maxTagTextLength:Number,maxTagPlaceholder:I.any.def(()=>e=>`+ ${e.length} ...`),tagRender:Function,onToggleOpen:{type:Function},onRemove:Function,onInputChange:Function,onInputPaste:Function,onInputKeyDown:Function,onInputMouseDown:Function,onInputCompositionStart:Function,onInputCompositionEnd:Function},Sn=e=>{e.preventDefault(),e.stopPropagation()},kt=pe({name:"MultipleSelectSelector",inheritAttrs:!1,props:Zt,setup(e){const n=q(),o=q(0),t=q(!1),l=Ze(),i=E(()=>`${e.prefixCls}-selection`),c=E(()=>e.open||e.mode==="tags"?e.searchValue:""),d=E(()=>e.mode==="tags"||e.showSearch&&(e.open||t.value)),m=ve("");xe(()=>{m.value=c.value}),we(()=>{de(m,()=>{o.value=n.value.scrollWidth},{flush:"post",immediate:!0})});function p(g,b,x,S,D){return $("span",{class:fe(`${i.value}-item`,{[`${i.value}-item-disabled`]:x}),title:typeof g=="string"||typeof g=="number"?g.toString():void 0},[$("span",{class:`${i.value}-item-content`},[b]),S&&$(Te,{class:`${i.value}-item-remove`,onMousedown:Sn,onClick:D,customizeIcon:e.removeIcon},{default:()=>[He("\xD7")]})])}function u(g,b,x,S,D,L){var P;const f=w=>{Sn(w),e.onToggleOpen(!open)};let O=L;return l.keyEntities&&(O=((P=l.keyEntities[g])===null||P===void 0?void 0:P.node)||{}),$("span",{key:g,onMousedown:f},[e.tagRender({label:b,value:g,disabled:x,closable:S,onClose:D,option:O})])}function C(g){const{disabled:b,label:x,value:S,option:D}=g,L=!e.disabled&&!b;let P=x;if(typeof e.maxTagTextLength=="number"&&(typeof x=="string"||typeof x=="number")){const O=String(P);O.length>e.maxTagTextLength&&(P=`${O.slice(0,e.maxTagTextLength)}...`)}const f=O=>{var w;O&&O.stopPropagation(),(w=e.onRemove)===null||w===void 0||w.call(e,g)};return typeof e.tagRender=="function"?u(S,P,b,L,f,D):p(x,P,b,L,f)}function V(g){const{maxTagPlaceholder:b=S=>`+ ${S.length} ...`}=e,x=typeof b=="function"?b(g):b;return p(x,x,!1)}const T=g=>{const b=g.target.composing;m.value=g.target.value,b||e.onInputChange(g)};return()=>{const{id:g,prefixCls:b,values:x,open:S,inputRef:D,placeholder:L,disabled:P,autofocus:f,autocomplete:O,activeDescendantId:w,tabindex:F,compositionStatus:R,onInputPaste:W,onInputKeyDown:_,onInputMouseDown:ne,onInputCompositionStart:K,onInputCompositionEnd:j}=e,H=$("div",{class:`${i.value}-search`,style:{width:o.value+"px"},key:"input"},[$(Tn,{inputRef:D,open:S,prefixCls:b,id:g,inputElement:null,disabled:P,autofocus:f,autocomplete:O,editable:d.value,activeDescendantId:w,value:m.value,onKeydown:_,onMousedown:ne,onChange:T,onPaste:W,onCompositionstart:K,onCompositionend:j,tabindex:F,attrs:Ge(e,!0),onFocus:()=>t.value=!0,onBlur:()=>t.value=!1},null),$("span",{ref:n,class:`${i.value}-search-mirror`,"aria-hidden":!0},[m.value,He("\xA0")])]),B=$(dt,{prefixCls:`${i.value}-overflow`,data:x,renderItem:C,renderRest:V,suffix:H,itemKey:"key",maxCount:e.maxTagCount,key:"overflow"},null);return $($e,null,[B,!x.length&&!c.value&&!R&&$("span",{class:`${i.value}-placeholder`},[L])])}}});var eo=kt;const no={inputElement:I.any,id:String,prefixCls:String,values:I.array,open:{type:Boolean,default:void 0},searchValue:String,inputRef:I.any,placeholder:I.any,compositionStatus:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},mode:String,showSearch:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},autocomplete:String,activeDescendantId:String,tabindex:I.oneOfType([I.number,I.string]),activeValue:String,backfill:{type:Boolean,default:void 0},optionLabelRender:Function,onInputChange:Function,onInputPaste:Function,onInputKeyDown:Function,onInputMouseDown:Function,onInputCompositionStart:Function,onInputCompositionEnd:Function},ke=pe({name:"SingleSelector",setup(e){const n=q(!1),o=E(()=>e.mode==="combobox"),t=E(()=>o.value||e.showSearch),l=E(()=>{let u=e.searchValue||"";return o.value&&e.activeValue&&!n.value&&(u=e.activeValue),u}),i=Ze();de([o,()=>e.activeValue],()=>{o.value&&(n.value=!1)},{immediate:!0});const c=E(()=>e.mode!=="combobox"&&!e.open&&!e.showSearch?!1:!!l.value||e.compositionStatus),d=E(()=>{const u=e.values[0];return u&&(typeof u.label=="string"||typeof u.label=="number")?u.label.toString():void 0}),m=()=>{if(e.values[0])return null;const u=c.value?{visibility:"hidden"}:void 0;return $("span",{class:`${e.prefixCls}-selection-placeholder`,style:u},[e.placeholder])},p=u=>{u.target.composing||(n.value=!0,e.onInputChange(u))};return()=>{var u,C,V,T;const{inputElement:g,prefixCls:b,id:x,values:S,inputRef:D,disabled:L,autofocus:P,autocomplete:f,activeDescendantId:O,open:w,tabindex:F,optionLabelRender:R,onInputKeyDown:W,onInputMouseDown:_,onInputPaste:ne,onInputCompositionStart:K,onInputCompositionEnd:j}=e,H=S[0];let B=null;if(H&&i.customSlots){const ae=(u=H.key)!==null&&u!==void 0?u:H.value,ie=((C=i.keyEntities[ae])===null||C===void 0?void 0:C.node)||{};B=i.customSlots[(V=ie.slots)===null||V===void 0?void 0:V.title]||i.customSlots.title||H.label,typeof B=="function"&&(B=B(ie))}else B=R&&H?R(H.option):H==null?void 0:H.label;return $($e,null,[$("span",{class:`${b}-selection-search`},[$(Tn,{inputRef:D,prefixCls:b,id:x,open:w,inputElement:g,disabled:L,autofocus:P,autocomplete:f,editable:t.value,activeDescendantId:O,value:l.value,onKeydown:W,onMousedown:_,onChange:p,onPaste:ne,onCompositionstart:K,onCompositionend:j,tabindex:F,attrs:Ge(e,!0)},null)]),!o.value&&H&&!c.value&&$("span",{class:`${b}-selection-item`,title:d.value},[$($e,{key:(T=H.key)!==null&&T!==void 0?T:H.value},[B])]),m()])}}});ke.props=no;ke.inheritAttrs=!1;var to=ke;function oo(e){return![M.ESC,M.SHIFT,M.BACKSPACE,M.TAB,M.WIN_KEY,M.ALT,M.META,M.WIN_KEY_RIGHT,M.CTRL,M.SEMICOLON,M.EQUALS,M.CAPS_LOCK,M.CONTEXT_MENU,M.F1,M.F2,M.F3,M.F4,M.F5,M.F6,M.F7,M.F8,M.F9,M.F10,M.F11,M.F12].includes(e)}function Fn(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,n=null,o;Ue(()=>{clearTimeout(o)});function t(l){(l||n===null)&&(n=l),clearTimeout(o),o=setTimeout(()=>{n=null},e)}return[()=>n,t]}const lo=pe({name:"Selector",inheritAttrs:!1,props:{id:String,prefixCls:String,showSearch:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},values:I.array,multiple:{type:Boolean,default:void 0},mode:String,searchValue:String,activeValue:String,inputElement:I.any,autofocus:{type:Boolean,default:void 0},activeDescendantId:String,tabindex:I.oneOfType([I.number,I.string]),disabled:{type:Boolean,default:void 0},placeholder:I.any,removeIcon:I.any,maxTagCount:I.oneOfType([I.number,I.string]),maxTagTextLength:Number,maxTagPlaceholder:I.any,tagRender:Function,optionLabelRender:Function,tokenWithEnter:{type:Boolean,default:void 0},choiceTransitionName:String,onToggleOpen:{type:Function},onSearch:Function,onSearchSubmit:Function,onRemove:Function,onInputKeyDown:{type:Function},domRef:Function},setup(e,n){let{expose:o}=n;const t=Je(),l=ve(!1),[i,c]=Fn(0),d=S=>{const{which:D}=S;(D===M.UP||D===M.DOWN)&&S.preventDefault(),e.onInputKeyDown&&e.onInputKeyDown(S),D===M.ENTER&&e.mode==="tags"&&!l.value&&!e.open&&e.onSearchSubmit(S.target.value),oo(D)&&e.onToggleOpen(!0)},m=()=>{c(!0)};let p=null;const u=S=>{e.onSearch(S,!0,l.value)!==!1&&e.onToggleOpen(!0)},C=()=>{l.value=!0},V=S=>{l.value=!1,e.mode!=="combobox"&&u(S.target.value)},T=S=>{let{target:{value:D}}=S;if(e.tokenWithEnter&&p&&/[\r\n]/.test(p)){const L=p.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");D=D.replace(L,p)}p=null,u(D)},g=S=>{const{clipboardData:D}=S;p=D.getData("text")},b=S=>{let{target:D}=S;D!==t.current&&(document.body.style.msTouchAction!==void 0?setTimeout(()=>{t.current.focus()}):t.current.focus())},x=S=>{const D=i();S.target!==t.current&&!D&&S.preventDefault(),(e.mode!=="combobox"&&(!e.showSearch||!D)||!e.open)&&(e.open&&e.onSearch("",!0,!1),e.onToggleOpen())};return o({focus:()=>{t.current.focus()},blur:()=>{t.current.blur()}}),()=>{const{prefixCls:S,domRef:D,mode:L}=e,P={inputRef:t,onInputKeyDown:d,onInputMouseDown:m,onInputChange:T,onInputPaste:g,compositionStatus:l.value,onInputCompositionStart:C,onInputCompositionEnd:V},f=L==="multiple"||L==="tags"?$(eo,J(J({},e),P),null):$(to,J(J({},e),P),null);return $("div",{ref:D,class:`${S}-selector`,onClick:b,onMousedown:x},[f])}}});var ao=lo;function io(e,n,o){function t(l){var i,c,d;let m=l.target;m.shadowRoot&&l.composed&&(m=l.composedPath()[0]||m);const p=[(i=e[0])===null||i===void 0?void 0:i.value,(d=(c=e[1])===null||c===void 0?void 0:c.value)===null||d===void 0?void 0:d.getPopupElement()];n.value&&p.every(u=>u&&!u.contains(m)&&u!==m)&&o(!1)}we(()=>{window.addEventListener("mousedown",t)}),Ue(()=>{window.removeEventListener("mousedown",t)})}function ro(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10;const n=q(!1);let o;const t=()=>{clearTimeout(o)};return we(()=>{t()}),[n,(i,c)=>{t(),o=setTimeout(()=>{n.value=i,c&&c()},e)},t]}const Dn=Symbol("BaseSelectContextKey");function uo(e){return Xe(Dn,e)}function so(){return Fe(Dn,{})}function En(e){if(!ft(e))return We(e);const n=new Proxy({},{get(o,t,l){return Reflect.get(e.value,t,l)},set(o,t,l){return e.value[t]=l,!0},deleteProperty(o,t){return Reflect.deleteProperty(e.value,t)},has(o,t){return Reflect.has(e.value,t)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return We(n)}var co=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(o[t[l]]=e[t[l]]);return o};const fo=["value","onChange","removeIcon","placeholder","autofocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabindex","OptionList","notFoundContent"],po=()=>({prefixCls:String,id:String,omitDomProps:Array,displayValues:Array,onDisplayValuesChange:Function,activeValue:String,activeDescendantId:String,onActiveValueChange:Function,searchValue:String,onSearch:Function,onSearchSplit:Function,maxLength:Number,OptionList:I.any,emptyOptions:Boolean}),Vn=()=>({showSearch:{type:Boolean,default:void 0},tagRender:{type:Function},optionLabelRender:{type:Function},direction:{type:String},tabindex:Number,autofocus:Boolean,notFoundContent:I.any,placeholder:I.any,onClear:Function,choiceTransitionName:String,mode:String,disabled:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},onDropdownVisibleChange:{type:Function},getInputElement:{type:Function},getRawInputElement:{type:Function},maxTagTextLength:Number,maxTagCount:{type:[String,Number]},maxTagPlaceholder:I.any,tokenSeparators:{type:Array},allowClear:{type:Boolean,default:void 0},showArrow:{type:Boolean,default:void 0},inputIcon:I.any,clearIcon:I.any,removeIcon:I.any,animation:String,transitionName:String,dropdownStyle:{type:Object},dropdownClassName:String,dropdownMatchSelectWidth:{type:[Boolean,Number],default:void 0},dropdownRender:{type:Function},dropdownAlign:Object,placement:{type:String},getPopupContainer:{type:Function},showAction:{type:Array},onBlur:{type:Function},onFocus:{type:Function},onKeyup:Function,onKeydown:Function,onMousedown:Function,onPopupScroll:Function,onInputKeyDown:Function,onMouseenter:Function,onMouseleave:Function,onClick:Function}),mo=()=>h(h({},po()),Vn());function Mn(e){return e==="tags"||e==="multiple"}var go=pe({compatConfig:{MODE:3},name:"BaseSelect",inheritAttrs:!1,props:Ye(mo(),{showAction:[],notFoundContent:"Not Found"}),setup(e,n){let{attrs:o,expose:t,slots:l}=n;const i=E(()=>Mn(e.mode)),c=E(()=>e.showSearch!==void 0?e.showSearch:i.value||e.mode==="combobox"),d=q(!1);we(()=>{d.value=Ht()});const m=Ze(),p=q(null),u=Je(),C=q(null),V=q(null),T=q(null),g=ve(!1),[b,x,S]=ro();t({focus:()=>{var a;(a=V.value)===null||a===void 0||a.focus()},blur:()=>{var a;(a=V.value)===null||a===void 0||a.blur()},scrollTo:a=>{var r;return(r=T.value)===null||r===void 0?void 0:r.scrollTo(a)}});const P=E(()=>{var a;if(e.mode!=="combobox")return e.searchValue;const r=(a=e.displayValues[0])===null||a===void 0?void 0:a.value;return typeof r=="string"||typeof r=="number"?String(r):""}),f=e.open!==void 0?e.open:e.defaultOpen,O=q(f),w=q(f),F=a=>{O.value=e.open!==void 0?e.open:a,w.value=O.value};de(()=>e.open,()=>{F(e.open)});const R=E(()=>!e.notFoundContent&&e.emptyOptions);xe(()=>{w.value=O.value,(e.disabled||R.value&&w.value&&e.mode==="combobox")&&(w.value=!1)});const W=E(()=>R.value?!1:w.value),_=a=>{const r=a!==void 0?a:!w.value;w.value!==r&&!e.disabled&&(F(r),e.onDropdownVisibleChange&&e.onDropdownVisibleChange(r),!r&&N.value&&(N.value=!1,x(!1,()=>{te.value=!1,g.value=!1})))},ne=E(()=>(e.tokenSeparators||[]).some(a=>[`
`,`\r
`].includes(a))),K=(a,r,y)=>{var v,A;let X=!0,U=a;(v=e.onActiveValueChange)===null||v===void 0||v.call(e,null);const Q=y?null:jt(a,e.tokenSeparators);return e.mode!=="combobox"&&Q&&(U="",(A=e.onSearchSplit)===null||A===void 0||A.call(e,Q),_(!1),X=!1),e.onSearch&&P.value!==U&&e.onSearch(U,{source:r?"typing":"effect"}),X},j=a=>{var r;!a||!a.trim()||(r=e.onSearch)===null||r===void 0||r.call(e,a,{source:"submit"})};de(w,()=>{!w.value&&!i.value&&e.mode!=="combobox"&&K("",!1,!1)},{immediate:!0,flush:"post"}),de(()=>e.disabled,()=>{O.value&&!!e.disabled&&F(!1),e.disabled&&!g.value&&x(!1)},{immediate:!0});const[H,B]=Fn(),ae=function(a){var r;const y=H(),{which:v}=a;if(v===M.ENTER&&(e.mode!=="combobox"&&a.preventDefault(),w.value||_(!0)),B(!!P.value),v===M.BACKSPACE&&!y&&i.value&&!P.value&&e.displayValues.length){const Q=[...e.displayValues];let z=null;for(let oe=Q.length-1;oe>=0;oe-=1){const le=Q[oe];if(!le.disabled){Q.splice(oe,1),z=le;break}}z&&e.onDisplayValuesChange(Q,{type:"remove",values:[z]})}for(var A=arguments.length,X=new Array(A>1?A-1:0),U=1;U<A;U++)X[U-1]=arguments[U];w.value&&T.value&&T.value.onKeydown(a,...X),(r=e.onKeydown)===null||r===void 0||r.call(e,a,...X)},ie=function(a){for(var r=arguments.length,y=new Array(r>1?r-1:0),v=1;v<r;v++)y[v-1]=arguments[v];w.value&&T.value&&T.value.onKeyup(a,...y),e.onKeyup&&e.onKeyup(a,...y)},G=a=>{const r=e.displayValues.filter(y=>y!==a);e.onDisplayValuesChange(r,{type:"remove",values:[a]})},te=q(!1),Y=function(){x(!0),e.disabled||(e.onFocus&&!te.value&&e.onFocus(...arguments),e.showAction&&e.showAction.includes("focus")&&_(!0)),te.value=!0},N=ve(!1),Z=function(){if(N.value||(g.value=!0,x(!1,()=>{te.value=!1,g.value=!1,_(!1)}),e.disabled))return;const a=P.value;a&&(e.mode==="tags"?e.onSearch(a,{source:"submit"}):e.mode==="multiple"&&e.onSearch("",{source:"blur"})),e.onBlur&&e.onBlur(...arguments)},k=()=>{N.value=!0},re=()=>{N.value=!1};Xe("VCSelectContainerEvent",{focus:Y,blur:Z});const ee=[];we(()=>{ee.forEach(a=>clearTimeout(a)),ee.splice(0,ee.length)}),Ue(()=>{ee.forEach(a=>clearTimeout(a)),ee.splice(0,ee.length)});const ue=function(a){var r,y;const{target:v}=a,A=(r=C.value)===null||r===void 0?void 0:r.getPopupElement();if(A&&A.contains(v)){const z=setTimeout(()=>{var oe;const le=ee.indexOf(z);le!==-1&&ee.splice(le,1),S(),!d.value&&!A.contains(document.activeElement)&&((oe=V.value)===null||oe===void 0||oe.focus())});ee.push(z)}for(var X=arguments.length,U=new Array(X>1?X-1:0),Q=1;Q<X;Q++)U[Q-1]=arguments[Q];(y=e.onMousedown)===null||y===void 0||y.call(e,a,...U)},se=q(null),s=()=>{};return we(()=>{de(W,()=>{var a;if(W.value){const r=Math.ceil((a=p.value)===null||a===void 0?void 0:a.offsetWidth);se.value!==r&&!Number.isNaN(r)&&(se.value=r)}},{immediate:!0,flush:"post"})}),io([p,C],W,_),uo(En(h(h({},pt(e)),{open:w,triggerOpen:W,showSearch:c,multiple:i,toggleOpen:_}))),()=>{const a=h(h({},e),o),{prefixCls:r,id:y,open:v,defaultOpen:A,mode:X,showSearch:U,searchValue:Q,onSearch:z,allowClear:oe,clearIcon:le,showArrow:Oe,inputIcon:Ve,disabled:be,loading:Se,getInputElement:tn,getPopupContainer:_n,placement:zn,animation:Hn,transitionName:Wn,dropdownStyle:Kn,dropdownClassName:jn,dropdownMatchSelectWidth:Gn,dropdownRender:Un,dropdownAlign:Xn,showAction:Uo,direction:Yn,tokenSeparators:Xo,tagRender:Qn,optionLabelRender:qn,onPopupScroll:Yo,onDropdownVisibleChange:Qo,onFocus:qo,onBlur:Jo,onKeyup:Zo,onKeydown:ko,onMousedown:el,onClear:Me,omitDomProps:Re,getRawInputElement:on,displayValues:Pe,onDisplayValuesChange:Jn,emptyOptions:Zn,activeDescendantId:kn,activeValue:et,OptionList:nt}=a,tt=co(a,["prefixCls","id","open","defaultOpen","mode","showSearch","searchValue","onSearch","allowClear","clearIcon","showArrow","inputIcon","disabled","loading","getInputElement","getPopupContainer","placement","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","showAction","direction","tokenSeparators","tagRender","optionLabelRender","onPopupScroll","onDropdownVisibleChange","onFocus","onBlur","onKeyup","onKeydown","onMousedown","onClear","omitDomProps","getRawInputElement","displayValues","onDisplayValuesChange","emptyOptions","activeDescendantId","activeValue","OptionList"]),ln=X==="combobox"&&tn&&tn()||null,Ie=typeof on=="function"&&on(),Ne=h({},tt);let an;Ie&&(an=he=>{_(he)}),fo.forEach(he=>{delete Ne[he]}),Re==null||Re.forEach(he=>{delete Ne[he]});const rn=Oe!==void 0?Oe:Se||!i.value&&X!=="combobox";let un;rn&&(un=$(Te,{class:fe(`${r}-arrow`,{[`${r}-arrow-loading`]:Se}),customizeIcon:Ve,customizeIconProps:{loading:Se,searchValue:P.value,open:w.value,focused:b.value,showSearch:c.value}},null));let sn;const ot=()=>{Me==null||Me(),Jn([],{type:"clear",values:Pe}),K("",!1,!1)};!be&&oe&&(Pe.length||P.value)&&(sn=$(Te,{class:`${r}-clear`,onMousedown:ot,customizeIcon:le},{default:()=>[He("\xD7")]}));const lt=$(nt,{ref:T},h(h({},m.customSlots),{option:l.option})),at=fe(r,o.class,{[`${r}-focused`]:b.value,[`${r}-multiple`]:i.value,[`${r}-single`]:!i.value,[`${r}-allow-clear`]:oe,[`${r}-show-arrow`]:rn,[`${r}-disabled`]:be,[`${r}-loading`]:Se,[`${r}-open`]:w.value,[`${r}-customize-input`]:ln,[`${r}-show-search`]:c.value}),cn=$(Yt,{ref:C,disabled:be,prefixCls:r,visible:W.value,popupElement:lt,containerWidth:se.value,animation:Hn,transitionName:Wn,dropdownStyle:Kn,dropdownClassName:jn,direction:Yn,dropdownMatchSelectWidth:Gn,dropdownRender:Un,dropdownAlign:Xn,placement:zn,getPopupContainer:_n,empty:Zn,getTriggerDOMNode:()=>u.current,onPopupVisibleChange:an,onPopupMouseEnter:s,onPopupFocusin:k,onPopupFocusout:re},{default:()=>Ie?Qe(Ie)&&xn(Ie,{ref:u},!1,!0):$(ao,J(J({},e),{},{domRef:u,prefixCls:r,inputElement:ln,ref:V,id:y,showSearch:c.value,mode:X,activeDescendantId:kn,tagRender:Qn,optionLabelRender:qn,values:Pe,open:w.value,onToggleOpen:_,activeValue:et,searchValue:P.value,onSearch:K,onSearchSubmit:j,onRemove:G,tokenWithEnter:ne.value}),null)});let Be;return Ie?Be=cn:Be=$("div",J(J({},Ne),{},{class:at,ref:p,onMousedown:ue,onKeydown:ae,onKeyup:ie}),[b.value&&!w.value&&$("span",{style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0},"aria-live":"polite"},[`${Pe.map(he=>{let{label:dn,value:it}=he;return["number","string"].includes(typeof dn)?dn:it}).join(", ")}`]),cn,un,sn]),Be}}});function vo(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}const Rn=Symbol("SelectContextKey");function ho(e){return Xe(Rn,e)}function bo(){return Fe(Rn,{})}var So=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(o[t[l]]=e[t[l]]);return o};function yn(e){return typeof e=="string"||typeof e=="number"}const yo=pe({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,setup(e,n){let{expose:o,slots:t}=n;const l=so(),i=bo(),c=E(()=>`${l.prefixCls}-item`),d=Wt(()=>i.flattenOptions,[()=>l.open,()=>i.flattenOptions],f=>f[0]),m=Je(),p=f=>{f.preventDefault()},u=f=>{m.current&&m.current.scrollTo(typeof f=="number"?{index:f}:f)},C=function(f){let O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const w=d.value.length;for(let F=0;F<w;F+=1){const R=(f+F*O+w)%w,{group:W,data:_}=d.value[R];if(!W&&!_.disabled)return R}return-1},V=We({activeIndex:C(0)}),T=function(f){let O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;V.activeIndex=f;const w={source:O?"keyboard":"mouse"},F=d.value[f];if(!F){i.onActiveValue(null,-1,w);return}i.onActiveValue(F.value,f,w)};de([()=>d.value.length,()=>l.searchValue],()=>{T(i.defaultActiveFirstOption!==!1?C(0):-1)},{immediate:!0});const g=f=>i.rawValues.has(f)&&l.mode!=="combobox";de([()=>l.open,()=>l.searchValue],()=>{if(!l.multiple&&l.open&&i.rawValues.size===1){const f=Array.from(i.rawValues)[0],O=$n(d.value).findIndex(w=>{let{data:F}=w;return F[i.fieldNames.value]===f});O!==-1&&(T(O),fn(()=>{u(O)}))}l.open&&fn(()=>{var f;(f=m.current)===null||f===void 0||f.scrollTo(void 0)})},{immediate:!0,flush:"post"});const b=f=>{f!==void 0&&i.onSelect(f,{selected:!i.rawValues.has(f)}),l.multiple||l.toggleOpen(!1)},x=f=>typeof f.label=="function"?f.label():f.label;function S(f){const O=d.value[f];if(!O)return null;const w=O.data||{},{value:F}=w,{group:R}=O,W=Ge(w,!0),_=x(O);return O?$("div",J(J({"aria-label":typeof _=="string"&&!R?_:null},W),{},{key:f,role:R?"presentation":"option",id:`${l.id}_list_${f}`,"aria-selected":g(F)}),[F]):null}return o({onKeydown:f=>{const{which:O,ctrlKey:w}=f;switch(O){case M.N:case M.P:case M.UP:case M.DOWN:{let F=0;if(O===M.UP?F=-1:O===M.DOWN?F=1:vo()&&w&&(O===M.N?F=1:O===M.P&&(F=-1)),F!==0){const R=C(V.activeIndex+F,F);u(R),T(R,!0)}break}case M.ENTER:{const F=d.value[V.activeIndex];F&&!F.data.disabled?b(F.value):b(void 0),l.open&&f.preventDefault();break}case M.ESC:l.toggleOpen(!1),l.open&&f.stopPropagation()}},onKeyup:()=>{},scrollTo:f=>{u(f)}}),()=>{const{id:f,notFoundContent:O,onPopupScroll:w}=l,{menuItemSelectedIcon:F,fieldNames:R,virtual:W,listHeight:_,listItemHeight:ne}=i,K=t.option,{activeIndex:j}=V,H=Object.keys(R).map(B=>R[B]);return d.value.length===0?$("div",{role:"listbox",id:`${f}_list`,class:`${c.value}-empty`,onMousedown:p},[O]):$($e,null,[$("div",{role:"listbox",id:`${f}_list`,style:{height:0,width:0,overflow:"hidden"}},[S(j-1),S(j),S(j+1)]),$(zt,{itemKey:"key",ref:m,data:d.value,height:_,itemHeight:ne,fullHeight:!1,onMousedown:p,onScroll:w,virtual:W},{default:(B,ae)=>{var ie;const{group:G,groupOption:te,data:Y,value:N}=B,{key:Z}=Y,k=typeof B.label=="function"?B.label():B.label;if(G){const le=(ie=Y.title)!==null&&ie!==void 0?ie:yn(k)&&k;return $("div",{class:fe(c.value,`${c.value}-group`),title:le},[K?K(Y):k!==void 0?k:Z])}const{disabled:re,title:ee,children:ue,style:se,class:s,className:a}=Y,r=So(Y,["disabled","title","children","style","class","className"]),y=De(r,H),v=g(N),A=`${c.value}-option`,X=fe(c.value,A,s,a,{[`${A}-grouped`]:te,[`${A}-active`]:j===ae&&!re,[`${A}-disabled`]:re,[`${A}-selected`]:v}),U=x(B),Q=!F||typeof F=="function"||v,z=typeof U=="number"?U:U||N;let oe=yn(z)?z.toString():void 0;return ee!==void 0&&(oe=ee),$("div",J(J({},y),{},{"aria-selected":v,class:X,title:oe,onMousemove:le=>{r.onMousemove&&r.onMousemove(le),!(j===ae||re)&&T(ae)},onClick:le=>{re||b(N),r.onClick&&r.onClick(le)},style:se}),[$("div",{class:`${A}-content`},[K?K(Y):z]),Qe(F)||v,Q&&$(Te,{class:`${c.value}-option-state`,customizeIcon:F,customizeIconProps:{isSelected:v}},{default:()=>[v?"\u2713":null]})])}})])}}});var wo=yo,Io=globalThis&&globalThis.__rest||function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(o[t[l]]=e[t[l]]);return o};function Co(e){const n=e,{key:o,children:t}=n,l=n.props,{value:i,disabled:c}=l,d=Io(l,["value","disabled"]),m=t==null?void 0:t.default;return h({key:o,value:i!==void 0?i:o,children:m,disabled:c||c===""},d)}function Nn(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return mt(e).map((t,l)=>{var i;if(!Qe(t)||!t.type)return null;const{type:{isSelectOptGroup:c},key:d,children:m,props:p}=t;if(n||!c)return Co(t);const u=m&&m.default?m.default():void 0,C=(p==null?void 0:p.label)||((i=m.label)===null||i===void 0?void 0:i.call(m))||d;return h(h({key:`__RC_SELECT_GRP__${d===null?l:String(d)}__`},p),{label:C,options:Nn(u||[])})}).filter(t=>t)}function xo(e,n,o){const t=q(),l=q(),i=q(),c=q([]);return de([e,n],()=>{e.value?c.value=$n(e.value).slice():c.value=Nn(n.value)},{immediate:!0,deep:!0}),xe(()=>{const d=c.value,m=new Map,p=new Map,u=o.value;function C(V){let T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(let g=0;g<V.length;g+=1){const b=V[g];!b[u.options]||T?(m.set(b[u.value],b),p.set(b[u.label],b)):C(b[u.options],!0)}}C(d),t.value=d,l.value=m,i.value=p}),{options:t,valueOptions:l,labelOptions:i}}let wn=0;const $o=gt();function Oo(){let e;return $o?(e=wn,wn+=1):e="TEST_OR_SSR",e}function Po(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ve("");const n=`rc_select_${Oo()}`;return e.value||n}function Bn(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}function Ae(e,n){return Bn(e).join("").toUpperCase().includes(n)}var To=(e,n,o,t,l)=>E(()=>{const i=o.value,c=l==null?void 0:l.value,d=t==null?void 0:t.value;if(!i||d===!1)return e.value;const{options:m,label:p,value:u}=n.value,C=[],V=typeof d=="function",T=i.toUpperCase(),g=V?d:(x,S)=>c?Ae(S[c],T):S[m]?Ae(S[p!=="children"?p:"label"],T):Ae(S[u],T),b=V?x=>je(x):x=>x;return e.value.forEach(x=>{if(x[m]){if(g(i,b(x)))C.push(x);else{const D=x[m].filter(L=>g(i,b(L)));D.length&&C.push(h(h({},x),{[m]:D}))}return}g(i,b(x))&&C.push(x)}),C}),Fo=(e,n)=>{const o=q({values:new Map,options:new Map});return[E(()=>{const{values:i,options:c}=o.value,d=e.value.map(u=>{var C;return u.label===void 0?h(h({},u),{label:(C=i.get(u.value))===null||C===void 0?void 0:C.label}):u}),m=new Map,p=new Map;return d.forEach(u=>{m.set(u.value,u),p.set(u.value,n.value.get(u.value)||c.get(u.value))}),o.value.values=m,o.value.options=p,d}),i=>n.value.get(i)||o.value.options.get(i)]};const Do=["inputValue"];function An(){return h(h({},Vn()),{prefixCls:String,id:String,backfill:{type:Boolean,default:void 0},fieldNames:Object,inputValue:String,searchValue:String,onSearch:Function,autoClearSearchValue:{type:Boolean,default:void 0},onSelect:Function,onDeselect:Function,filterOption:{type:[Boolean,Function],default:void 0},filterSort:Function,optionFilterProp:String,optionLabelProp:String,options:Array,defaultActiveFirstOption:{type:Boolean,default:void 0},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,menuItemSelectedIcon:I.any,mode:String,labelInValue:{type:Boolean,default:void 0},value:I.any,defaultValue:I.any,onChange:Function,children:Array})}function Eo(e){return!e||typeof e!="object"}var Vo=pe({compatConfig:{MODE:3},name:"VcSelect",inheritAttrs:!1,props:Ye(An(),{prefixCls:"vc-select",autoClearSearchValue:!0,listHeight:200,listItemHeight:20,dropdownMatchSelectWidth:!0}),setup(e,n){let{expose:o,attrs:t,slots:l}=n;const i=Po(me(e,"id")),c=E(()=>Mn(e.mode)),d=E(()=>!!(!e.options&&e.children)),m=E(()=>e.filterOption===void 0&&e.mode==="combobox"?!1:e.filterOption),p=E(()=>Pn(e.fieldNames,d.value)),[u,C]=hn("",{value:E(()=>e.searchValue!==void 0?e.searchValue:e.inputValue),postState:s=>s||""}),V=xo(me(e,"options"),me(e,"children"),p),{valueOptions:T,labelOptions:g,options:b}=V,x=s=>Bn(s).map(r=>{var y,v;let A,X,U,Q;Eo(r)?A=r:(U=r.key,X=r.label,A=(y=r.value)!==null&&y!==void 0?y:U);const z=T.value.get(A);return z&&(X===void 0&&(X=z==null?void 0:z[e.optionLabelProp||p.value.label]),U===void 0&&(U=(v=z==null?void 0:z.key)!==null&&v!==void 0?v:A),Q=z==null?void 0:z.disabled),{label:X,value:A,key:U,disabled:Q,option:z}}),[S,D]=hn(e.defaultValue,{value:me(e,"value")}),L=E(()=>{var s;const a=x(S.value);return e.mode==="combobox"&&!(!((s=a[0])===null||s===void 0)&&s.value)?[]:a}),[P,f]=Fo(L,T),O=E(()=>{if(!e.mode&&P.value.length===1){const s=P.value[0];if(s.value===null&&(s.label===null||s.label===void 0))return[]}return P.value.map(s=>{var a;return h(h({},s),{label:(a=typeof s.label=="function"?s.label():s.label)!==null&&a!==void 0?a:s.value})})}),w=E(()=>new Set(P.value.map(s=>s.value)));xe(()=>{var s;if(e.mode==="combobox"){const a=(s=P.value[0])===null||s===void 0?void 0:s.value;a!=null&&C(String(a))}},{flush:"post"});const F=(s,a)=>{const r=a!=null?a:s;return{[p.value.value]:s,[p.value.label]:r}},R=q();xe(()=>{if(e.mode!=="tags"){R.value=b.value;return}const s=b.value.slice(),a=r=>T.value.has(r);[...P.value].sort((r,y)=>r.value<y.value?-1:1).forEach(r=>{const y=r.value;a(y)||s.push(F(y,r.label))}),R.value=s});const W=To(R,p,u,m,me(e,"optionFilterProp")),_=E(()=>e.mode!=="tags"||!u.value||W.value.some(s=>s[e.optionFilterProp||"value"]===u.value)?W.value:[F(u.value),...W.value]),ne=E(()=>e.filterSort?[..._.value].sort((s,a)=>e.filterSort(s,a)):_.value),K=E(()=>Kt(ne.value,{fieldNames:p.value,childrenAsData:d.value})),j=s=>{const a=x(s);if(D(a),e.onChange&&(a.length!==P.value.length||a.some((r,y)=>{var v;return((v=P.value[y])===null||v===void 0?void 0:v.value)!==(r==null?void 0:r.value)}))){const r=e.labelInValue?a.map(v=>h(h({},v),{originLabel:v.label,label:typeof v.label=="function"?v.label():v.label})):a.map(v=>v.value),y=a.map(v=>je(f(v.value)));e.onChange(c.value?r:r[0],c.value?y:y[0])}},[H,B]=pn(null),[ae,ie]=pn(0),G=E(()=>e.defaultActiveFirstOption!==void 0?e.defaultActiveFirstOption:e.mode!=="combobox"),te=function(s,a){let{source:r="keyboard"}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};ie(a),e.backfill&&e.mode==="combobox"&&s!==null&&r==="keyboard"&&B(String(s))},Y=(s,a)=>{const r=()=>{var y;const v=f(s),A=v==null?void 0:v[p.value.label];return[e.labelInValue?{label:typeof A=="function"?A():A,originLabel:A,value:s,key:(y=v==null?void 0:v.key)!==null&&y!==void 0?y:s}:s,je(v)]};if(a&&e.onSelect){const[y,v]=r();e.onSelect(y,v)}else if(!a&&e.onDeselect){const[y,v]=r();e.onDeselect(y,v)}},N=(s,a)=>{let r;const y=c.value?a.selected:!0;y?r=c.value?[...P.value,s]:[s]:r=P.value.filter(v=>v.value!==s),j(r),Y(s,y),e.mode==="combobox"?B(""):(!c.value||e.autoClearSearchValue)&&(C(""),B(""))},Z=(s,a)=>{j(s),(a.type==="remove"||a.type==="clear")&&a.values.forEach(r=>{Y(r.value,!1)})},k=(s,a)=>{var r;if(C(s),B(null),a.source==="submit"){const y=(s||"").trim();if(y){const v=Array.from(new Set([...w.value,y]));j(v),Y(y,!0),C("")}return}a.source!=="blur"&&(e.mode==="combobox"&&j(s),(r=e.onSearch)===null||r===void 0||r.call(e,s))},re=s=>{let a=s;e.mode!=="tags"&&(a=s.map(y=>{const v=g.value.get(y);return v==null?void 0:v.value}).filter(y=>y!==void 0));const r=Array.from(new Set([...w.value,...a]));j(r),r.forEach(y=>{Y(y,!0)})},ee=E(()=>e.virtual!==!1&&e.dropdownMatchSelectWidth!==!1);ho(En(h(h({},V),{flattenOptions:K,onActiveValue:te,defaultActiveFirstOption:G,onSelect:N,menuItemSelectedIcon:me(e,"menuItemSelectedIcon"),rawValues:w,fieldNames:p,virtual:ee,listHeight:me(e,"listHeight"),listItemHeight:me(e,"listItemHeight"),childrenAsData:d})));const ue=ve();o({focus(){var s;(s=ue.value)===null||s===void 0||s.focus()},blur(){var s;(s=ue.value)===null||s===void 0||s.blur()},scrollTo(s){var a;(a=ue.value)===null||a===void 0||a.scrollTo(s)}});const se=E(()=>De(e,["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","listHeight","listItemHeight","value","defaultValue","labelInValue","onChange"]));return()=>$(go,J(J(J({},se.value),t),{},{id:i,prefixCls:e.prefixCls,ref:ue,omitDomProps:Do,mode:e.mode,displayValues:O.value,onDisplayValuesChange:Z,searchValue:u.value,onSearch:k,onSearchSplit:re,dropdownMatchSelectWidth:e.dropdownMatchSelectWidth,OptionList:wo,emptyOptions:!K.value.length,activeValue:H.value,activeDescendantId:`${i}_list_${ae.value}`}),l)}});const en=()=>null;en.isSelectOption=!0;en.displayName="ASelectOption";var Mo=en;const nn=()=>null;nn.isSelectOptGroup=!0;nn.displayName="ASelectOptGroup";var Ro=nn;function No(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{loading:o,multiple:t,prefixCls:l,hasFeedback:i,feedbackIcon:c,showArrow:d}=e,m=e.suffixIcon||n.suffixIcon&&n.suffixIcon(),p=e.clearIcon||n.clearIcon&&n.clearIcon(),u=e.menuItemSelectedIcon||n.menuItemSelectedIcon&&n.menuItemSelectedIcon(),C=e.removeIcon||n.removeIcon&&n.removeIcon(),V=p!=null?p:$(vt,null,null),T=S=>$($e,null,[d!==!1&&S,i&&c]);let g=null;if(m!==void 0)g=T(m);else if(o)g=T($(ht,{spin:!0},null));else{const S=`${l}-suffix`;g=D=>{let{open:L,showSearch:P}=D;return T(L&&P?$(yt,{class:S},null):$(wt,{class:S},null))}}let b=null;u!==void 0?b=u:t?b=$(bt,null,null):b=null;let x=null;return C!==void 0?x=C:x=$(St,null,null),{clearIcon:V,suffixIcon:g,itemIcon:b,removeIcon:x}}const In=e=>{const{controlPaddingHorizontal:n}=e;return{position:"relative",display:"block",minHeight:e.controlHeight,padding:`${(e.controlHeight-e.fontSize*e.lineHeight)/2}px ${n}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,boxSizing:"border-box"}},Bo=e=>{const{antCls:n,componentCls:o}=e,t=`${o}-item`;return[{[`${o}-dropdown`]:h(h({},qe(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
            &${n}-slide-up-enter${n}-slide-up-enter-active${o}-dropdown-placement-bottomLeft,
            &${n}-slide-up-appear${n}-slide-up-appear-active${o}-dropdown-placement-bottomLeft
          `]:{animationName:It},[`
            &${n}-slide-up-enter${n}-slide-up-enter-active${o}-dropdown-placement-topLeft,
            &${n}-slide-up-appear${n}-slide-up-appear-active${o}-dropdown-placement-topLeft
          `]:{animationName:Ct},[`&${n}-slide-up-leave${n}-slide-up-leave-active${o}-dropdown-placement-bottomLeft`]:{animationName:xt},[`&${n}-slide-up-leave${n}-slide-up-leave-active${o}-dropdown-placement-topLeft`]:{animationName:$t},"&-hidden":{display:"none"},"&-empty":{color:e.colorTextDisabled},[`${t}-empty`]:h(h({},In(e)),{color:e.colorTextDisabled}),[`${t}`]:h(h({},In(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":h({flex:"auto"},Ke),"&-state":{flex:"none"},[`&-active:not(${t}-option-disabled)`]:{backgroundColor:e.controlItemBgHover},[`&-selected:not(${t}-option-disabled)`]:{color:e.colorText,fontWeight:e.fontWeightStrong,backgroundColor:e.controlItemBgActive,[`${t}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${t}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.controlPaddingHorizontal*2}}}),"&-rtl":{direction:"rtl"}})},mn(e,"slide-up"),mn(e,"slide-down"),gn(e,"move-up"),gn(e,"move-down")]};var Ao=Bo;const ye=2;function Ln(e){let{controlHeightSM:n,controlHeight:o,lineWidth:t}=e;const l=(o-n)/2-t,i=Math.ceil(l/2);return[l,i]}function Le(e,n){const{componentCls:o,iconCls:t}=e,l=`${o}-selection-overflow`,i=e.controlHeightSM,[c]=Ln(e),d=n?`${o}-${n}`:"";return{[`${o}-multiple${d}`]:{fontSize:e.fontSize,[l]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"}},[`${o}-selector`]:{display:"flex",flexWrap:"wrap",alignItems:"center",padding:`${c-ye}px ${ye*2}px`,borderRadius:e.borderRadius,[`${o}-show-search&`]:{cursor:"text"},[`${o}-disabled&`]:{background:e.colorBgContainerDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${ye}px 0`,lineHeight:`${i}px`,content:'"\\a0"'}},[`
        &${o}-show-arrow ${o}-selector,
        &${o}-allow-clear ${o}-selector
      `]:{paddingInlineEnd:e.fontSizeIcon+e.controlPaddingHorizontal},[`${o}-selection-item`]:{position:"relative",display:"flex",flex:"none",boxSizing:"border-box",maxWidth:"100%",height:i,marginTop:ye,marginBottom:ye,lineHeight:`${i-e.lineWidth*2}px`,background:e.colorFillSecondary,border:`${e.lineWidth}px solid ${e.colorSplit}`,borderRadius:e.borderRadiusSM,cursor:"default",transition:`font-size ${e.motionDurationSlow}, line-height ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,userSelect:"none",marginInlineEnd:ye*2,paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS/2,[`${o}-disabled&`]:{color:e.colorTextDisabled,borderColor:e.colorBorder,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.paddingXS/2,overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":h(h({},On()),{display:"inline-block",color:e.colorIcon,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${t}`]:{verticalAlign:"-0.2em"},"&:hover":{color:e.colorIconHover}})},[`${l}-item + ${l}-item`]:{[`${o}-selection-search`]:{marginInlineStart:0}},[`${o}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.inputPaddingHorizontalBase-c,[`
          &-input,
          &-mirror
        `]:{height:i,fontFamily:e.fontFamily,lineHeight:`${i}px`,transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${o}-selection-placeholder `]:{position:"absolute",top:"50%",insetInlineStart:e.inputPaddingHorizontalBase,insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}}}}function Lo(e){const{componentCls:n}=e,o=ge(e,{controlHeight:e.controlHeightSM,controlHeightSM:e.controlHeightXS,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),[,t]=Ln(e);return[Le(e),Le(o,"sm"),{[`${n}-multiple${n}-sm`]:{[`${n}-selection-placeholder`]:{insetInlineStart:e.controlPaddingHorizontalSM-e.lineWidth,insetInlineEnd:"auto"},[`${n}-selection-search`]:{marginInlineStart:t}}},Le(ge(e,{fontSize:e.fontSizeLG,controlHeight:e.controlHeightLG,controlHeightSM:e.controlHeight,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius}),"lg")]}function _e(e,n){const{componentCls:o,inputPaddingHorizontalBase:t,borderRadius:l}=e,i=e.controlHeight-e.lineWidth*2,c=Math.ceil(e.fontSize*1.25),d=n?`${o}-${n}`:"";return{[`${o}-single${d}`]:{fontSize:e.fontSize,[`${o}-selector`]:h(h({},qe(e)),{display:"flex",borderRadius:l,[`${o}-selection-search`]:{position:"absolute",top:0,insetInlineStart:t,insetInlineEnd:t,bottom:0,"&-input":{width:"100%"}},[`
          ${o}-selection-item,
          ${o}-selection-placeholder
        `]:{padding:0,lineHeight:`${i}px`,transition:`all ${e.motionDurationSlow}`,"@supports (-moz-appearance: meterbar)":{lineHeight:`${i}px`}},[`${o}-selection-item`]:{position:"relative",userSelect:"none"},[`${o}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${o}-selection-item:after`,`${o}-selection-placeholder:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${o}-show-arrow ${o}-selection-item,
        &${o}-show-arrow ${o}-selection-placeholder
      `]:{paddingInlineEnd:c},[`&${o}-open ${o}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${o}-customize-input)`]:{[`${o}-selector`]:{width:"100%",height:e.controlHeight,padding:`0 ${t}px`,[`${o}-selection-search-input`]:{height:i},"&:after":{lineHeight:`${i}px`}}},[`&${o}-customize-input`]:{[`${o}-selector`]:{"&:after":{display:"none"},[`${o}-selection-search`]:{position:"static",width:"100%"},[`${o}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${t}px`,"&:after":{display:"none"}}}}}}}function _o(e){const{componentCls:n}=e,o=e.controlPaddingHorizontalSM-e.lineWidth;return[_e(e),_e(ge(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${n}-single${n}-sm`]:{[`&:not(${n}-customize-input)`]:{[`${n}-selection-search`]:{insetInlineStart:o,insetInlineEnd:o},[`${n}-selector`]:{padding:`0 ${o}px`},[`&${n}-show-arrow ${n}-selection-search`]:{insetInlineEnd:o+e.fontSize*1.5},[`
            &${n}-show-arrow ${n}-selection-item,
            &${n}-show-arrow ${n}-selection-placeholder
          `]:{paddingInlineEnd:e.fontSize*1.5}}}},_e(ge(e,{controlHeight:e.controlHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const zo=e=>{const{componentCls:n}=e;return{position:"relative",backgroundColor:e.colorBgContainer,border:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${n}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit"}},[`${n}-disabled&`]:{color:e.colorTextDisabled,background:e.colorBgContainerDisabled,cursor:"not-allowed",[`${n}-multiple&`]:{background:e.colorBgContainerDisabled},input:{cursor:"not-allowed"}}}},ze=function(e,n){let o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;const{componentCls:t,borderHoverColor:l,outlineColor:i,antCls:c}=n,d=o?{[`${t}-selector`]:{borderColor:l}}:{};return{[e]:{[`&:not(${t}-disabled):not(${t}-customize-input):not(${c}-pagination-size-changer)`]:h(h({},d),{[`${t}-focused& ${t}-selector`]:{borderColor:l,boxShadow:`0 0 0 ${n.controlOutlineWidth}px ${i}`,borderInlineEndWidth:`${n.controlLineWidth}px !important`,outline:0},[`&:hover ${t}-selector`]:{borderColor:l,borderInlineEndWidth:`${n.controlLineWidth}px !important`}})}}},Ho=e=>{const{componentCls:n}=e;return{[`${n}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none","&::-webkit-search-cancel-button":{display:"none","-webkit-appearance":"none"}}}},Wo=e=>{const{componentCls:n,inputPaddingHorizontalBase:o,iconCls:t}=e;return{[n]:h(h({},qe(e)),{position:"relative",display:"inline-block",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:h(h({},zo(e)),Ho(e)),[`${n}-selection-item`]:h({flex:1,fontWeight:"normal"},Ke),[`${n}-selection-placeholder`]:h(h({},Ke),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:h(h({},On()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:-e.fontSizeIcon/2,color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",[t]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:-e.fontSizeIcon/2,color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",background:e.colorBgContainer,cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}},"&:hover":{[`${n}-clear`]:{opacity:1}}}),[`${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:o+e.fontSize+e.paddingXXS}}}},Ko=e=>{const{componentCls:n}=e;return[{[n]:{[`&-borderless ${n}-selector`]:{backgroundColor:"transparent !important",borderColor:"transparent !important",boxShadow:"none !important"},[`&${n}-in-form-item`]:{width:"100%"}}},Wo(e),_o(e),Lo(e),Ao(e),{[`${n}-rtl`]:{direction:"rtl"}},ze(n,ge(e,{borderHoverColor:e.colorPrimaryHover,outlineColor:e.controlOutline})),ze(`${n}-status-error`,ge(e,{borderHoverColor:e.colorErrorHover,outlineColor:e.colorErrorOutline}),!0),ze(`${n}-status-warning`,ge(e,{borderHoverColor:e.colorWarningHover,outlineColor:e.colorWarningOutline}),!0),Pt(e,{borderElCls:`${n}-selector`,focusElCls:`${n}-focused`})]};var jo=Ot("Select",(e,n)=>{let{rootPrefixCls:o}=n;const t=ge(e,{rootPrefixCls:o,inputPaddingHorizontalBase:e.paddingSM-1});return[Ko(t)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));const Go=()=>h(h({},De(An(),["inputIcon","mode","getInputElement","getRawInputElement","backfill"])),{value:vn([Array,Object,String,Number]),defaultValue:vn([Array,Object,String,Number]),notFoundContent:I.any,suffixIcon:I.any,itemIcon:I.any,size:Ce(),mode:Ce(),bordered:Rt(!0),transitionName:String,choiceTransitionName:Ce(""),popupClassName:String,dropdownClassName:String,placement:Ce(),status:Ce(),"onUpdate:value":Nt()}),Cn="SECRET_COMBOBOX_MODE_DO_NOT_USE",ce=pe({compatConfig:{MODE:3},name:"ASelect",Option:Mo,OptGroup:Ro,inheritAttrs:!1,props:Ye(Go(),{listHeight:256,listItemHeight:24}),SECRET_COMBOBOX_MODE_DO_NOT_USE:Cn,slots:Object,setup(e,n){let{attrs:o,emit:t,slots:l,expose:i}=n;const c=ve(),d=Tt(),m=Ft.useInject(),p=E(()=>Bt(m.status,e.status)),u=()=>{var N;(N=c.value)===null||N===void 0||N.focus()},C=()=>{var N;(N=c.value)===null||N===void 0||N.blur()},V=N=>{var Z;(Z=c.value)===null||Z===void 0||Z.scrollTo(N)},T=E(()=>{const{mode:N}=e;if(N!=="combobox")return N===Cn?"combobox":N}),{prefixCls:g,direction:b,configProvider:x,renderEmpty:S,size:D,getPrefixCls:L,getPopupContainer:P,disabled:f,select:O}=Dt("select",e),{compactSize:w,compactItemClassnames:F}=Et(g,b),R=E(()=>w.value||D.value),W=Vt(),_=E(()=>{var N;return(N=f.value)!==null&&N!==void 0?N:W.value}),[ne,K]=jo(g),j=E(()=>L()),H=E(()=>e.placement!==void 0?e.placement:b.value==="rtl"?"bottomRight":"bottomLeft"),B=E(()=>At(j.value,Lt(H.value),e.transitionName)),ae=E(()=>fe({[`${g.value}-lg`]:R.value==="large",[`${g.value}-sm`]:R.value==="small",[`${g.value}-rtl`]:b.value==="rtl",[`${g.value}-borderless`]:!e.bordered,[`${g.value}-in-form-item`]:m.isFormItemInput},_t(g.value,p.value,m.hasFeedback),F.value,K.value)),ie=function(){for(var N=arguments.length,Z=new Array(N),k=0;k<N;k++)Z[k]=arguments[k];t("update:value",Z[0]),t("change",...Z),d.onFieldChange()},G=N=>{t("blur",N),d.onFieldBlur()};i({blur:C,focus:u,scrollTo:V});const te=E(()=>T.value==="multiple"||T.value==="tags"),Y=E(()=>e.showArrow!==void 0?e.showArrow:e.loading||!(te.value||T.value==="combobox"));return()=>{var N,Z,k,re;const{notFoundContent:ee,listHeight:ue=256,listItemHeight:se=24,popupClassName:s,dropdownClassName:a,virtual:r,dropdownMatchSelectWidth:y,id:v=d.id.value,placeholder:A=(N=l.placeholder)===null||N===void 0?void 0:N.call(l),showArrow:X}=e,{hasFeedback:U,feedbackIcon:Q}=m;let z;ee!==void 0?z=ee:l.notFoundContent?z=l.notFoundContent():T.value==="combobox"?z=null:z=(S==null?void 0:S("Select"))||$(Mt,{componentName:"Select"},null);const{suffixIcon:oe,itemIcon:le,removeIcon:Oe,clearIcon:Ve}=No(h(h({},e),{multiple:te.value,prefixCls:g.value,hasFeedback:U,feedbackIcon:Q,showArrow:Y.value}),l),be=De(e,["prefixCls","suffixIcon","itemIcon","removeIcon","clearIcon","size","bordered","status"]),Se=fe(s||a,{[`${g.value}-dropdown-${b.value}`]:b.value==="rtl"},K.value);return ne($(Vo,J(J(J({ref:c,virtual:r,dropdownMatchSelectWidth:y},be),o),{},{showSearch:(Z=e.showSearch)!==null&&Z!==void 0?Z:(k=O==null?void 0:O.value)===null||k===void 0?void 0:k.showSearch,placeholder:A,listHeight:ue,listItemHeight:se,mode:T.value,prefixCls:g.value,direction:b.value,inputIcon:oe,menuItemSelectedIcon:le,removeIcon:Oe,clearIcon:Ve,notFoundContent:z,class:[ae.value,o.class],getPopupContainer:P==null?void 0:P.value,dropdownClassName:Se,onChange:ie,onBlur:G,id:v,dropdownRender:be.dropdownRender||l.dropdownRender,transitionName:B.value,children:(re=l.default)===null||re===void 0?void 0:re.call(l),tagRender:e.tagRender||l.tagRender,optionLabelRender:l.optionLabel,maxTagPlaceholder:e.maxTagPlaceholder||l.maxTagPlaceholder,showArrow:U||X,disabled:_.value}),{option:l.option}))}}});ce.install=function(e){return e.component(ce.name,ce),e.component(ce.Option.displayName,ce.Option),e.component(ce.OptGroup.displayName,ce.OptGroup),e};const il=ce.Option;ce.OptGroup;export{il as S,ce as a,Go as s};
