import{b as i,Y,h as O,aJ as q,bf as K,bg as Z,bh as k,M as c,a5 as ee,a6 as te,a7 as le,a8 as ne,V as B,d as V,u as oe,bi as ie,bj as ae,E as se,X as re,aQ as X,H as de,e as G,ab as ce,bk as F,a3 as pe,bl as ue}from"./index.8746381c.js";function I(e){return e!=null}const me=e=>{const{itemPrefixCls:t,component:l,span:o,labelStyle:n,contentStyle:a,bordered:d,label:r,content:s,colon:u}=e,p=l;return d?i(p,{class:[{[`${t}-item-label`]:I(r),[`${t}-item-content`]:I(s)}],colSpan:o},{default:()=>[I(r)&&i("span",{style:n},[r]),I(s)&&i("span",{style:a},[s])]}):i(p,{class:[`${t}-item`],colSpan:o},{default:()=>[i("div",{class:`${t}-item-container`},[(r||r===0)&&i("span",{class:[`${t}-item-label`,{[`${t}-item-no-colon`]:!u}],style:n},[r]),(s||s===0)&&i("span",{class:`${t}-item-content`,style:a},[s])])]})};var _=me;const be=e=>{const t=(u,p,L)=>{let{colon:m,prefixCls:x,bordered:f}=p,{component:y,type:w,showLabel:M,showContent:P,labelStyle:g,contentStyle:S}=L;return u.map((b,h)=>{var $,v;const j=b.props||{},{prefixCls:z=x,span:R=1,labelStyle:A=j["label-style"],contentStyle:H=j["content-style"],label:N=(v=($=b.children)===null||$===void 0?void 0:$.label)===null||v===void 0?void 0:v.call($)}=j,W=K(b),D=Z(b),E=k(b),{key:T}=b;return typeof y=="string"?i(_,{key:`${w}-${String(T)||h}`,class:D,style:E,labelStyle:c(c({},g),A),contentStyle:c(c({},S),H),span:R,colon:m,component:y,itemPrefixCls:z,bordered:f,label:M?N:null,content:P?W:null},null):[i(_,{key:`label-${String(T)||h}`,class:D,style:c(c(c({},g),E),A),span:1,colon:m,component:y[0],itemPrefixCls:z,bordered:f,label:N},null),i(_,{key:`content-${String(T)||h}`,class:D,style:c(c(c({},S),E),H),span:R*2-1,component:y[1],itemPrefixCls:z,bordered:f,content:W},null)]})},{prefixCls:l,vertical:o,row:n,index:a,bordered:d}=e,{labelStyle:r,contentStyle:s}=Y(Q,{labelStyle:O({}),contentStyle:O({})});return o?i(q,null,[i("tr",{key:`label-${a}`,class:`${l}-row`},[t(n,e,{component:"th",type:"label",showLabel:!0,labelStyle:r.value,contentStyle:s.value})]),i("tr",{key:`content-${a}`,class:`${l}-row`},[t(n,e,{component:"td",type:"content",showContent:!0,labelStyle:r.value,contentStyle:s.value})])]):i("tr",{key:a,class:`${l}-row`},[t(n,e,{component:d?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0,labelStyle:r.value,contentStyle:s.value})])};var fe=be;const ye=e=>{const{componentCls:t,descriptionsSmallPadding:l,descriptionsDefaultPadding:o,descriptionsMiddlePadding:n,descriptionsBg:a}=e;return{[`&${t}-bordered`]:{[`${t}-view`]:{border:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto",borderCollapse:"collapse"}},[`${t}-item-label, ${t}-item-content`]:{padding:o,borderInlineEnd:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`${t}-item-label`]:{backgroundColor:a,"&::after":{display:"none"}},[`${t}-row`]:{borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBottom:"none"}},[`&${t}-middle`]:{[`${t}-item-label, ${t}-item-content`]:{padding:n}},[`&${t}-small`]:{[`${t}-item-label, ${t}-item-content`]:{padding:l}}}}},ge=e=>{const{componentCls:t,descriptionsExtraColor:l,descriptionItemPaddingBottom:o,descriptionsItemLabelColonMarginRight:n,descriptionsItemLabelColonMarginLeft:a,descriptionsTitleMarginBottom:d}=e;return{[t]:c(c(c({},le(e)),ye(e)),{["&-rtl"]:{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:d},[`${t}-title`]:c(c({},ne),{flex:"auto",color:e.colorText,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed"}},[`${t}-row`]:{"> th, > td":{paddingBottom:o},"&:last-child":{borderBottom:"none"}},[`${t}-item-label`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${a}px ${n}px`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}};var Se=ee("Descriptions",e=>{const t=e.colorFillAlter,l=e.fontSizeSM*e.lineHeightSM,o=e.colorText,n=`${e.paddingXS}px ${e.padding}px`,a=`${e.padding}px ${e.paddingLG}px`,d=`${e.paddingSM}px ${e.paddingLG}px`,r=e.padding,s=e.marginXS,u=e.marginXXS/2,p=te(e,{descriptionsBg:t,descriptionsTitleMarginBottom:l,descriptionsExtraColor:o,descriptionItemPaddingBottom:r,descriptionsSmallPadding:n,descriptionsDefaultPadding:a,descriptionsMiddlePadding:d,descriptionsItemLabelColonMarginRight:s,descriptionsItemLabelColonMarginLeft:u});return[ge(p)]});B.any;const $e=()=>({prefixCls:String,label:B.any,labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0},span:{type:Number,default:1}}),ve=V({compatConfig:{MODE:3},name:"ADescriptionsItem",props:$e(),setup(e,t){let{slots:l}=t;return()=>{var o;return(o=l.default)===null||o===void 0?void 0:o.call(l)}}}),J={xxxl:3,xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};function xe(e,t){if(typeof e=="number")return e;if(typeof e=="object")for(let l=0;l<F.length;l++){const o=F[l];if(t[o]&&e[o]!==void 0)return e[o]||J[o]}return 3}function U(e,t,l){let o=e;return(l===void 0||l>t)&&(o=pe(e,{span:t}),ue()),o}function he(e,t){const l=ce(e),o=[];let n=[],a=t;return l.forEach((d,r)=>{var s;const u=(s=d.props)===null||s===void 0?void 0:s.span,p=u||1;if(r===l.length-1){n.push(U(d,a,u)),o.push(n);return}p<a?(a-=p,n.push(d)):(n.push(U(d,a,p)),o.push(n),a=t,n=[])}),o}const Ce=()=>({prefixCls:String,bordered:{type:Boolean,default:void 0},size:{type:String,default:"default"},title:B.any,extra:B.any,column:{type:[Number,Object],default:()=>J},layout:String,colon:{type:Boolean,default:void 0},labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0}}),Q=Symbol("descriptionsContext"),C=V({compatConfig:{MODE:3},name:"ADescriptions",inheritAttrs:!1,props:Ce(),slots:Object,Item:ve,setup(e,t){let{slots:l,attrs:o}=t;const{prefixCls:n,direction:a}=oe("descriptions",e);let d;const r=O({}),[s,u]=Se(n),p=ie();ae(()=>{d=p.value.subscribe(m=>{typeof e.column=="object"&&(r.value=m)})}),se(()=>{p.value.unsubscribe(d)}),re(Q,{labelStyle:X(e,"labelStyle"),contentStyle:X(e,"contentStyle")});const L=de(()=>xe(e.column,r.value));return()=>{var m,x,f;const{size:y,bordered:w=!1,layout:M="horizontal",colon:P=!0,title:g=(m=l.title)===null||m===void 0?void 0:m.call(l),extra:S=(x=l.extra)===null||x===void 0?void 0:x.call(l)}=e,b=(f=l.default)===null||f===void 0?void 0:f.call(l),h=he(b,L.value);return s(i("div",G(G({},o),{},{class:[n.value,{[`${n.value}-${y}`]:y!=="default",[`${n.value}-bordered`]:!!w,[`${n.value}-rtl`]:a.value==="rtl"},o.class,u.value]}),[(g||S)&&i("div",{class:`${n.value}-header`},[g&&i("div",{class:`${n.value}-title`},[g]),S&&i("div",{class:`${n.value}-extra`},[S])]),i("div",{class:`${n.value}-view`},[i("table",null,[i("tbody",null,[h.map(($,v)=>i(fe,{key:v,index:v,colon:P,prefixCls:n.value,vertical:M==="vertical",bordered:w,row:$},null))])])])]))}}});C.install=function(e){return e.component(C.name,C),e.component(C.Item.name,C.Item),e};export{ve as D,C as a};
