import{r as F,I as v}from"./manage.2dfb5a24.js";import{e as R,g as x,r as w,f as O,o as p,C as E,w as t,b as o,d as f,a as C,I as h,j as P,R as $,k as M,F as T,a7 as q,M as I,c as K,E as _,m as y,B as z,$ as H}from"./index.fba97cfa.js";const J=R({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>{}}},setup(G,{expose:D}){const{$infoBox:i,$access:L}=x().appContext.config.globalProperties,n=G,e=w({confirmLoading:!1,isAdd:!0,isShow:!1,saveObject:{autoDivisionFlag:0},recordId:null,rules:{receiverGroupName:[{required:!0,message:"\u8BF7\u8F93\u5165\u7EC4\u540D\u79F0",trigger:"blur"}]}}),r=O();function A(s){e.isAdd=!s,e.saveObject={autoDivisionFlag:0},e.confirmLoading=!1,r.value&&r.value.resetFields(),e.isAdd?e.isShow=!0:(e.recordId=s,F.getById(v,s).then(u=>{e.saveObject=u,e.isShow=!0}))}function g(){r.value.validate().then(s=>{s&&(e.confirmLoading=!0,e.isAdd?F.add(v,e.saveObject).then(u=>{i.message.success("\u6DFB\u52A0\u6210\u529F"),e.isShow=!1,n.callbackFunc()}).catch(u=>{e.confirmLoading=!1}):F.updateById(v,e.recordId,e.saveObject).then(u=>{i.message.success("\u4FEE\u6539\u6210\u529F"),e.isShow=!1,n.callbackFunc()}).catch(u=>{e.confirmLoading=!1}))})}return D({show:A}),(s,u)=>{const B=h,l=P,a=$,b=M,d=T,k=q;return p(),E(k,{open:e.isShow,"onUpdate:open":u[2]||(u[2]=c=>e.isShow=c),title:e.isAdd?"\u65B0\u589E\u83DC\u5355":"\u4FEE\u6539\u83DC\u5355",onOk:g,confirmLoading:e.confirmLoading},{default:t(()=>[o(d,{ref_key:"infoFormModel",ref:r,model:e.saveObject,"label-col":{span:6},"wrapper-col":{span:15},rules:e.rules},{default:t(()=>[o(l,{label:"\u7EC4\u540D\u79F0\uFF1A",name:"receiverGroupName"},{default:t(()=>[o(B,{value:e.saveObject.receiverGroupName,"onUpdate:value":u[0]||(u[0]=c=>e.saveObject.receiverGroupName=c)},null,8,["value"])]),_:1}),o(l,{label:"\u81EA\u52A8\u5206\u8D26\u7EC4",name:"autoDivisionFlag"},{default:t(()=>[o(b,{value:e.saveObject.autoDivisionFlag,"onUpdate:value":u[1]||(u[1]=c=>e.saveObject.autoDivisionFlag=c)},{default:t(()=>[o(a,{value:1},{default:t(()=>u[3]||(u[3]=[f("\u662F")])),_:1}),o(a,{value:0},{default:t(()=>u[4]||(u[4]=[f("\u5426")])),_:1})]),_:1},8,["value"]),u[5]||(u[5]=C("hr",null,null,-1)),u[6]||(u[6]=C("p",{style:{color:"indianred"}}," 1. \u81EA\u52A8\u5206\u8D26\u7EC4: \u5F53\u8BA2\u5355\u5206\u8D26\u6A21\u5F0F\u4E3A\u81EA\u52A8\u5206\u8D26\uFF0C\u8BE5\u7EC4\u4E0B\u7684\u6240\u6709\u6B63\u5E38\u5206\u8D26\u72B6\u6001\u7684\u8D26\u53F7\u5C06\u4F5C\u4E3A\u8BA2\u5355\u5206\u8D26\u5BF9\u8C61 ",-1)),u[7]||(u[7]=C("p",{style:{color:"indianred"}}," 2. \u6BCF\u4E2A\u5546\u6237\u4EC5\u6709\u4E00\u4E2A\u9ED8\u8BA4\u5206\u8D26\u7EC4\uFF0C \u5F53\u8BE5\u7EC4\u66F4\u65B0\u4E3A\u81EA\u52A8\u5206\u8D26\u65F6\uFF0C\u5176\u4ED6\u7EC4\u5C06\u6539\u4E3A\u5426 ",-1))]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["open","title","confirmLoading"])}}}),Q={key:0,class:"table-page-search-wrapper"},W={class:"table-layer"},X={class:"table-page-search-submitButtons"},ee=R({__name:"DivisionReceiverGroupPage",setup(G){const{$infoBox:D,$access:i}=x().appContext.config.globalProperties,n=w({tableColumns:[{key:"receiverGroupId",dataIndex:"receiverGroupId",title:"\u7EC4ID"},{key:"receiverGroupName",dataIndex:"receiverGroupName",title:"\u7EC4\u540D\u79F0"},{key:"autoDivisionFlag",dataIndex:"autoDivisionFlag",title:"\u81EA\u52A8\u5206\u8D26\u7EC4",customRender:({text:l})=>l===1?"\u662F":"\u5426"},{key:"createdBy",dataIndex:"createdBy",title:"\u521B\u5EFA\u4EBA"},{key:"createdAt",dataIndex:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4"},{key:"op",title:"\u64CD\u4F5C",width:"200px",fixed:"right",align:"center"}],searchData:{},btnLoading:!1}),e=O(),r=O();function A(l){return F.list(v,l)}function g(){n.btnLoading=!0,e.value.refTable(!0)}function s(){r.value.show()}function u(l){r.value.show(l)}function B(l){D.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>F.delById(v,l).then(a=>{D.message.success("\u5220\u9664\u6210\u529F\uFF01"),e.value.refTable(!1)}))}return(l,a)=>{const b=y("cloudpay-text-up"),d=z,k=T,c=y("cloudpayTableColumns"),V=y("cloudpayTable"),S=H,j=y("page-header-wrapper");return p(),E(j,null,{default:t(()=>[o(S,null,{default:t(()=>[I(i)("ENT_DIVISION_RECEIVER_GROUP_LIST")?(p(),K("div",Q,[o(k,{layout:"inline",class:"table-head-ground"},{default:t(()=>[C("div",W,[o(b,{placeholder:"\u7EC4ID",value:n.searchData.receiverGroupId,"onUpdate:value":a[0]||(a[0]=m=>n.searchData.receiverGroupId=m)},null,8,["value"]),o(b,{placeholder:"\u7EC4\u540D\u79F0",value:n.searchData.receiverGroupName,"onUpdate:value":a[1]||(a[1]=m=>n.searchData.receiverGroupName=m)},null,8,["value"]),C("span",X,[o(d,{type:"primary",onClick:g,loading:n.btnLoading},{default:t(()=>a[4]||(a[4]=[f(" \u67E5\u8BE2 ")])),_:1},8,["loading"]),o(d,{style:{"margin-left":"8px"},onClick:a[2]||(a[2]=()=>n.searchData={})},{default:t(()=>a[5]||(a[5]=[f(" \u91CD\u7F6E ")])),_:1})])])]),_:1})])):_("",!0),o(V,{ref_key:"infoTable",ref:e,initData:!0,reqTableDataFunc:A,tableColumns:n.tableColumns,searchData:n.searchData,onBtnLoadClose:a[3]||(a[3]=m=>n.btnLoading=!1),rowKey:"receiverGroupId"},{opRow:t(()=>[I(i)("ENT_DIVISION_RECEIVER_GROUP_ADD")?(p(),E(d,{key:0,type:"primary",onClick:s,class:"mg-b-30"},{default:t(()=>a[6]||(a[6]=[f(" \u65B0\u5EFA ")])),_:1})):_("",!0)]),bodyCell:t(({column:m,record:N})=>[m.key==="op"?(p(),E(c,{key:0},{default:t(()=>[I(i)("ENT_DIVISION_RECEIVER_GROUP_EDIT")?(p(),E(d,{key:0,type:"link",onClick:U=>u(N.receiverGroupId)},{default:t(()=>a[7]||(a[7]=[f(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])):_("",!0),I(i)("ENT_DIVISION_RECEIVER_GROUP_DELETE")?(p(),E(d,{key:1,danger:"",type:"link",onClick:U=>B(N.receiverGroupId)},{default:t(()=>a[8]||(a[8]=[f(" \u5220\u9664 ")])),_:2},1032,["onClick"])):_("",!0)]),_:2},1024)):_("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),o(J,{ref_key:"infoAddOrEdit",ref:r,callbackFunc:g},null,512)]),_:1})}}});export{ee as default};
