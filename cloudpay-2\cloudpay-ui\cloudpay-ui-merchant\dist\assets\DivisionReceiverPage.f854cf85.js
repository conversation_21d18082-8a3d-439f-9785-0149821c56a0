import{r as U,I as ae,J as se,K as M,o as de}from"./manage.2dfb5a24.js";import{e as W,g as J,r as X,f as L,D as pe,ac as Y,o as i,C as k,w as a,b as t,a as g,t as O,c as b,K as Z,J as D,G as x,H as A,d as l,E as c,am as ve,ad as h,Q as ye,W as fe,B as ee,m as $,I as le,an as ce,$ as ue,V as oe,j as me,R as ke,k as _e,F as ne,x as be,M as Q,ab as Ce}from"./index.fba97cfa.js";import{C as Ie}from"./ChannelUserModal.1b02fdc8.js";import"./reconnecting-websocket.9353e695.js";const ge={style:{color:"red"}},De={style:{color:"salmon"}},Ee={style:{color:"green"}},Re={style:{color:"red"}},Fe={style:{color:"salmon"}},Te={style:{color:"green"}},Se={style:{color:"red"}},Be={class:"drawer-btn-center"};var xe=W({__name:"ReceiverAdd",props:{callbackFunc:{type:Function,default:()=>({})}},setup(V,{expose:q}){const{$infoBox:d,$access:j}=J().appContext.config.globalProperties,P=[{key:"reqBindState",title:"\u72B6\u6001",scopedSlots:{customRender:"reqBindStateSlot"}},{key:"receiverAlias",title:"\u8D26\u53F7\u522B\u540D",scopedSlots:{customRender:"receiverAliasSlot"}},{key:"accType",title:"\u8D26\u53F7\u7C7B\u578B",scopedSlots:{customRender:"accTypeSlot"}},{key:"accNo",width:"300px",title:"\u63A5\u6536\u65B9\u8D26\u53F7",scopedSlots:{customRender:"accNoSlot"}},{key:"accName",width:"180px",title:"\u63A5\u6536\u65B9\u59D3\u540D",scopedSlots:{customRender:"accNameSlot"}},{key:"relationType",title:"\u5206\u8D26\u5173\u7CFB",scopedSlots:{customRender:"relationTypeSlot"}},{key:"relationTypeName",width:"200px",title:"\u5173\u7CFB\u540D\u79F0",scopedSlots:{customRender:"relationTypeNameSlot"}},{key:"divisionProfit",title:"\u9ED8\u8BA4\u5206\u8D26\u6BD4\u4F8B",scopedSlots:{customRender:"divisionProfitSlot"}},{key:"op",title:"\u64CD\u4F5C",scopedSlots:{customRender:"opSlot"}}],v={reqBindState:0,receiverAlias:"",receiverGroupId:"",appId:"",ifCode:"",accType:"0",accNo:null,accName:"",relationType:"PARTNER",relationTypeName:"\u5408\u4F5C\u4F19\u4F34",divisionProfit:""},F=V,o=X({visible:!1,appInfo:null,accTableColumns:P,allReceiverGroup:[],selectedReceiverGroupId:"",appSupportIfCodes:[],receiverTableData:[]}),G=L();function I(y){o.appSupportIfCodes=[],o.receiverTableData=[],U.list(ae,{pageSize:-1}).then(e=>{o.allReceiverGroup=e.records,o.allReceiverGroup&&o.allReceiverGroup.length>0&&(o.selectedReceiverGroupId=o.allReceiverGroup[0].receiverGroupId)}),se(y.appId).then(e=>{o.appSupportIfCodes=e}),o.appInfo=y,o.visible=!0}function s(){F.callbackFunc(),o.visible=!1}function T(y){const e=o.receiverTableData.indexOf(y);e>-1&&o.receiverTableData.splice(e,1)}function _(y,e){y.relationType=e.key,e.key!=="CUSTOM"?y.relationTypeName=e.label[0].children:y.relationTypeName=""}function r(y,e){G.value.showModal(o.appInfo.appId,y,e)}function S({channelUserId:y,extObject:e}){e.accNo=y}function N(y){if(!o.selectedReceiverGroupId)return d.message.error("\u8BF7\u9009\u9009\u62E9\u8981\u52A0\u5165\u7684\u5206\u7EC4");o.receiverTableData.push(Object.assign({},v,{rowKey:ve(),ifCode:y,appId:o.appInfo.appId}))}function B(y){if(o.receiverTableData.length<=0)return d.message.error("\u8BF7\u5148\u6DFB\u52A0\u8D26\u53F7");if(y>=o.receiverTableData.length)return d.message.success("\u5DF2\u5B8C\u6210\u6240\u6709\u8D26\u53F7\u7684\u7ED1\u5B9A\u64CD\u4F5C");const e=o.receiverTableData[y];if(e.receiverGroupId=o.selectedReceiverGroupId,e.reqBindState===1)return B(++y);if(!e.accNo)return d.message.error(`\u7B2C${y+1}\u6761\uFF1A \u63A5\u6536\u65B9\u8D26\u53F7\u4E0D\u80FD\u4E3A\u7A7A`);if(e.relationType==="CUSTOM"&&!e.relationTypeName)return d.message.error(`\u7B2C${y+1}\u6761\uFF1A \u81EA\u5B9A\u4E49\u7C7B\u578B\u65F6\u63A5\u6536\u65B9\u8D26\u53F7\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A`);if(!e.divisionProfit||e.divisionProfit<=0||e.divisionProfit>100)return d.message.error(`\u7B2C${y+1}\u6761\uFF1A \u9ED8\u8BA4\u5206\u8D26\u6BD4\u4F8B\u8BF7\u8BBE\u7F6E\u5728[0.01% ~ 100% ] \u4E4B\u95F4`);U.add(M,e).then(E=>{E.bindState===1?(B(++y),e.reqBindState=1):(e.reqBindState=2,d.modalError(`\u7B2C${y+1}\u6761\uFF1A \u7ED1\u5B9A\u5F02\u5E38`,t("div",null,[t("div",null,[l("\u9519\u8BEF\u7801\uFF1A"),E.errCode]),t("div",null,[l("\u9519\u8BEF\u4FE1\u606F\uFF1A"),E.errMsg])])))}).catch(()=>{e.reqBindState=2})}return q({show:I}),(y,e)=>{const E=pe,u=Y,w=h,K=ye,H=fe,R=ee,m=$("a-icon"),C=le,z=ce,te=ue,ie=oe;return o.visible?(i(),k(ie,{key:0,open:o.visible,"onUpdate:open":e[5]||(e[5]=p=>o.visible=p),onClose:s,closable:!0,maskClosable:!1,"body-style":{paddingBottom:"80px"},"drawer-style":{backgroundColor:"#f0f2f5"},width:"80%"},{default:a(()=>[t(K,{title:"\u7ED1\u5B9A\u5206\u8D26\u63A5\u6536\u8005\u8D26\u53F7"},{default:a(()=>[t(E,{label:"\u5F53\u524D\u5E94\u7528"},{default:a(()=>[g("span",ge,O(o.appInfo.appName)+" ["+O(o.appInfo.appId)+"]",1)]),_:1}),t(E,{label:"\u9009\u62E9\u8981\u52A0\u5165\u5230\u7684\u8D26\u53F7\u5206\u7EC4"},{default:a(()=>[t(w,{style:{width:"210px"},placeholder:"\u8D26\u53F7\u5206\u7EC4",value:o.selectedReceiverGroupId,"onUpdate:value":e[0]||(e[0]=p=>o.selectedReceiverGroupId=p)},{default:a(()=>[(i(!0),b(D,null,Z(o.allReceiverGroup,p=>(i(),k(u,{key:p.receiverGroupId,value:p.receiverGroupId},{default:a(()=>[l(O(p.receiverGroupName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(H),x(t(te,{title:"\u5FAE\u4FE1\u8D26\u53F7"},{extra:a(()=>[t(R,{style:{background:"green",color:"white"},onClick:e[1]||(e[1]=p=>N("wxpay"))},{default:a(()=>e[6]||(e[6]=[l(" \u6DFB\u52A0\u3010\u5FAE\u4FE1\u5B98\u65B9\u3011\u5206\u8D26\u63A5\u6536\u8D26\u53F7 ")])),_:1})]),default:a(()=>[t(z,{scroll:{x:"max-content"},columns:o.accTableColumns,"data-source":o.receiverTableData.filter(p=>p.ifCode=="wxpay"),pagination:!1,rowKey:"rowKey"},{bodyCell:a(({column:p,record:n,index:re})=>[p.key==="reqBindState"?(i(),b(D,{key:0},[x(g("div",De,[t(m,{type:"info-circle"}),e[7]||(e[7]=l(" \u5F85\u7ED1\u5B9A "))],512),[[A,n.reqBindState==0]]),x(g("div",Ee,[t(m,{type:"check-circle"}),e[8]||(e[8]=l(" \u7ED1\u5B9A\u6210\u529F "))],512),[[A,n.reqBindState==1]]),x(g("div",Re,[t(m,{type:"close-circle"}),e[9]||(e[9]=l(" \u7ED1\u5B9A\u5F02\u5E38 "))],512),[[A,n.reqBindState==2]])],64)):c("",!0),p.key==="receiverAlias"?(i(),k(C,{key:1,value:n.receiverAlias,"onUpdate:value":f=>n.receiverAlias=f,style:{width:"150px"},placeholder:"(\u9009\u586B)\u9ED8\u8BA4\u4E3A\u8D26\u53F7"},null,8,["value","onUpdate:value"])):c("",!0),p.key==="accType"?(i(),k(w,{key:2,style:{width:"110px"},value:n.accType,"onUpdate:value":f=>n.accType=f,placeholder:"\u8D26\u53F7\u7C7B\u578B","default-value":"0"},{default:a(()=>[t(u,{value:"0"},{default:a(()=>e[10]||(e[10]=[l("\u4E2A\u4EBA")])),_:1}),t(u,{value:"1"},{default:a(()=>e[11]||(e[11]=[l("\u5FAE\u4FE1\u5546\u6237")])),_:1})]),_:2},1032,["value","onUpdate:value"])):c("",!0),p.key==="accNo"?(i(),b(D,{key:3},[t(C,{value:n.accNo,"onUpdate:value":f=>n.accNo=f,style:{width:"150px"},placeholder:"\u8BF7\u8F93\u5165\u63A5\u6536\u65B9\u8D26\u53F7"},null,8,["value","onUpdate:value"]),n.accType==0?(i(),k(R,{key:0,type:"link",onClick:f=>r("wxpay",n)},{default:a(()=>e[12]||(e[12]=[l(" \u626B\u7801\u83B7\u53D6 ")])),_:2},1032,["onClick"])):c("",!0)],64)):c("",!0),p.key==="accName"?(i(),k(C,{key:4,value:n.accName,"onUpdate:value":f=>n.accName=f,placeholder:"\u8BF7\u8F93\u5165\u63A5\u6536\u65B9\u59D3\u540D"},null,8,["value","onUpdate:value"])):c("",!0),p.key==="relationType"?(i(),k(w,{key:5,style:{width:"110px"},labelInValue:"",placeholder:"\u5206\u8D26\u5173\u7CFB\u7C7B\u578B",defaultValue:{key:"PARTNER"},onChange:f=>_(n,f)},{default:a(()=>[t(u,{key:"PARTNER"},{default:a(()=>e[13]||(e[13]=[l("\u5408\u4F5C\u4F19\u4F34")])),_:1}),t(u,{key:"SERVICE_PROVIDER"},{default:a(()=>e[14]||(e[14]=[l("\u670D\u52A1\u5546")])),_:1}),t(u,{key:"STORE"},{default:a(()=>e[15]||(e[15]=[l("\u95E8\u5E97")])),_:1}),t(u,{key:"STAFF"},{default:a(()=>e[16]||(e[16]=[l("\u5458\u5DE5")])),_:1}),t(u,{key:"STORE_OWNER"},{default:a(()=>e[17]||(e[17]=[l("\u5E97\u4E3B")])),_:1}),t(u,{key:"HEADQUARTER"},{default:a(()=>e[18]||(e[18]=[l("\u603B\u90E8")])),_:1}),t(u,{key:"BRAND"},{default:a(()=>e[19]||(e[19]=[l("\u54C1\u724C\u65B9")])),_:1}),t(u,{key:"DISTRIBUTOR"},{default:a(()=>e[20]||(e[20]=[l("\u5206\u9500\u5546")])),_:1}),t(u,{key:"USER"},{default:a(()=>e[21]||(e[21]=[l("\u7528\u6237")])),_:1}),t(u,{key:"SUPPLIER"},{default:a(()=>e[22]||(e[22]=[l("\u4F9B\u5E94\u5546")])),_:1}),t(u,{key:"CUSTOM"},{default:a(()=>e[23]||(e[23]=[l("\u81EA\u5B9A\u4E49")])),_:1})]),_:2},1032,["onChange"])):c("",!0),p.key==="relationTypeName"?(i(),k(C,{key:6,disabled:n.relationType!=="CUSTOM",value:n.relationTypeName,"onUpdate:value":f=>n.relationTypeName=f},null,8,["disabled","value","onUpdate:value"])):c("",!0),p.key==="divisionProfit"?(i(),b(D,{key:7},[t(C,{value:n.divisionProfit,"onUpdate:value":f=>n.divisionProfit=f,style:{width:"65px"}},null,8,["value","onUpdate:value"]),e[24]||(e[24]=l(" % "))],64)):c("",!0),p.key==="op"?(i(),k(R,{key:8,type:"link",onClick:f=>T(n)},{default:a(()=>e[25]||(e[25]=[l("\u5220\u9664")])),_:2},1032,["onClick"])):c("",!0)]),_:1},8,["columns","data-source"])]),_:1},512),[[A,o.appSupportIfCodes.indexOf("wxpay")>=0]]),e[48]||(e[48]=g("br",null,null,-1)),x(t(te,{title:"\u652F\u4ED8\u5B9D\u8D26\u53F7"},{extra:a(()=>[t(R,{style:{background:"dodgerblue",color:"white"},onClick:e[2]||(e[2]=p=>N("alipay"))},{default:a(()=>e[26]||(e[26]=[l(" \u6DFB\u52A0\u3010\u652F\u4ED8\u5B9D\u5B98\u65B9\u3011\u5206\u8D26\u63A5\u6536\u8D26\u53F7 ")])),_:1})]),default:a(()=>[t(z,{scroll:{x:"max-content"},columns:o.accTableColumns,"data-source":o.receiverTableData.filter(p=>p.ifCode=="alipay"),pagination:!1,rowKey:"rowKey"},{bodyCell:a(({column:p,record:n,index:re})=>[p.key==="reqBindState"?(i(),b(D,{key:0},[x(g("div",Fe,[t(m,{type:"info-circle"}),e[27]||(e[27]=l(" \u5F85\u7ED1\u5B9A "))],512),[[A,n.reqBindState==0]]),x(g("div",Te,[t(m,{type:"check-circle"}),e[28]||(e[28]=l(" \u7ED1\u5B9A\u6210\u529F "))],512),[[A,n.reqBindState==1]]),x(g("div",Se,[t(m,{type:"close-circle"}),e[29]||(e[29]=l(" \u7ED1\u5B9A\u5F02\u5E38 "))],512),[[A,n.reqBindState==2]])],64)):c("",!0),p.key==="receiverAlias"?(i(),k(C,{key:1,value:n.receiverAlias,"onUpdate:value":f=>n.receiverAlias=f,style:{width:"150px"},placeholder:"(\u9009\u586B)\u9ED8\u8BA4\u4E3A\u8D26\u53F7"},null,8,["value","onUpdate:value"])):c("",!0),p.key==="accType"?(i(),k(w,{key:2,style:{width:"110px"},value:n.accType,"onUpdate:value":f=>n.accType=f,placeholder:"\u8D26\u53F7\u7C7B\u578B","default-value":"0"},{default:a(()=>[t(u,{value:"0"},{default:a(()=>e[30]||(e[30]=[l("\u4E2A\u4EBA")])),_:1}),t(u,{value:"1"},{default:a(()=>e[31]||(e[31]=[l("\u5546\u6237")])),_:1})]),_:2},1032,["value","onUpdate:value"])):c("",!0),p.key==="accNo"?(i(),b(D,{key:3},[t(C,{value:n.accNo,"onUpdate:value":f=>n.accNo=f,style:{width:"150px"},placeholder:"\u8BF7\u8F93\u5165\u63A5\u6536\u65B9\u8D26\u53F7"},null,8,["value","onUpdate:value"]),n.accType==0?(i(),k(R,{key:0,type:"link",onClick:f=>r("alipay",n)},{default:a(()=>e[32]||(e[32]=[l(" \u626B\u7801\u83B7\u53D6 ")])),_:2},1032,["onClick"])):c("",!0)],64)):c("",!0),p.key==="accName"?(i(),k(C,{key:4,value:n.accName,"onUpdate:value":f=>n.accName=f,placeholder:"\u8BF7\u8F93\u5165\u63A5\u6536\u65B9\u59D3\u540D"},null,8,["value","onUpdate:value"])):c("",!0),p.key==="relationType"?(i(),k(w,{key:5,style:{width:"110px"},labelInValue:"",placeholder:"\u5206\u8D26\u5173\u7CFB\u7C7B\u578B",defaultValue:{key:"PARTNER"},onChange:f=>_(n,f)},{default:a(()=>[t(u,{key:"PARTNER"},{default:a(()=>e[33]||(e[33]=[l("\u5408\u4F5C\u4F19\u4F34")])),_:1}),t(u,{key:"SERVICE_PROVIDER"},{default:a(()=>e[34]||(e[34]=[l("\u670D\u52A1\u5546")])),_:1}),t(u,{key:"STORE"},{default:a(()=>e[35]||(e[35]=[l("\u95E8\u5E97")])),_:1}),t(u,{key:"STAFF"},{default:a(()=>e[36]||(e[36]=[l("\u5458\u5DE5")])),_:1}),t(u,{key:"STORE_OWNER"},{default:a(()=>e[37]||(e[37]=[l("\u5E97\u4E3B")])),_:1}),t(u,{key:"HEADQUARTER"},{default:a(()=>e[38]||(e[38]=[l("\u603B\u90E8")])),_:1}),t(u,{key:"BRAND"},{default:a(()=>e[39]||(e[39]=[l("\u54C1\u724C\u65B9")])),_:1}),t(u,{key:"DISTRIBUTOR"},{default:a(()=>e[40]||(e[40]=[l("\u5206\u9500\u5546")])),_:1}),t(u,{key:"USER"},{default:a(()=>e[41]||(e[41]=[l("\u7528\u6237")])),_:1}),t(u,{key:"SUPPLIER"},{default:a(()=>e[42]||(e[42]=[l("\u4F9B\u5E94\u5546")])),_:1}),t(u,{key:"CUSTOM"},{default:a(()=>e[43]||(e[43]=[l("\u81EA\u5B9A\u4E49")])),_:1})]),_:2},1032,["onChange"])):c("",!0),p.key==="relationTypeName"?(i(),k(C,{key:6,disabled:n.relationType!=="CUSTOM",value:n.relationTypeName,"onUpdate:value":f=>n.relationTypeName=f},null,8,["disabled","value","onUpdate:value"])):c("",!0),p.key==="divisionProfit"?(i(),b(D,{key:7},[t(C,{value:n.divisionProfit,"onUpdate:value":f=>n.divisionProfit=f,style:{width:"65px"}},null,8,["value","onUpdate:value"]),e[44]||(e[44]=l(" % "))],64)):c("",!0),p.key==="op"?(i(),k(R,{key:8,type:"link",onClick:f=>T(n)},{default:a(()=>e[45]||(e[45]=[l("\u5220\u9664")])),_:2},1032,["onClick"])):c("",!0)]),_:1},8,["columns","data-source"])]),_:1},512),[[A,o.appSupportIfCodes.indexOf("alipay")>=0]]),g("div",Be,[t(R,{type:"primary",style:{marginRight:"8px"},onClick:e[3]||(e[3]=p=>B(0))},{default:a(()=>e[46]||(e[46]=[l(" \u53D1\u8D77\u7ED1\u5B9A\u8BF7\u6C42 ")])),_:1}),t(R,{onClick:s},{default:a(()=>e[47]||(e[47]=[l("\u5173\u95ED")])),_:1})]),t(Ie,{ref_key:"channelUserModal",ref:G,onChangeChannelUserId:e[4]||(e[4]=p=>S(p))},null,512)]),_:1},8,["open"])):c("",!0)}}});const Ae={class:"drawer-btn-center"},Ne=W({__name:"ReceiverEdit",props:{callbackFunc:{type:Function,default:()=>{}}},setup(V,{expose:q}){const{$infoBox:d,$access:j}=J().appContext.config.globalProperties,P=V,v=X({confirmLoading:!1,isShow:!1,saveObject:{},recordId:null,allReceiverGroup:[],rules:{receiverAlias:[{required:!0,message:"\u8BF7\u8F93\u5165\u522B\u540D",trigger:"blur"}],receiverGroupId:[{required:!0,message:"\u8BF7\u9009\u62E9\u5206\u7EC4",trigger:"blur"}],divisionProfit:[{required:!0,message:"\u8BF7\u5F55\u5165\u9ED8\u8BA4\u5206\u8D26\u6BD4\u4F8B",trigger:"blur"}],state:[{required:!0,message:"\u8BF7\u9009\u62E9\u72B6\u6001",trigger:"blur"}]}}),F=L();function o(I){v.saveObject={},v.confirmLoading=!1,F.value&&F.value.resetFields(),v.recordId=I,U.getById(M,I).then(s=>{s.divisionProfit=(s.divisionProfit*100).toFixed(2),v.saveObject=s}),U.list(ae,{pageSize:-1}).then(s=>{v.allReceiverGroup=s.records}),v.isShow=!0}function G(){F.value.validate().then(I=>{if(I){v.confirmLoading=!0;var s={receiverAlias:v.saveObject.receiverAlias,receiverGroupId:v.saveObject.receiverGroupId,divisionProfit:v.saveObject.divisionProfit,state:v.saveObject.state};U.updateById(M,v.recordId,s).then(T=>{d.message.success("\u4FEE\u6539\u6210\u529F"),v.isShow=!1,P.callbackFunc()}).catch(T=>{v.confirmLoading=!1})}})}return q({show:o}),(I,s)=>{const T=le,_=me,r=ke,S=_e,N=Y,B=h,y=ne,e=ee,E=oe;return i(),k(E,{open:v.isShow,"onUpdate:open":s[5]||(s[5]=u=>v.isShow=u),title:"\u4FEE\u6539\u5206\u8D26\u7528\u6237\u4FE1\u606F",width:"30%",maskClosable:!1,onClose:s[6]||(s[6]=u=>v.isShow=!1)},{default:a(()=>[t(y,{ref_key:"infoFormModel",ref:F,model:v.saveObject,"label-col":{span:6},"wrapper-col":{span:15},rules:v.rules},{default:a(()=>[t(_,{label:"\u8D26\u53F7\u522B\u540D\uFF1A",name:"receiverAlias"},{default:a(()=>[t(T,{value:v.saveObject.receiverAlias,"onUpdate:value":s[0]||(s[0]=u=>v.saveObject.receiverAlias=u)},null,8,["value"])]),_:1}),t(_,{label:"\u9ED8\u8BA4\u5206\u8D26\u6BD4\u4F8B\uFF1A",name:"divisionProfit"},{default:a(()=>[t(T,{value:v.saveObject.divisionProfit,"onUpdate:value":s[1]||(s[1]=u=>v.saveObject.divisionProfit=u),style:{width:"100px"}},null,8,["value"]),s[7]||(s[7]=l(" % "))]),_:1}),t(_,{label:"\u72B6\u6001",name:"state"},{default:a(()=>[t(S,{value:v.saveObject.state,"onUpdate:value":s[2]||(s[2]=u=>v.saveObject.state=u)},{default:a(()=>[t(r,{value:1},{default:a(()=>s[8]||(s[8]=[l("\u6B63\u5E38\u5206\u8D26")])),_:1}),t(r,{value:0},{default:a(()=>s[9]||(s[9]=[l("\u6682\u505C\u5206\u8D26")])),_:1})]),_:1},8,["value"])]),_:1}),t(_,{label:"\u5206\u7EC4\u53D8\u66F4\uFF1A",name:"receiverGroupId"},{default:a(()=>[t(B,{style:{width:"210px"},placeholder:"\u8D26\u53F7\u5206\u7EC4",value:v.saveObject.receiverGroupId,"onUpdate:value":s[3]||(s[3]=u=>v.saveObject.receiverGroupId=u)},{default:a(()=>[(i(!0),b(D,null,Z(v.allReceiverGroup,u=>(i(),k(N,{key:u.receiverGroupId,value:u.receiverGroupId},{default:a(()=>[l(O(u.receiverGroupName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},8,["model","rules"]),g("div",Ae,[t(e,{style:{marginRight:"8px"},onClick:s[4]||(s[4]=u=>v.isShow=!1)},{default:a(()=>s[10]||(s[10]=[l("\u53D6\u6D88")])),_:1}),t(e,{type:"primary",onClick:G,loading:v.confirmLoading},{default:a(()=>s[11]||(s[11]=[l("\u4FDD\u5B58")])),_:1},8,["loading"])])]),_:1},8,["open"])}}}),we={key:0,class:"table-page-search-wrapper"},Ue={class:"table-layer"},Oe={class:"table-page-search-submitButtons table-head-layout"},Pe={key:0,style:{color:"green"}},Ge={key:1,style:{color:"dodgerblue"}},$e={key:0},Le={key:1},Ve={key:2},He=W({__name:"DivisionReceiverPage",setup(V){const d=X({tableColumns:[{key:"receiverId",dataIndex:"receiverId",title:"\u7ED1\u5B9AID"},{key:"ifCode",title:"\u6E20\u9053\u7C7B\u578B",scopedSlots:{customRender:"ifCodeSlot"}},{key:"receiverAlias",dataIndex:"receiverAlias",title:"\u8D26\u53F7\u522B\u540D"},{key:"receiverGroupName",dataIndex:"receiverGroupName",title:"\u7EC4\u540D\u79F0"},{key:"accNo",dataIndex:"accNo",title:"\u5206\u8D26\u63A5\u6536\u8D26\u53F7"},{key:"accName",dataIndex:"accName",title:"\u5206\u8D26\u63A5\u6536\u8D26\u53F7\u540D\u79F0"},{key:"relationTypeName",dataIndex:"relationTypeName",title:"\u5206\u8D26\u5173\u7CFB\u7C7B\u578B"},{title:"\u72B6\u6001",key:"state",scopedSlots:{customRender:"stateSlot"},align:"center"},{key:"bindSuccessTime",dataIndex:"bindSuccessTime",title:"\u7ED1\u5B9A\u6210\u529F\u65F6\u95F4"},{key:"divisionProfit",dataIndex:"divisionProfit",title:"\u9ED8\u8BA4\u5206\u8D26\u6BD4\u4F8B",customRender:({text:_})=>(_*100).toFixed(2)+"%"},{key:"op",title:"\u64CD\u4F5C",width:"100px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],searchData:{appId:""},btnLoading:!1,mchAppList:[]}),j=L(),P=L(),v=L(),{$infoBox:F,$access:o}=J().appContext.config.globalProperties;be(()=>{U.list(de,{pageSize:-1}).then(_=>{d.mchAppList=_.records,d.mchAppList&&d.mchAppList.length>0&&(d.searchData.appId=d.mchAppList[0].appId+"",I())})});function G(_){return U.list(M,_)}function I(){d.btnLoading=!0,j.value.refTable(!0)}function s(){if(d.mchAppList.length<=0)return F.message.error("\u5F53\u524D\u5546\u6237\u65E0\u4EFB\u4F55\u5E94\u7528\uFF0C\u8BF7\u5148\u521B\u5EFA\u5E94\u7528\u540E\u518D\u8BD5\u3002");if(!d.searchData.appId)return F.message.error("\u8BF7\u5148\u9009\u62E9\u5E94\u7528\u3002");P.value.show(d.mchAppList.filter(_=>_.appId===d.searchData.appId)[0])}function T(_){v.value.show(_)}return(_,r)=>{const S=Y,N=h,B=$("cloudpay-text-up"),y=ee,e=ne,E=$("a-icon"),u=Ce,w=$("cloudpayTableColumns"),K=$("cloudpayTable"),H=ue,R=$("page-header-wrapper");return i(),k(R,null,{default:a(()=>[t(H,null,{default:a(()=>[Q(o)("ENT_DIVISION_RECEIVER_LIST")?(i(),b("div",we,[t(e,{layout:"inline",class:"table-head-ground"},{default:a(()=>[g("div",Ue,[t(N,{value:d.searchData.appId,"onUpdate:value":r[0]||(r[0]=m=>d.searchData.appId=m),placeholder:"\u9009\u62E9\u5E94\u7528",class:"table-head-layout"},{default:a(()=>[t(S,{key:""},{default:a(()=>r[7]||(r[7]=[l("\u5168\u90E8\u5E94\u7528")])),_:1}),(i(!0),b(D,null,Z(d.mchAppList,m=>(i(),k(S,{key:m.appId},{default:a(()=>[l(O(m.appName)+" ["+O(m.appId)+"] ",1)]),_:2},1024))),128))]),_:1},8,["value"]),t(B,{placeholder:"\u5206\u8D26\u63A5\u6536\u8005ID[\u7CBE\u51C6]",value:d.searchData.receiverId,"onUpdate:value":r[1]||(r[1]=m=>d.searchData.receiverId=m)},null,8,["value"]),t(B,{placeholder:"\u63A5\u6536\u8005\u8D26\u53F7\u522B\u540D[\u6A21\u7CCA]",value:d.searchData.receiverAlias,"onUpdate:value":r[2]||(r[2]=m=>d.searchData.receiverAlias=m)},null,8,["value"]),t(B,{placeholder:"\u7EC4ID[\u7CBE\u51C6]",value:d.searchData.receiverGroupId,"onUpdate:value":r[3]||(r[3]=m=>d.searchData.receiverGroupId=m)},null,8,["value"]),t(N,{class:"table-head-layout",value:d.searchData.state,"onUpdate:value":r[4]||(r[4]=m=>d.searchData.state=m),placeholder:"\u8D26\u53F7\u72B6\u6001\uFF08\u672C\u7CFB\u7EDF\uFF09","default-value":""},{default:a(()=>[t(S,{value:""},{default:a(()=>r[8]||(r[8]=[l("\u5168\u90E8")])),_:1}),t(S,{value:"1"},{default:a(()=>r[9]||(r[9]=[l("\u6B63\u5E38\u5206\u8D26")])),_:1}),t(S,{value:"0"},{default:a(()=>r[10]||(r[10]=[l("\u6682\u505C\u5206\u8D26")])),_:1})]),_:1},8,["value"]),g("span",Oe,[t(y,{type:"primary",onClick:I,loading:d.btnLoading},{default:a(()=>r[11]||(r[11]=[l(" \u67E5\u8BE2 ")])),_:1},8,["loading"]),t(y,{style:{"margin-left":"8px"},onClick:r[5]||(r[5]=()=>d.searchData={})},{default:a(()=>r[12]||(r[12]=[l(" \u91CD\u7F6E ")])),_:1})])])]),_:1})])):c("",!0),t(K,{ref_key:"infoTable",ref:j,initData:!1,reqTableDataFunc:G,tableColumns:d.tableColumns,searchData:d.searchData,onBtnLoadClose:r[6]||(r[6]=m=>d.btnLoading=!1),rowKey:"receiverId"},{opRow:a(()=>[Q(o)("ENT_DIVISION_RECEIVER_ADD")?(i(),k(y,{key:0,type:"primary",onClick:s},{default:a(()=>r[13]||(r[13]=[l(" \u65B0\u5EFA ")])),_:1})):c("",!0)]),bodyCell:a(({column:m,record:C})=>[m.key==="ifCode"?(i(),b(D,{key:0},[C.ifCode==="wxpay"?(i(),b("span",Pe,[t(E,{type:"wechat"}),r[14]||(r[14]=l(" \u5FAE\u4FE1 "))])):C.ifCode=="alipay"?(i(),b("span",Ge,[t(E,{type:"alipay-circle"}),r[15]||(r[15]=l(" \u652F\u4ED8\u5B9D "))])):(i(),b(D,{key:2},[l(O(C.ifCode),1)],64))],64)):c("",!0),m.key==="state"?(i(),b(D,{key:1},[C.state==0?(i(),b("div",$e,[t(u,{status:"error",text:"\u6682\u505C\u5206\u8D26"})])):C.state==1?(i(),b("div",Le,[t(u,{status:"processing",text:"\u6B63\u5E38\u5206\u8D26"})])):(i(),b("div",Ve,[t(u,{status:"warning",text:"\u672A\u77E5"})]))],64)):c("",!0),m.key==="op"?(i(),k(w,{key:2},{default:a(()=>[Q(o)("ENT_DIVISION_RECEIVER_EDIT")?(i(),k(y,{key:0,type:"link",onClick:z=>T(C.receiverId)},{default:a(()=>r[16]||(r[16]=[l(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])):c("",!0)]),_:2},1024)):c("",!0)]),_:1},8,["tableColumns","searchData"]),t(xe,{ref_key:"receiverAdd",ref:P,callbackFunc:I},null,512),t(Ne,{ref_key:"receiverEdit",ref:v,callbackFunc:I},null,512)]),_:1})]),_:1})}}});export{He as default};
