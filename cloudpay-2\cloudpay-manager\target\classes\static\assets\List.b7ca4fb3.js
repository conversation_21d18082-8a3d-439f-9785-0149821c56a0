import{u as J,r as v,q as E,p as K}from"./manage.6e729324.js";import{d as S,g as $,r as q,h as N,aE as b,w as t,i as f,o as m,b as a,j as i,aF as P,a as r,t as R,I as Q,m as X,c8 as Z,bA as ee,B as ue,F as ae,_ as te,c9 as k,c as oe,ca as se,aI as ne}from"./index.8746381c.js";import{_ as le}from"./index.9b74c380.js";import{_ as de,a as ie}from"./index.7c25015e.js";import{R as re,_ as ce}from"./Group.170fc6be.js";const pe=["src"],_e={class:"drawer-btn-center"},ve=S({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>()=>({})}},setup(T,{expose:y}){const{$infoBox:C,$access:c}=$().appContext.config.globalProperties,F=T,A=(s,u,l)=>e.saveObject.isMchMode===1&&e.saveObject.configPageType===1&&!u?Promise.reject("\u8BF7\u8F93\u5165\u666E\u901A\u5546\u6237\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0"):Promise.resolve(),x=(s,u,l)=>e.saveObject.isIsvMode===1&&e.saveObject.configPageType===1&&!u?Promise.reject("\u8BF7\u8F93\u5165\u670D\u52A1\u5546\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0"):Promise.resolve(),j=(s,u,l)=>e.saveObject.isIsvMode===1&&e.saveObject.configPageType===1&&!u?Promise.reject("\u8BF7\u8F93\u5165\u7279\u7EA6\u5546\u6237\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0"):Promise.resolve(),B=(s,u,l)=>e.checkedList.length<=0?Promise.reject("\u8BF7\u9009\u62E9\u652F\u4ED8\u65B9\u5F0F"):Promise.resolve(),e=q({isAdd:!0,open:!1,action:J.ifBG,ifCode:"",saveObject:{},rules:{ifCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u63A5\u53E3\u4EE3\u7801",trigger:"blur"}],ifName:[{required:!0,message:"\u8BF7\u8F93\u5165\u63A5\u53E3\u540D\u79F0",trigger:"blur"}],normalMchParams:[{validator:A,trigger:"blur"}],isvParams:[{validator:x,trigger:"blur"}],isvsubMchParams:[{validator:j,trigger:"blur"}],checkedList:[{validator:B,trigger:"blur"}]},wayCodesOptions:[],checkedList:[]}),p=N();function g(s){e.isAdd=!s,e.saveObject={isMchMode:1,isIsvMode:1,state:1,configPageType:1},p.value&&p.value.resetFields(),e.isAdd?(e.checkedList=[],e.open=!0):(e.ifCode=s,v.getById(E,s).then(u=>{e.saveObject=u;const l=[];u.wayCodes.forEach(n=>{l.push(n.wayCode)}),e.checkedList=l}),e.open=!0)}function O(){e.open=!1}function M(){p.value.validate().then(s=>{s&&(e.saveObject.wayCodeStrs=e.checkedList.join(","),e.isAdd?v.add(E,e.saveObject).then(u=>{C.message.success("\u65B0\u589E\u6210\u529F"),e.open=!1,F.callbackFunc()}):v.updateById(E,e.ifCode,e.saveObject).then(u=>{C.message.success("\u4FEE\u6539\u6210\u529F"),e.open=!1,F.callbackFunc()}))})}function h(){v.list(K,{pageSize:"-1"}).then(s=>{s.records.forEach(u=>{e.wayCodesOptions.push({label:u.wayName,value:u.wayCode})})})}h();function I(s,u){e.saveObject.icon=s}function w(s){e.checkedList=s}return y({show:g}),(s,u)=>{const l=Q,n=X,d=de,_=re,D=ce,L=Z,V=ee,W=f("a-icon"),U=ue,z=f("cloudpayUpload"),Y=ie,G=ae,H=le;return m(),b(H,{open:e.open,"onUpdate:open":u[13]||(u[13]=o=>e.open=o),title:e.isAdd?"\u65B0\u589E\u652F\u4ED8\u63A5\u53E3":"\u4FEE\u6539\u652F\u4ED8\u63A5\u53E3",width:"40%",maskClosable:!1,onClose:O},{default:t(()=>[a(G,{ref_key:"infoFormModel",ref:p,model:e.saveObject,layout:"vertical",rules:e.rules},{default:t(()=>[a(Y,{gutter:16,style:{"padding-bottom":"50px"}},{default:t(()=>[a(d,{span:12},{default:t(()=>[a(n,{label:"\u63A5\u53E3\u4EE3\u7801",name:"ifCode"},{default:t(()=>[a(l,{value:e.saveObject.ifCode,"onUpdate:value":u[0]||(u[0]=o=>e.saveObject.ifCode=o),placeholder:"\u8BF7\u8F93\u5165",disabled:!e.isAdd},null,8,["value","disabled"])]),_:1})]),_:1}),a(d,{span:12},{default:t(()=>[a(n,{label:"\u63A5\u53E3\u540D\u79F0",name:"ifName"},{default:t(()=>[a(l,{value:e.saveObject.ifName,"onUpdate:value":u[1]||(u[1]=o=>e.saveObject.ifName=o),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:t(()=>[a(n,{label:"\u662F\u5426\u652F\u6301\u666E\u901A\u5546\u6237\u6A21\u5F0F",name:"isMchMode"},{default:t(()=>[a(D,{value:e.saveObject.isMchMode,"onUpdate:value":u[2]||(u[2]=o=>e.saveObject.isMchMode=o)},{default:t(()=>[a(_,{value:1},{default:t(()=>u[14]||(u[14]=[i("\u652F\u6301")])),_:1,__:[14]}),a(_,{value:0},{default:t(()=>u[15]||(u[15]=[i("\u4E0D\u652F\u6301")])),_:1,__:[15]})]),_:1},8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:t(()=>[a(n,{label:"\u662F\u5426\u652F\u6301\u670D\u52A1\u5546\u5B50\u5546\u6237\u6A21\u5F0F",name:"isIsvMode"},{default:t(()=>[a(D,{value:e.saveObject.isIsvMode,"onUpdate:value":u[3]||(u[3]=o=>e.saveObject.isIsvMode=o)},{default:t(()=>[a(_,{value:1},{default:t(()=>u[16]||(u[16]=[i("\u652F\u6301")])),_:1,__:[16]}),a(_,{value:0},{default:t(()=>u[17]||(u[17]=[i("\u4E0D\u652F\u6301")])),_:1,__:[17]})]),_:1},8,["value"])]),_:1})]),_:1}),a(d,{span:24},{default:t(()=>[a(n,{label:"\u652F\u4ED8\u53C2\u6570\u914D\u7F6E\u9875\u9762\u7C7B\u578B",name:"configPageType"},{default:t(()=>[a(D,{value:e.saveObject.configPageType,"onUpdate:value":u[4]||(u[4]=o=>e.saveObject.configPageType=o)},{default:t(()=>[a(_,{value:1},{default:t(()=>u[18]||(u[18]=[i("\u6839\u636E\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0\u6E32\u67D3\u9875\u9762")])),_:1,__:[18]}),a(_,{value:2},{default:t(()=>u[19]||(u[19]=[i("\u81EA\u5B9A\u4E49\u9875\u9762")])),_:1,__:[19]})]),_:1},8,["value"])]),_:1})]),_:1}),e.saveObject.isIsvMode==1&&e.saveObject.configPageType===1?(m(),b(d,{key:0,span:24},{default:t(()=>[a(n,{label:"\u670D\u52A1\u5546\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0",name:"isvParams"},{default:t(()=>[a(L,{value:e.saveObject.isvParams,"onUpdate:value":u[5]||(u[5]=o=>e.saveObject.isvParams=o),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})):P("",!0),e.saveObject.isIsvMode==1&&e.saveObject.configPageType===1?(m(),b(d,{key:1,span:24},{default:t(()=>[a(n,{label:"\u7279\u7EA6\u5546\u6237\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0",name:"isvsubMchParams"},{default:t(()=>[a(L,{value:e.saveObject.isvsubMchParams,"onUpdate:value":u[6]||(u[6]=o=>e.saveObject.isvsubMchParams=o),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})):P("",!0),e.saveObject.isMchMode==1&&e.saveObject.configPageType===1?(m(),b(d,{key:2,span:24},{default:t(()=>[a(n,{label:"\u666E\u901A\u5546\u6237\u63A5\u53E3\u914D\u7F6E\u5B9A\u4E49\u63CF\u8FF0",name:"normalMchParams"},{default:t(()=>[a(L,{value:e.saveObject.normalMchParams,"onUpdate:value":u[7]||(u[7]=o=>e.saveObject.normalMchParams=o),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1})):P("",!0),a(d,{span:12},{default:t(()=>[a(n,{label:"\u72B6\u6001",name:"state"},{default:t(()=>[a(D,{value:e.saveObject.state,"onUpdate:value":u[8]||(u[8]=o=>e.saveObject.state=o)},{default:t(()=>[a(_,{value:1},{default:t(()=>u[20]||(u[20]=[i("\u542F\u7528")])),_:1,__:[20]}),a(_,{value:0},{default:t(()=>u[21]||(u[21]=[i("\u505C\u7528")])),_:1,__:[21]})]),_:1},8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:t(()=>[a(n,{label:"\u5907\u6CE8",name:"remark"},{default:t(()=>[a(l,{value:e.saveObject.remark,"onUpdate:value":u[9]||(u[9]=o=>e.saveObject.remark=o),placeholder:"\u8BF7\u8F93\u5165"},null,8,["value"])]),_:1})]),_:1}),a(d,{span:24},{default:t(()=>[a(n,{label:"\u652F\u6301\u7684\u652F\u4ED8\u65B9\u5F0F",name:"checkedList"},{default:t(()=>[a(V,{value:e.checkedList,"onUpdate:value":u[10]||(u[10]=o=>e.checkedList=o),options:e.wayCodesOptions,onChange:w},null,8,["value","options"])]),_:1})]),_:1}),a(d,{span:12},{default:t(()=>[a(n,{label:"\u9875\u9762\u5C55\u793A\uFF1A\u5361\u7247icon",name:"icon"},{default:t(()=>[a(z,{action:e.action,accept:".jpg, .jpeg, .png",onUploadSuccess:u[11]||(u[11]=o=>I(o,""))},{uploadSlot:t(({loading:o})=>[r("img",{src:e.saveObject.icon,style:{width:"80px"}},null,8,pe),a(U,{style:{"margin-left":"5px"}},{default:t(()=>[a(W,{type:e.loading?"loading":"upload"},null,8,["type"]),i(" "+R(e.loading?"\u6B63\u5728\u4E0A\u4F20":"\u70B9\u51FB\u4E0A\u4F20"),1)]),_:2},1024)]),_:1},8,["action"])]),_:1})]),_:1}),a(d,{span:12},{default:t(()=>[a(n,{label:"\u9875\u9762\u5C55\u793A\uFF1A\u5361\u7247\u80CC\u666F\u8272",name:"bgColor"},{default:t(()=>[a(l,{value:e.saveObject.bgColor,"onUpdate:value":u[12]||(u[12]=o=>e.saveObject.bgColor=o),placeholder:"\u8BF7\u8F93\u5165",type:"color"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),r("div",_e,[a(U,{onClick:O,style:{marginRight:"8px"}},{default:t(()=>u[22]||(u[22]=[i("\u53D6\u6D88")])),_:1,__:[22]}),a(U,{type:"primary",onClick:M},{default:t(()=>u[23]||(u[23]=[i("\u4FDD\u5B58")])),_:1,__:[23]})])]),_:1},8,["open","title"])}}});const fe={style:{"background-color":"#f0f2f5",padding:"20px","border-radius":"10px"}},me=["src"],Fe={class:"title"},ge={class:"cloudpay-card-ops"},be=S({__name:"List",setup(T){const{$infoBox:y,$access:C}=$().appContext.config.globalProperties,c=q({cloudpayCard:{name:"\u652F\u4ED8\u63A5\u53E3",height:200,span:{xxl:8,xl:4,lg:4,md:3,sm:2,xs:1},addAuthority:C("ENT_PC_IF_DEFINE_ADD")}}),F=N(),A=N();function x(){return v.list(E)}function j(){F.value.refCardList()}function B(p){A.value.show(p)}function e(p){y.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>{v.delById(E,p).then(g=>{y.message.success("\u5220\u9664\u6210\u529F\uFF01"),j()})})}return(p,g)=>{const O=se,M=f("EditOutlined"),h=ne,I=f("DeleteOutlined"),w=f("cloudpayCard"),s=f("page-header-wrapper");return m(),b(s,null,{default:t(()=>[r("div",fe,[a(O,{message:"",type:"info",style:{width:"calc(100% - 24px)","margin-bottom":"20px"}},{description:t(()=>g[0]||(g[0]=[r("p",{style:{display:"flex","justify-content":"space-between",margin:"0 0 4px"}},[i(" \u8BA1\u5168\u79D1\u6280\u5DF2\u5F00\u653E\u652F\u4ED8\u63A5\u53E3\u8D2D\u4E70\u6E20\u9053\uFF0C\u5B98\u65B9\u56E2\u961F\u5F00\u53D1\u3001\u6E90\u7801\u63D0\u4F9B\u3001\u4E0B\u8F7D\u540E\u76F4\u63A5\u4F7F\u7528\u3002 "),r("a",{href:"https://docs.jeequan.com/docs/cloudpay/cloudpay-1ejdnsuhveb16",target:"_blank"}," \u63A5\u53E3\u4E0B\u8F7D\u3001\u5B89\u88C5\u8BF4\u660E\u3002 "),r("a",{href:"https://www.jeequan.com/ifstore/list.html",target:"_blank"},"\u524D\u5F80\u63A5\u53E3\u5E02\u573A >")],-1)])),_:1}),a(w,{ref_key:"infoCard",ref:F,reqCardListFunc:x,span:c.cloudpayCard.span,height:c.cloudpayCard.height,name:c.cloudpayCard.name,addAuthority:c.cloudpayCard.addAuthority,onAddcloudpayCard:B},{cardContentSlot:t(({record:u})=>[r("div",{style:k({height:c.cloudpayCard.height+"px"}),class:"cloudpay-card-content"},[r("div",{class:"cloudpay-card-content-header",style:k({backgroundColor:u.bgColor,height:c.cloudpayCard.height/2+"px"})},[u.icon?(m(),oe("img",{key:0,src:u.icon,style:k({height:c.cloudpayCard.height/5+"px"})},null,12,me)):P("",!0)],4),r("div",{class:"cloudpay-card-content-body",style:k({height:c.cloudpayCard.height/2-50+"px"})},[r("div",Fe,R(u.ifName),1)],4),r("div",ge,[a(h,{placement:"top",title:"\u7F16\u8F91"},{default:t(()=>[a(M,{key:"edit",type:"edit",onClick:l=>B(u.ifCode)},null,8,["onClick"])]),_:2},1024),a(h,{placement:"top",title:"\u5220\u9664"},{default:t(()=>[a(I,{key:"delete",type:"delete",onClick:l=>e(u.ifCode)},null,8,["onClick"])]),_:2},1024)])],4)]),_:1},8,["span","height","name","addAuthority"])]),a(ve,{ref_key:"payIfDefineAddOrEdit",ref:A,callbackFunc:j},null,512)]),_:1})}}});var Be=te(be,[["__scopeId","data-v-26f3f8e0"]]);export{Be as default};
