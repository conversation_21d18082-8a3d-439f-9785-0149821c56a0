import{e as S,g as N,r as $,o as g,c as F,b as s,w as l,a,d as p,G as y,C as W,H as m,t as L,B as R,a7 as h,a8 as te,_ as J,f as w,ae,a1 as H,I as Q,x as oe,ac as se,J as ne,K as ue,af as f,E as le,ag as pe,ad as ie,j as re,F as de,W as ye,R as me,k as ce,ah as _e,$ as fe}from"./index.fba97cfa.js";import{w as ge,r as Ae,o as ve,x as Ce,y as Fe}from"./manage.2dfb5a24.js";import{R as Ie}from"./reconnecting-websocket.9353e695.js";var Ee="/assets/wx_native.ea98e252.svg",Pe="/assets/wx_bar.071a559f.svg",xe="/assets/wx_jsapi.08b26a53.svg",Be="/assets/wx_h5.af807f31.svg",U="/assets/ali_qr.14f84e7c.svg",De="/assets/ali_bar.4b9213a9.svg",ke="/assets/ali_jsapi.8f67cbcc.svg",be="/assets/ali_pc.28980862.svg",We="/assets/ali_wap.22e1434b.svg",Le="/assets/qr_cashier.09abb0b2.svg",we="/assets/auto_bar.30a260da.svg",Te="/assets/pp_pc.84782fe4.svg",Oe="/assets/wx_app.cd02cce9.svg",Se="/assets/ali_app.8ed743e0.svg";const $e={style:{width:"100%","margin-bottom":"20px","text-align":"center"}},Re=["src"],Me={key:1},Ve=["href"],Xe={key:2},Ue={class:"describe"},Ne={src:Oe,alt:""},he={src:Se,alt:""};var Je=S({__name:"PayTestModal",emits:["closeBarCode"],setup(M,{expose:B,emit:I}){const{$infoBox:i,$access:t}=N().appContext.config.globalProperties,o=$({open:!1,payText:"",wxApp:!1,aliApp:!1,apiRes:{},payOrderWebSocket:null}),C=I;function E(){i.message.success("\u590D\u5236\u6210\u529F")}function D(_,n){if(o.payOrderWebSocket&&o.payOrderWebSocket.close(),o.apiRes=n,o.wxApp=!1,o.aliApp=!1,o.open=!0,o.payText="",_==="WX_NATIVE"||_==="WX_JSAPI"?(o.wxApp=!0,o.payText='\u8BF7\u4F7F\u7528\u5FAE\u4FE1"\u626B\u4E00\u626B"\u626B\u7801\u652F\u4ED8'):_==="ALI_QR"||_==="ALI_JSAPI"||_==="ALI_OC"?(o.aliApp=!0,o.payText='\u8BF7\u4F7F\u7528\u652F\u4ED8\u5B9D"\u626B\u4E00\u626B"\u626B\u7801\u652F\u4ED8'):_==="QR_CASHIER"&&(o.wxApp=!0,o.aliApp=!0,o.payText="\u652F\u6301\u5FAE\u4FE1\u3001\u652F\u4ED8\u5B9D\u626B\u7801"),n.orderState===2||n.orderState===3){if(n.orderState===2){r();const A=i.modalSuccess("\u652F\u4ED8\u6210\u529F",s("div",null,[p("2s\u540E\u81EA\u52A8\u5173\u95ED...")]));setTimeout(()=>{A.destroy()},2e3),C("closeBarCode")}else n.orderState===3&&(r(),C("closeBarCode"),i.modalError("\u652F\u4ED8\u5931\u8D25",s("div",null,[s("div",null,[p("\u9519\u8BEF\u7801\uFF1A"),n.errCode]),s("div",null,[p("\u9519\u8BEF\u4FE1\u606F\uFF1A"),n.errMsg])])));return}_==="WX_H5"||_==="ALI_WAP"?o.payText="\u8BF7\u590D\u5236\u94FE\u63A5\u5230\u624B\u673A\u7AEF\u6253\u5F00":n.payDataType==="payurl"&&window.open(n.payData),C("closeBarCode"),o.payOrderWebSocket=new Ie(ge()+"/api/anon/ws/payOrder/"+n.payOrderId+"/"+new Date().getTime()),o.payOrderWebSocket.onopen=()=>{},o.payOrderWebSocket.onmessage=A=>{if(JSON.parse(A.data).state===2){r();const d=i.modalSuccess("\u652F\u4ED8\u6210\u529F",s("div",null,[p("2s\u540E\u81EA\u52A8\u5173\u95ED...")]));setTimeout(()=>{d.destroy()},2e3)}else r(),i.modalError("\u652F\u4ED8\u5931\u8D25",s("div",null,[s("div",null,[p("\u9519\u8BEF\u7801\uFF1A"),n.errCode]),s("div",null,[p("\u9519\u8BEF\u4FE1\u606F\uFF1A"),n.errMsg])]))}}function r(){o.payOrderWebSocket&&o.payOrderWebSocket.close(),o.open=!1}return B({showModal:D}),(_,n)=>{const A=R,P=h,d=te("clipboard");return g(),F("div",null,[s(P,{open:o.open,"onUpdate:open":n[0]||(n[0]=x=>o.open=x),title:"\u7B49\u5F85\u652F\u4ED8",onOk:r,footer:null,width:300},{default:l(()=>[a("div",$e,[o.apiRes.payDataType=="codeImgUrl"?(g(),F("img",{key:0,src:o.apiRes.payData,alt:""},null,8,Re)):o.apiRes.payDataType=="payurl"?(g(),F("span",Me,[n[2]||(n[2]=p(" \u7B49\u5F85\u7528\u6237\u652F\u4ED8 ")),n[3]||(n[3]=a("hr",null,null,-1)),n[4]||(n[4]=p(" \u5982\u6D4F\u89C8\u5668\u672A\u6B63\u786E\u8DF3\u8F6C\u8BF7\u70B9\u51FB\uFF1A ")),a("a",{href:o.apiRes.payData,target:"_blank"},"\u652F\u4ED8\u5730\u5740",8,Ve),y((g(),W(A,{size:"small",class:"copy-btn"},{default:l(()=>n[1]||(n[1]=[p(" \u590D\u5236\u94FE\u63A5 ")])),_:1})),[[d,o.apiRes.payData,"copy"],[d,E,"success"]])])):(g(),F("span",Xe,"\u7B49\u5F85\u7528\u6237\u652F\u4ED8,\u8BF7\u7A0D\u540E"))]),a("p",Ue,[y(a("img",Ne,null,512),[[m,o.wxApp]]),y(a("img",he,null,512),[[m,o.aliApp]]),a("span",null,L(o.payText),1)])]),_:1},8,["open"])])}}});var He=J(Je,[["__scopeId","data-v-6e4192b9"]]),Qe="/assets/scan.0e31c69c.svg";const je={style:{display:"flex","flex-direction":"row","margin-bottom":"14px"}},ze=S({__name:"PayTestBarCode",emits:["barCodeValue","CodeAgainChange"],setup(M,{expose:B,emit:I}){const i=$({open:!1,barCodeValue:"",loading:!1}),t=I,o=w();function C(){i.loading=!1,i.barCodeValue="",i.open=!0,H(()=>{o.value.focus()})}function E(){i.barCodeValue!==""&&(i.loading=!0,t("barCodeValue",i.barCodeValue))}function D(){t("CodeAgainChange")}function r(){return i.open}function _(){return i.open}function n(){i.loading=!1}function A(){i.open=!1}return B({showModal:C,getVisible:r,processCatch:n,getopen:_,closeVisible:A}),(P,d)=>{const x=Q,T=R,O=h;return g(),F("div",null,[s(O,{open:i.open,"onUpdate:open":d[1]||(d[1]=k=>i.open=k),title:"\u6761\u7801\u652F\u4ED8",onCancel:D,footer:null,width:350},{default:l(()=>[a("div",null,[d[3]||(d[3]=a("p",null,"\u8BF7\u8F93\u5165\u7528\u6237\u6761\u5F62\u7801:",-1)),a("div",je,[s(x,{value:i.barCodeValue,"onUpdate:value":d[0]||(d[0]=k=>i.barCodeValue=k),ref_key:"barCodeInput",ref:o,onKeyup:ae(E,["enter"])},null,8,["value"]),s(T,{onClick:E,type:"primary",style:{"margin-left":"10px"},loading:i.loading},{default:l(()=>d[2]||(d[2]=[p(" \u786E\u8BA4\u652F\u4ED8 ")])),_:1},8,["loading"])]),d[4]||(d[4]=a("p",null,"\u6216\u8005\u4F7F\u7528(\u626B\u7801\u67AA/\u626B\u7801\u76D2)\u626B\u7801:",-1)),d[5]||(d[5]=a("div",{style:{"text-align":"center"}},[a("img",{src:Qe,alt:""})],-1))])]),_:1},8,["open"])])}}});const qe={style:{display:"flex","flex-direction":"row"}},Ke={key:3,style:{width:"100%"},class:"paydemo"},Ge={class:"paydemo-type-content"},Ye={class:"paydemo-type-name article-title"},Ze={class:"paydemo-type-body"},et={class:"paydemo-type-name article-title"},tt={class:"paydemo-type-body"},at={class:"paydemo-type-name article-title"},ot={class:"paydemo-type-body"},st={class:"paydemo-type-content"},nt={class:"layui-form"},ut={class:"paydemo-form-item"},lt={id:"payMchOrderNo"},pt={class:"paydemo-form-item"},it={class:"paydemo-form-item"},rt={style:{display:"flex"}},dt={class:"paydemo-form-item"},yt={style:{"margin-top":"20px","text-align":"left"}},mt=S({__name:"PayTest",setup(M){const B=pe(),{$infoBox:I,$access:i}=N().appContext.config.globalProperties,t=$({mchAppList:[],appId:"",appPaywayList:[],currentWayCode:"",currentPayDataType:"",mchOrderNo:"",authCode:"",paytestAmount:"0.01",amountInput:!1,noConfigText:!1,divisionMode:0,orderTitle:"\u63A5\u53E3\u8C03\u8BD5"}),o=w(),C=w(),E=w();oe(()=>{const c=B.params.appId;c&&(t.appId=c,P(c)),Ae.list(ve,{pageSize:-1}).then(e=>{t.mchAppList=e.records,t.mchAppList.length>0&&(t.appId=t.mchAppList[0].appId,P(t.appId))}),n()});function D(){return!(t.appId===""||t.appPaywayList.length===0)}function r(c,e){t.currentWayCode=c,t.currentPayDataType=e}function _(c){P(c)}function n(){t.mchOrderNo="M"+new Date().getTime()+Math.floor(Math.random()*(9999-1e3)+1e3)}function A(c){t.authCode=c,d()}function P(c){if(!c)return t.appPaywayList=[],!1;Ce(c).then(e=>{t.appPaywayList=e,e.length===0?t.noConfigText=!0:t.noConfigText=!1})}function d(){if(!t.paytestAmount||t.paytestAmount==0)return I.message.error("\u8BF7\u8F93\u5165\u652F\u4ED8\u91D1\u989D");if(t.currentWayCode==="")return I.message.error("\u8BF7\u9009\u62E9\u652F\u4ED8\u65B9\u5F0F");if(!t.orderTitle||t.orderTitle.length>20)return I.message.error("\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u8BA2\u5355\u6807\u9898[20\u5B57\u4EE5\u5185]");if(!o.value.getVisible()&&(t.currentWayCode==="WX_BAR"||t.currentWayCode==="ALI_BAR"||t.currentWayCode==="AUTO_BAR"))return o.value.showModal();Fe({wayCode:t.currentWayCode==="WX_JSAPI"||t.currentWayCode==="ALI_JSAPI"?"QR_CASHIER":t.currentWayCode,amount:parseFloat(t.paytestAmount),appId:t.appId,mchOrderNo:t.mchOrderNo,payDataType:t.currentPayDataType,authCode:t.authCode,divisionMode:t.divisionMode,orderTitle:t.orderTitle}).then(c=>{C.value.showModal(t.currentWayCode,c),n()}).catch(()=>{o.value.processCatch(),n()})}function x(c){return t.appPaywayList.toString().indexOf(c)!==-1}function T(){return t.appPaywayList.toString().indexOf("WX")!==-1||t.appPaywayList.toString().indexOf("ALI")!==-1||t.appPaywayList.toString().indexOf("PP_PC")!==-1}function O(){H(()=>{E.value.focus()}),t.amountInput=!0,t.paytestAmount=""}function k(){n()}function j(){o.value&&o.value.closeVisible(),o.value.open=!1}return(c,e)=>{const V=se,z=ie,q=re,K=de,b=ye,X=R,G=Q,v=me,Y=ce,Z=_e,ee=fe;return g(),F("div",null,[s(ee,{style:{"box-sizing":"border-box",padding:"30px"}},{default:l(()=>[s(K,null,{default:l(()=>[a("div",qe,[s(q,{label:"",class:"table-head-layout"},{default:l(()=>[s(z,{value:t.appId,"onUpdate:value":e[0]||(e[0]=u=>t.appId=u),onChange:_,style:{width:"300px"}},{default:l(()=>[s(V,{key:""},{default:l(()=>e[23]||(e[23]=[p("\u5E94\u7528APPID")])),_:1}),(g(!0),F(ne,null,ue(t.mchAppList,u=>(g(),W(V,{key:u.appId},{default:l(()=>[p(L(u.appName)+" ["+L(u.appId)+"] ",1)]),_:2},1024))),128))]),_:1},8,["value"])]),_:1})])]),_:1}),t.appId?t.noConfigText?(g(),W(b,{key:1},{default:l(()=>e[25]||(e[25]=[p("\u60A8\u5C1A\u672A\u914D\u7F6E\u4EFB\u4F55\u652F\u4ED8\u65B9\u5F0F")])),_:1})):(g(),W(b,{key:2})):(g(),W(b,{key:0},{default:l(()=>e[24]||(e[24]=[p("\u8BF7\u9009\u62E9\u5E94\u7528APPID")])),_:1})),D()?(g(),F("div",Ke,[a("div",Ge,[y(a("div",Ye,"\u5FAE\u4FE1\u652F\u4ED8",512),[[m,x("WX")]]),a("div",Ze,[y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="WX_NATIVE"}]),onClick:e[1]||(e[1]=u=>r("WX_NATIVE","codeImgUrl"))},e[26]||(e[26]=[a("img",{src:Ee,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u5FAE\u4FE1\u4E8C\u7EF4\u7801",-1)]),2),[[m,t.appPaywayList.indexOf("WX_NATIVE")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="WX_BAR"}]),onClick:e[2]||(e[2]=u=>r("WX_BAR",""))},e[27]||(e[27]=[a("img",{src:Pe,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u5FAE\u4FE1\u6761\u7801",-1)]),2),[[m,t.appPaywayList.indexOf("WX_BAR")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="WX_JSAPI"}]),onClick:e[3]||(e[3]=u=>r("WX_JSAPI","codeImgUrl"))},e[28]||(e[28]=[a("img",{src:xe,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u516C\u4F17\u53F7/\u5C0F\u7A0B\u5E8F",-1)]),2),[[m,t.appPaywayList.indexOf("WX_JSAPI")>=0]]),y(a("div",{class:f(["paydemo-type-h5",{this:t.currentWayCode==="WX_H5"}]),onClick:e[4]||(e[4]=u=>r("WX_H5","payurl"))},e[29]||(e[29]=[a("img",{src:Be,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u5FAE\u4FE1H5",-1)]),2),[[m,t.appPaywayList.indexOf("WX_H5")>=0]])]),y(a("div",et,"\u652F\u4ED8\u5B9D\u652F\u4ED8",512),[[m,x("ALI")]]),a("div",tt,[y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="ALI_QR"}]),onClick:e[5]||(e[5]=u=>r("ALI_QR","codeImgUrl"))},e[30]||(e[30]=[a("img",{src:U,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u652F\u4ED8\u5B9D\u4E8C\u7EF4\u7801",-1)]),2),[[m,t.appPaywayList.indexOf("ALI_QR")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="ALI_BAR"}]),onClick:e[6]||(e[6]=u=>r("ALI_BAR",""))},e[31]||(e[31]=[a("img",{src:De,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u652F\u4ED8\u5B9D\u6761\u7801",-1)]),2),[[m,t.appPaywayList.indexOf("ALI_BAR")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="ALI_JSAPI"}]),onClick:e[7]||(e[7]=u=>r("ALI_JSAPI","codeImgUrl"))},e[32]||(e[32]=[a("img",{src:ke,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u652F\u4ED8\u5B9D\u751F\u6D3B\u53F7",-1)]),2),[[m,t.appPaywayList.indexOf("ALI_JSAPI")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="ALI_PC"}]),onClick:e[8]||(e[8]=u=>r("ALI_PC","payurl"))},e[33]||(e[33]=[a("img",{src:be,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u652F\u4ED8\u5B9DPC\u7F51\u7AD9",-1)]),2),[[m,t.appPaywayList.indexOf("ALI_PC")>=0]]),a("div",null,[y(a("div",{class:f(["paydemo-type-h5",{this:t.currentWayCode==="ALI_WAP"}]),onClick:e[9]||(e[9]=u=>r("ALI_WAP","payurl"))},e[34]||(e[34]=[a("img",{src:We,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u652F\u4ED8\u5B9DWAP",-1)]),2),[[m,t.appPaywayList.indexOf("ALI_WAP")>=0]])]),a("div",null,[y(a("div",{class:f(["paydemo-type-h5",{this:t.currentWayCode==="ALI_OC"}]),onClick:e[10]||(e[10]=u=>r("ALI_OC","codeImgUrl"))},e[35]||(e[35]=[a("img",{src:U,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u652F\u4ED8\u5B9D\u8BA2\u5355\u7801",-1)]),2),[[m,t.appPaywayList.indexOf("ALI_OC")>=0]])])]),y(a("div",at,"\u5176\u5B83\u652F\u4ED8",512),[[m,T()]]),a("div",ot,[y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="QR_CASHIER"}]),onClick:e[11]||(e[11]=u=>r("QR_CASHIER","codeImgUrl"))},e[36]||(e[36]=[a("img",{src:Le,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u805A\u5408\u4E3B\u626B",-1)]),2),[[m,t.appPaywayList.indexOf("WX_JSAPI")>=0||t.appPaywayList.indexOf("ALI_JSAPI")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="AUTO_BAR"}]),onClick:e[12]||(e[12]=u=>r("AUTO_BAR","codeImgUrl"))},e[37]||(e[37]=[a("img",{src:we,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"\u805A\u5408\u88AB\u626B",-1)]),2),[[m,t.appPaywayList.indexOf("WX_BAR")>=0||t.appPaywayList.indexOf("ALI_BAR")>=0]]),y(a("div",{class:f(["paydemo-type color-change",{this:t.currentWayCode==="PP_PC"}]),onClick:e[13]||(e[13]=u=>r("PP_PC","payurl"))},e[38]||(e[38]=[a("img",{src:Te,class:"paydemo-type-img"},null,-1),a("span",{class:"color-change"},"PayPal\u652F\u4ED8",-1)]),2),[[m,t.appPaywayList.indexOf("PP_PC")>=0]])])]),s(b),a("div",st,[e[54]||(e[54]=a("div",{class:"paydemo-type-name article-title"},"\u652F\u4ED8\u4FE1\u606F",-1)),a("form",nt,[a("div",ut,[e[40]||(e[40]=a("label",null,"\u8BA2\u5355\u7F16\u53F7\uFF1A",-1)),a("span",lt,L(t.mchOrderNo),1),s(X,{onClick:n,type:"link"},{default:l(()=>e[39]||(e[39]=[p("\u5237\u65B0\u8BA2\u5355\u53F7")])),_:1})]),a("div",pt,[e[41]||(e[41]=a("label",null,"\u8BA2\u5355\u6807\u9898\uFF1A",-1)),s(G,{value:t.orderTitle,"onUpdate:value":e[14]||(e[14]=u=>t.orderTitle=u),style:{width:"200px"}},null,8,["value"])]),a("div",it,[e[45]||(e[45]=a("label",null,"\u5206\u8D26\u65B9\u5F0F\uFF1A",-1)),s(Y,{value:t.divisionMode,"onUpdate:value":e[15]||(e[15]=u=>t.divisionMode=u),style:{display:"flex"}},{default:l(()=>[a("div",rt,[s(v,{value:0},{default:l(()=>e[42]||(e[42]=[p("\u8BA2\u5355\u4E0D\u5206\u8D26")])),_:1}),s(v,{value:1},{default:l(()=>e[43]||(e[43]=[p("\u652F\u4ED8\u5B8C\u6210\u81EA\u52A8\u5206\u8D26")])),_:1}),s(v,{value:2},{default:l(()=>e[44]||(e[44]=[p("\u624B\u52A8\u5206\u8D26\uFF08\u51BB\u7ED3\u5546\u6237\u8D44\u91D1\uFF0C \u53EA\u80FD\u901A\u8FC7API\u53D1\u8D77\u5206\u8D26\u540E\u89E3\u51BB\uFF09")])),_:1})])]),_:1},8,["value"])]),s(b),a("div",dt,[e[52]||(e[52]=a("span",null,"\u652F\u4ED8\u91D1\u989D(\u5143)\uFF1A",-1)),a("div",{style:{display:"flex"},onClick:e[21]||(e[21]=u=>t.amountInput=!1)},[s(v,{checked:t.paytestAmount==="0.01",onClick:e[16]||(e[16]=u=>t.paytestAmount="0.01")},{default:l(()=>e[46]||(e[46]=[p(" \uFFE50.01 ")])),_:1},8,["checked"]),s(v,{checked:t.paytestAmount==="0.15",onClick:e[17]||(e[17]=u=>t.paytestAmount="0.15")},{default:l(()=>e[47]||(e[47]=[p(" \uFFE50.15 ")])),_:1},8,["checked"]),s(v,{checked:t.paytestAmount==="0.21",onClick:e[18]||(e[18]=u=>t.paytestAmount="0.21")},{default:l(()=>e[48]||(e[48]=[p(" \uFFE50.21 ")])),_:1},8,["checked"]),s(v,{checked:t.paytestAmount==="0.29",onClick:e[19]||(e[19]=u=>t.paytestAmount="0.29")},{default:l(()=>e[49]||(e[49]=[p(" \uFFE50.29 ")])),_:1},8,["checked"]),s(v,{checked:t.paytestAmount==="0.64",onClick:e[20]||(e[20]=u=>t.paytestAmount="0.64")},{default:l(()=>e[50]||(e[50]=[p(" \uFFE50.64 ")])),_:1},8,["checked"])]),s(v,{onClick:O,checked:t.amountInput},{default:l(()=>[e[51]||(e[51]=a("span",{style:{"margin-right":"3px"}},"\u81EA\u5B9A\u4E49\u91D1\u989D",-1)),y(s(Z,{ref_key:"amountInputFocus",ref:E,max:1e5,min:.01,value:t.paytestAmount,"onUpdate:value":e[22]||(e[22]=u=>t.paytestAmount=u),precision:2},null,8,["value"]),[[m,t.amountInput]])]),_:1},8,["checked"])]),a("div",yt,[s(X,{onClick:d,type:"primary"},{default:l(()=>e[53]||(e[53]=[p("\u7ACB\u5373\u652F\u4ED8")])),_:1})])])])])):le("",!0)]),_:1}),s(He,{ref_key:"payTestModal",ref:C,onCloseBarCode:j},null,512),s(ze,{ref_key:"payTestBarCodeRef",ref:o,onBarCodeValue:A,onCodeAgainChange:k},null,512)])}}});var gt=J(mt,[["__scopeId","data-v-7d33a1f8"]]);export{gt as default};
