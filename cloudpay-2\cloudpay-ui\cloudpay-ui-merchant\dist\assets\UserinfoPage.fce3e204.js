import{_ as S,e as q,g as M,u as T,r as h,f as j,o as N,c as V,b as u,w as t,a as s,d,t as R,h as $,i as O,I as H,j as G,R as J,k as K,F as Q,B as W,l as X,m as g,n as Y,p as Z,q as z,T as ee}from"./index.fba97cfa.js";import{u as ae,g as ue,a as te,b as ne}from"./manage.2dfb5a24.js";const oe={style:{background:"#fff",padding:"20px"}},le={class:"account-settings-info-view"},se={style:{display:"flex","justify-content":"center"}},re={class:"ant-upload-preview"},de=["src"],ie={class:"account-settings-info-view"},ce={style:{display:"flex","justify-content":"center"}},pe=q({__name:"UserinfoPage",setup(_e){const{$infoBox:i,$access:me}=M().appContext.config.globalProperties,p=T(),e=h({action:ae.avatar,btnLoading:!1,saveObject:{loginUsername:"",realname:"",sex:"",avatarUrl:""},updateObject:{originalPwd:"",newPwd:"",confirmPwd:""},recordId:p.userInfo.sysUserId,rules:{realname:[{required:!0,message:"\u8BF7\u8F93\u5165\u771F\u5B9E\u59D3\u540D",trigger:"blur"}]},rulesPass:{originalPwd:[{required:!0,message:"\u8BF7\u8F93\u5165\u539F\u5BC6\u7801",trigger:"blur"}],newPwd:[{min:6,max:12,required:!0,message:"\u8BF7\u8F93\u51656-12\u4F4D\u65B0\u5BC6\u7801",trigger:"blur"}],confirmPwd:[{required:!0,message:"\u8BF7\u8F93\u5165\u786E\u8BA4\u65B0\u5BC6\u7801",trigger:"blur"},{validator:(o,a)=>e.updateObject.newPwd===a?Promise.resolve():Promise.reject("\u65B0\u5BC6\u7801\u4E0E\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4")}]}}),v=j(),c=j();function P(o){e.option.img=o}function y(){ue().then(o=>{e.saveObject=o})}y();function U(){v.value.validate().then(o=>{o&&i.confirmPrimary("\u786E\u8BA4\u66F4\u65B0\u4FE1\u606F\u5417\uFF1F","",()=>{e.btnLoading=!0,te(e.saveObject).then(a=>(e.btnLoading=!0,$())).then(a=>{a.avatarUrl=e.saveObject.avatarUrl,a.realname=e.saveObject.realname,e.btnLoading=!1,p.refUserInfo(a),i.message.success("\u4FEE\u6539\u6210\u529F")}).catch(a=>{console.log(a),e.btnLoading=!1})})})}function C(o){c.value.validate().then(a=>{a&&i.confirmPrimary("\u786E\u8BA4\u66F4\u65B0\u5BC6\u7801\u5417\uFF1F","",()=>{e.btnLoading=!0,e.confirmLoading=!0,e.updateObject.recordId=e.recordId,e.updateObject.originalPwd=O.encode(e.updateObject.originalPwd),e.updateObject.confirmPwd=O.encode(e.updateObject.confirmPwd),e.updateObject.newPwd=null,ne(e.updateObject).then(r=>{i.message.success("\u4FEE\u6539\u6210\u529F"),p.logout()}).catch(r=>{e.confirmLoading=!1,e.btnLoading=!1})})})}function E(){c.value&&c.value.resetFields()}function x(o,a){e.saveObject.avatarUrl=o}return(o,a)=>{const r=H,l=G,b=J,A=K,F=Q,_=W,m=X,I=g("a-icon"),k=g("cloudpayUpload"),B=Y,L=g("avatar-modal"),w=Z,f=z,D=ee;return N(),V("div",oe,[u(D,{onChange:E},{default:t(()=>[u(w,{key:"1",tab:"\u57FA\u672C\u4FE1\u606F"},{default:t(()=>[s("div",le,[u(B,{gutter:16},{default:t(()=>[u(m,{md:16,lg:16},{default:t(()=>[u(F,{ref_key:"infoFormModel",ref:v,model:e.saveObject,"label-col":{span:9},"wrapper-col":{span:10},rules:e.rules},{default:t(()=>[u(l,{label:"\u7528\u6237\u767B\u5F55\u540D:"},{default:t(()=>[u(r,{value:e.saveObject.loginUsername,"onUpdate:value":a[0]||(a[0]=n=>e.saveObject.loginUsername=n),disabled:""},null,8,["value"])]),_:1}),u(l,{label:"\u7528\u6237\u59D3\u540D\uFF1A",name:"realname"},{default:t(()=>[u(r,{value:e.saveObject.realname,"onUpdate:value":a[1]||(a[1]=n=>e.saveObject.realname=n)},null,8,["value"])]),_:1}),u(l,{label:"\u624B\u673A\u53F7\uFF1A",name:"telphone"},{default:t(()=>[u(r,{value:e.saveObject.telphone,"onUpdate:value":a[2]||(a[2]=n=>e.saveObject.telphone=n),disabled:""},null,8,["value"])]),_:1}),u(l,{label:"\u8BF7\u9009\u62E9\u6027\u522B\uFF1A"},{default:t(()=>[u(A,{value:e.saveObject.sex,"onUpdate:value":a[3]||(a[3]=n=>e.saveObject.sex=n)},{default:t(()=>[u(b,{value:1},{default:t(()=>a[8]||(a[8]=[d("\u7537")])),_:1}),u(b,{value:2},{default:t(()=>a[9]||(a[9]=[d("\u5973")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1},8,["model","rules"]),s("div",se,[u(_,{type:"primary",onClick:U,loading:e.btnLoading},{default:t(()=>a[10]||(a[10]=[d(" \u66F4\u65B0\u57FA\u672C\u4FE1\u606F ")])),_:1},8,["loading"])])]),_:1}),u(m,{md:8,lg:8,style:{minHeight:"180px",margin:"0 auto"}},{default:t(()=>[s("div",re,[s("img",{src:e.saveObject.avatarUrl,style:{border:"1px solid rgba(0, 0, 0, 0.08)"}},null,8,de),u(k,{style:{"margin-top":"10px"},action:e.action,accept:".jpg, .jpeg, .png",onUploadSuccess:a[4]||(a[4]=n=>x(n,""))},{uploadSlot:t(()=>[u(_,{style:{"margin-left":"5px"}},{default:t(()=>[u(I,{type:e.loading?"loading":"upload"},null,8,["type"]),d(" "+R(e.loading?"\u6B63\u5728\u4E0A\u4F20":"\u66F4\u6362\u5934\u50CF"),1)]),_:1})]),_:1},8,["action"])])]),_:1})]),_:1}),u(L,{ref:"modal",onOk:P},null,512)])]),_:1}),u(w,{key:"2",tab:"\u5B89\u5168\u4FE1\u606F"},{default:t(()=>[s("div",ie,[u(B,{gutter:16},{default:t(()=>[u(m,{md:16,lg:16},{default:t(()=>[u(F,{ref_key:"pwdFormModel",ref:c,model:e.updateObject,"label-col":{span:9},"wrapper-col":{span:10},rules:e.rulesPass},{default:t(()=>[u(l,{label:"\u539F\u5BC6\u7801\uFF1A",name:"originalPwd"},{default:t(()=>[u(f,{value:e.updateObject.originalPwd,"onUpdate:value":a[5]||(a[5]=n=>e.updateObject.originalPwd=n),placeholder:"\u8BF7\u8F93\u5165\u539F\u5BC6\u7801"},null,8,["value"])]),_:1}),u(l,{label:"\u65B0\u5BC6\u7801\uFF1A",name:"newPwd"},{default:t(()=>[u(f,{value:e.updateObject.newPwd,"onUpdate:value":a[6]||(a[6]=n=>e.updateObject.newPwd=n),placeholder:"\u8BF7\u8F93\u5165\u65B0\u5BC6\u7801"},null,8,["value"])]),_:1}),u(l,{label:"\u786E\u8BA4\u65B0\u5BC6\u7801\uFF1A",name:"confirmPwd"},{default:t(()=>[u(f,{value:e.updateObject.confirmPwd,"onUpdate:value":a[7]||(a[7]=n=>e.updateObject.confirmPwd=n),placeholder:"\u786E\u8BA4\u65B0\u5BC6\u7801"},null,8,["value"])]),_:1})]),_:1},8,["model","rules"]),s("div",ce,[u(_,{type:"primary",onClick:C,loading:e.btnLoading},{default:t(()=>a[11]||(a[11]=[d(" \u66F4\u65B0\u5BC6\u7801 ")])),_:1},8,["loading"])])]),_:1})]),_:1})])]),_:1})]),_:1})])}}});var ve=S(pe,[["__scopeId","data-v-69c2284a"]]);export{ve as default};
