package com.king.cloudpay.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.king.cloudpay.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 系统用户表
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "t_sys_user", autoResultMap = true)
public class SysUser extends BaseModel<SysUser> {

    //gw
    public static final LambdaQueryWrapper<SysUser> gw() {
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    /**
     * 系统用户ID
     */
    @TableId(value = "sys_user_id", type = IdType.AUTO)
    private Long sysUserId;

    /**
     * 登录用户名
     */
    private String loginUsername;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * 手机号
     */
    private String telphone;

    /**
     * 支付费率类型 0 百分比 1 单笔
     */
    private Byte rateType;

    /**
     * 支付汇率值
     */
    private BigDecimal raeTypeExchange;

    /**
     * 转账汇率类型 0 百分比 1 单笔
     */
    private Byte tranAccountsType;

    /**
     * 转账汇率值
     */
    private BigDecimal tranAccountsExchange;

    /**
     * 性别 0-未知, 1-男, 2-女
     */
    private Byte sex;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 员工编号
     */
    private String userNo;

    /**
     * 是否超管（超管拥有全部权限） 0-否 1-是
     */
    private Byte isAdmin;

    /**
     * 状态 0-停用 1-启用
     */
    private Byte state;

    /**
     * 代理商开通的国家
     */
    @TableField(exist = false)
    private String countryCodes;

    /**
     * 虚拟货币提现地址
     */
    private String virtualCurrencyAddress;

    /**
     * 所属系统： MGR-运营平台, MCH-商户中心
     */
    private String sysType;

    /**
     * google 验证码随机密钥
     */
    private String googlePrivate;

    /**
     * google 验证码是否绑定 0 未绑定 1 已绑定
     */
    private Byte googleState;

    /**
     * 所属商户ID / 0(平台)
     */
    private String belongInfoId;

    /**
     * 登录IP白名单
     */
    private String whiteIp;

    // Getter and Setter methods (in case Lombok doesn't work properly)
    public Long getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(Long sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getLoginUsername() {
        return loginUsername;
    }

    public void setLoginUsername(String loginUsername) {
        this.loginUsername = loginUsername;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getTelphone() {
        return telphone;
    }

    public void setTelphone(String telphone) {
        this.telphone = telphone;
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Byte getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(Byte isAdmin) {
        this.isAdmin = isAdmin;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getBelongInfoId() {
        return belongInfoId;
    }

    public void setBelongInfoId(String belongInfoId) {
        this.belongInfoId = belongInfoId;
    }

    public String getWhiteIp() {
        return whiteIp;
    }

    public void setWhiteIp(String whiteIp) {
        this.whiteIp = whiteIp;
    }

    public String getGooglePrivate() {
        return googlePrivate;
    }

    public void setGooglePrivate(String googlePrivate) {
        this.googlePrivate = googlePrivate;
    }

    public Byte getGoogleState() {
        return googleState;
    }

    public void setGoogleState(Byte googleState) {
        this.googleState = googleState;
    }

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

}
