import{r as y,p as b}from"./manage.6e729324.js";import{d as N,g as T,r as O,h,aE as p,w as t,bZ as R,o as i,b as o,I as W,m as q,F as S,i as v,a as A,aw as g,j as f,aF as _,c as V,t as M,B as H}from"./index.8746381c.js";import{C as K}from"./Card.d6389e0b.js";import"./index.8f4a8fa1.js";import"./TabPane.9792ea88.js";import"./useMergedState.8a9045a6.js";import"./index.4c901be3.js";const Z=N({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>()=>({})}},setup(x,{expose:C}){const{$infoBox:c,$access:j}=T().appContext.config.globalProperties,D=x,e=O({isAdd:!0,isShow:!1,saveObject:{},wayCode:null,rules:{wayCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u652F\u4ED8\u65B9\u5F0F\u4EE3\u7801",trigger:"blur"}],wayName:[{required:!0,message:"\u8BF7\u8F93\u5165\u652F\u4ED8\u65B9\u5F0F\u540D\u79F0",trigger:"blur"}]}}),l=h();function F(s){e.isAdd=!s,e.saveObject={},l.value&&l.value.resetFields(),e.isAdd||(e.wayCode=s,y.getById(b,s).then(u=>{e.saveObject=u})),e.isShow=!0}function k(){l.value.validate().then(s=>{s&&(e.isAdd?y.add(b,e.saveObject).then(u=>{c.message.success("\u65B0\u589E\u6210\u529F"),e.isShow=!1,D.callbackFunc()}):y.updateById(b,e.wayCode,e.saveObject).then(u=>{c.message.success("\u4FEE\u6539\u6210\u529F"),e.isShow=!1,D.callbackFunc()}))})}return C({show:F}),(s,u)=>{const E=W,w=q,n=S,a=R;return i(),p(a,{open:e.isShow,"onUpdate:open":u[2]||(u[2]=r=>e.isShow=r),title:e.isAdd?"\u65B0\u589E\u652F\u4ED8\u65B9\u5F0F":"\u4FEE\u6539\u652F\u4ED8\u65B9\u5F0F",onOk:k},{default:t(()=>[o(n,{ref_key:"infoFormModel",ref:l,model:e.saveObject,"label-col":{span:6},"wrapper-col":{span:15},rules:e.rules},{default:t(()=>[o(w,{label:"\u652F\u4ED8\u65B9\u5F0F\u4EE3\u7801\uFF1A",name:"wayCode"},{default:t(()=>[o(E,{value:e.saveObject.wayCode,"onUpdate:value":u[0]||(u[0]=r=>e.saveObject.wayCode=r),disabled:!e.isAdd},null,8,["value","disabled"])]),_:1}),o(w,{label:"\u652F\u4ED8\u65B9\u5F0F\u540D\u79F0\uFF1A",name:"wayName"},{default:t(()=>[o(E,{value:e.saveObject.wayName,"onUpdate:value":u[1]||(u[1]=r=>e.saveObject.wayName=r)},null,8,["value"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["open","title"])}}}),z={class:"table-page-search-wrapper"},G={class:"table-layer"},J={class:"table-page-search-submitButtons"},Q={key:0},se=N({__name:"List",setup(x){const{$infoBox:C,$access:c,$hasAgentEnt:j}=T().appContext.config.globalProperties,e=O({tableColumns:[{key:"wayCode",fixed:"left",title:"\u652F\u4ED8\u65B9\u5F0F\u4EE3\u7801",scopedSlots:{customRender:"wayCodeSlot"}},{key:"wayName",title:"\u652F\u4ED8\u65B9\u5F0F\u540D\u79F0",dataIndex:"wayName"},{key:"op",title:"\u64CD\u4F5C",width:"200px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],searchData:{},btnLoading:!1}),l=h(),F=h();function k(n){return y.list(b,n)}function s(n=!1){e.btnLoading=!0,l.value.refTable(n)}function u(){F.value.show()}function E(n){F.value.show(n)}function w(n){C.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>{y.delById(b,n).then(a=>{C.message.success("\u5220\u9664\u6210\u529F\uFF01"),l.value.refTable(!1)})})}return(n,a)=>{const r=v("cloudpay-text-up"),m=H,I=S,$=v("cloudpayTableColumns"),L=v("cloudpayTable"),P=K,U=v("page-header-wrapper");return i(),p(U,null,{default:t(()=>[o(P,null,{default:t(()=>[A("div",z,[g(c)("ENT_PC_WAY_SEARCH")?(i(),p(I,{key:0,layout:"inline",class:"table-head-ground"},{default:t(()=>[A("div",G,[o(r,{placeholder:"\u652F\u4ED8\u65B9\u5F0F\u4EE3\u7801",value:e.searchData.wayCode,"onUpdate:value":a[0]||(a[0]=d=>e.searchData.wayCode=d)},null,8,["value"]),o(r,{placeholder:"\u652F\u4ED8\u65B9\u5F0F\u540D\u79F0",value:e.searchData.wayName,"onUpdate:value":a[1]||(a[1]=d=>e.searchData.wayName=d)},null,8,["value"]),A("span",J,[o(m,{type:"primary",onClick:a[2]||(a[2]=d=>s(!0)),loading:e.btnLoading},{default:t(()=>a[5]||(a[5]=[f(" \u67E5\u8BE2 ")])),_:1,__:[5]},8,["loading"]),o(m,{style:{"margin-left":"8px"},onClick:a[3]||(a[3]=()=>e.searchData={})},{default:t(()=>a[6]||(a[6]=[f(" \u91CD\u7F6E ")])),_:1,__:[6]})])])]),_:1})):_("",!0)]),o(L,{onBtnLoadClose:a[4]||(a[4]=d=>e.btnLoading=!1),ref_key:"infoTable",ref:l,initData:!0,reqTableDataFunc:k,tableColumns:e.tableColumns,searchData:e.searchData,rowKey:"wayCode"},{opRow:t(()=>[g(c)("ENT_PC_WAY_ADD")?(i(),p(m,{key:0,type:"primary",onClick:u,class:"mg-b-30"},{default:t(()=>a[7]||(a[7]=[f(" \u65B0\u5EFA ")])),_:1,__:[7]})):_("",!0)]),bodyCell:t(({column:d,record:B})=>[d.key==="wayCode"?(i(),V("b",Q,M(B.wayCode),1)):_("",!0),d.key==="op"?(i(),p($,{key:1},{default:t(()=>[g(c)("ENT_PC_WAY_EDIT")?(i(),p(m,{key:0,type:"link",onClick:Y=>E(B.wayCode)},{default:t(()=>a[8]||(a[8]=[f(" \u4FEE\u6539 ")])),_:2,__:[8]},1032,["onClick"])):_("",!0),g(c)("ENT_PC_WAY_DEL")?(i(),p(m,{key:1,type:"link",danger:"",onClick:Y=>w(B.wayCode)},{default:t(()=>a[9]||(a[9]=[f(" \u5220\u9664 ")])),_:2,__:[9]},1032,["onClick"])):_("",!0)]),_:2},1024)):_("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),o(Z,{ref_key:"infoAddOrEdit",ref:F,callbackFunc:s},null,512)]),_:1})}}});export{se as default};
