<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/assets/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>商户平台-CloudPay计全支付</title>
    <style>
      * {
        padding: 0;
        margin: 0;
      }

      .first-loading-wrp {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        min-height: 420px;
        height: 100vh;
      }
      .first-loading-wrp > h1 {
        font-size: 128px;
      }
      .first-loading-wrp .loading-wrp {
        padding: 98px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .dot {
        animation: antRotate 1.2s infinite linear;
        transform: rotate(45deg);
        position: relative;
        display: inline-block;
        font-size: 32px;
        width: 32px;
        height: 32px;
        box-sizing: border-box;
      }
      .dot i {
        width: 14px;
        height: 14px;
        position: absolute;
        display: block;
        background-color: #1890ff;
        border-radius: 100%;
        transform: scale(0.75);
        transform-origin: 50% 50%;
        opacity: 0.3;
        animation: antSpinMove 1s infinite linear alternate;
      }
      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }
      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        -webkit-animation-delay: 0.4s;
        animation-delay: 0.4s;
      }
      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        -webkit-animation-delay: 0.8s;
        animation-delay: 0.8s;
      }
      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        -webkit-animation-delay: 1.2s;
        animation-delay: 1.2s;
      }
      @keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @-webkit-keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
      @-webkit-keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }

      /* Logo styles */
      #cloudpay_index_logo_img {
        height: 50px;
      }

      /* Logo text styles */
      #cloudpay_index_logo_text {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    </style>
    <script type="module" crossorigin src="/assets/index.fba97cfa.js"></script>
    <link rel="stylesheet" href="/assets/index.b1cb4719.css">
  </head>

  <body>
    <div id="app">
      <!-- 默认页面的等待效果 -->
      <div class="first-loading-wrp">
        <div>
          <img
            src="/assets/logo.070cbf2b.svg"
            id="cloudpay_index_logo_img"
            alt="CloudPay计全支付平台Logo"
            class="logo"
          />
        </div>
        <div class="loading-wrp">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <div
          id="cloudpay_index_logo_text"
        >
          计全科技
        </div>
      </div>
    </div>
    
  </body>
</html>

