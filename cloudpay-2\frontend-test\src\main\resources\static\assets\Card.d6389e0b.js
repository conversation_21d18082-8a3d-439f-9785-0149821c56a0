import{a5 as ta,a6 as ra,M as l,a7 as na,bC as m,a8 as _,d as da,u as oa,bD as h,ab as la,b as d,aj as sa,bE as ca,e as D,V as y,bF as E,bG as pa,bH as ga}from"./index.8746381c.js";import"./index.8f4a8fa1.js";import{S as $a}from"./index.4c901be3.js";import{T as j}from"./TabPane.9792ea88.js";const ba=a=>{const{antCls:e,componentCls:i,cardHeadHeight:n,cardPaddingBase:r,cardHeadTabsMarginBottom:p}=a;return l(l({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${r}px`,color:a.colorTextHeading,fontWeight:a.fontWeightStrong,fontSize:a.fontSizeLG,background:"transparent",borderBottom:`${a.lineWidth}px ${a.lineType} ${a.colorBorderSecondary}`,borderRadius:`${a.borderRadiusLG}px ${a.borderRadiusLG}px 0 0`},m()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":l(l({display:"inline-block",flex:1},_),{[`
          > ${i}-typography,
          > ${i}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${e}-tabs-top`]:{clear:"both",marginBottom:p,color:a.colorText,fontWeight:"normal",fontSize:a.fontSize,"&-bar":{borderBottom:`${a.lineWidth}px ${a.lineType} ${a.colorBorderSecondary}`}}})},ua=a=>{const{cardPaddingBase:e,colorBorderSecondary:i,cardShadow:n,lineWidth:r}=a;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:`
      ${r}px 0 0 0 ${i},
      0 ${r}px 0 0 ${i},
      ${r}px ${r}px 0 0 ${i},
      ${r}px 0 0 0 ${i} inset,
      0 ${r}px 0 0 ${i} inset;
    `,transition:`all ${a.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},ha=a=>{const{componentCls:e,iconCls:i,cardActionsLiMargin:n,cardActionsIconSize:r,colorBorderSecondary:p}=a;return l(l({margin:0,padding:0,listStyle:"none",background:a.colorBgContainer,borderTop:`${a.lineWidth}px ${a.lineType} ${p}`,display:"flex",borderRadius:`0 0 ${a.borderRadiusLG}px ${a.borderRadiusLG}px `},m()),{"& > li":{margin:n,color:a.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:a.cardActionsIconSize*2,fontSize:a.fontSize,lineHeight:a.lineHeight,cursor:"pointer","&:hover":{color:a.colorPrimary,transition:`color ${a.motionDurationMid}`},[`a:not(${e}-btn), > ${i}`]:{display:"inline-block",width:"100%",color:a.colorTextDescription,lineHeight:`${a.fontSize*a.lineHeight}px`,transition:`color ${a.motionDurationMid}`,"&:hover":{color:a.colorPrimary}},[`> ${i}`]:{fontSize:r,lineHeight:`${r*a.lineHeight}px`}},"&:not(:last-child)":{borderInlineEnd:`${a.lineWidth}px ${a.lineType} ${p}`}}})},ya=a=>l(l({margin:`-${a.marginXXS}px 0`,display:"flex"},m()),{"&-avatar":{paddingInlineEnd:a.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:a.marginXS}},"&-title":l({color:a.colorTextHeading,fontWeight:a.fontWeightStrong,fontSize:a.fontSizeLG},_),"&-description":{color:a.colorTextDescription}}),Sa=a=>{const{componentCls:e,cardPaddingBase:i,colorFillAlter:n}=a;return{[`${e}-head`]:{padding:`0 ${i}px`,background:n,"&-title":{fontSize:a.fontSize}},[`${e}-body`]:{padding:`${a.padding}px ${i}px`}}},fa=a=>{const{componentCls:e}=a;return{overflow:"hidden",[`${e}-body`]:{userSelect:"none"}}},ma=a=>{const{componentCls:e,cardShadow:i,cardHeadPadding:n,colorBorderSecondary:r,boxShadow:p,cardPaddingBase:S}=a;return{[e]:l(l({},na(a)),{position:"relative",background:a.colorBgContainer,borderRadius:a.borderRadiusLG,[`&:not(${e}-bordered)`]:{boxShadow:p},[`${e}-head`]:ba(a),[`${e}-extra`]:{marginInlineStart:"auto",color:"",fontWeight:"normal",fontSize:a.fontSize},[`${e}-body`]:l({padding:S,borderRadius:` 0 0 ${a.borderRadiusLG}px ${a.borderRadiusLG}px`},m()),[`${e}-grid`]:ua(a),[`${e}-cover`]:{"> *":{display:"block",width:"100%"},img:{borderRadius:`${a.borderRadiusLG}px ${a.borderRadiusLG}px 0 0`}},[`${e}-actions`]:ha(a),[`${e}-meta`]:ya(a)}),[`${e}-bordered`]:{border:`${a.lineWidth}px ${a.lineType} ${r}`,[`${e}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${e}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${a.motionDurationMid}, border-color ${a.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:i}},[`${e}-contain-grid`]:{[`${e}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${e}-loading) ${e}-body`]:{marginBlockStart:-a.lineWidth,marginInlineStart:-a.lineWidth,padding:0}},[`${e}-contain-tabs`]:{[`> ${e}-head`]:{[`${e}-head-title, ${e}-extra`]:{paddingTop:n}}},[`${e}-type-inner`]:Sa(a),[`${e}-loading`]:fa(a),[`${e}-rtl`]:{direction:"rtl"}}},xa=a=>{const{componentCls:e,cardPaddingSM:i,cardHeadHeightSM:n}=a;return{[`${e}-small`]:{[`> ${e}-head`]:{minHeight:n,padding:`0 ${i}px`,fontSize:a.fontSize,[`> ${e}-head-wrapper`]:{[`> ${e}-extra`]:{fontSize:a.fontSize}}},[`> ${e}-body`]:{padding:i}},[`${e}-small${e}-contain-tabs`]:{[`> ${e}-head`]:{[`${e}-head-title, ${e}-extra`]:{minHeight:n,paddingTop:0,display:"flex",alignItems:"center"}}}}};var va=ta("Card",a=>{const e=ra(a,{cardShadow:a.boxShadowCard,cardHeadHeight:a.fontSizeLG*a.lineHeightLG+a.padding*2,cardHeadHeightSM:a.fontSize*a.lineHeight+a.paddingXS*2,cardHeadPadding:a.padding,cardPaddingBase:a.paddingLG,cardHeadTabsMarginBottom:-a.padding-a.lineWidth,cardActionsLiMargin:`${a.paddingSM}px 0`,cardActionsIconSize:a.fontSize,cardPaddingSM:12});return[ma(e),xa(e)]});const{TabPane:Ca}=j,Ta=()=>({prefixCls:String,title:y.any,extra:y.any,bordered:{type:Boolean,default:!0},bodyStyle:{type:Object,default:void 0},headStyle:{type:Object,default:void 0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},type:{type:String},size:{type:String},actions:y.any,tabList:{type:Array},tabBarExtraContent:y.any,activeTabKey:String,defaultActiveTabKey:String,cover:y.any,onTabChange:{type:Function}}),za=da({compatConfig:{MODE:3},name:"ACard",inheritAttrs:!1,props:Ta(),slots:Object,setup(a,e){let{slots:i,attrs:n}=e;const{prefixCls:r,direction:p,size:S}=oa("card",a),[K,O]=va(r),F=c=>c.map((o,g)=>E(o)&&!pa(o)||!E(o)?d("li",{style:{width:`${100/c.length}%`},key:`action-${g}`},[d("span",null,[o])]):null),X=c=>{var s;(s=a.onTabChange)===null||s===void 0||s.call(a,c)},N=function(){let c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],s;return c.forEach(o=>{o&&ga(o.type)&&o.type.__ANT_CARD_GRID&&(s=!0)}),s};return()=>{var c,s,o,g,x,v;const{headStyle:V={},bodyStyle:U={},loading:H,bordered:q=!0,type:w,tabList:$,hoverable:J,activeTabKey:L,defaultActiveTabKey:Q,tabBarExtraContent:R=h((c=i.tabBarExtraContent)===null||c===void 0?void 0:c.call(i)),title:C=h((s=i.title)===null||s===void 0?void 0:s.call(i)),extra:T=h((o=i.extra)===null||o===void 0?void 0:o.call(i)),actions:z=h((g=i.actions)===null||g===void 0?void 0:g.call(i)),cover:G=h((x=i.cover)===null||x===void 0?void 0:x.call(i))}=a,b=la((v=i.default)===null||v===void 0?void 0:v.call(i)),t=r.value,Y={[`${t}`]:!0,[O.value]:!0,[`${t}-loading`]:H,[`${t}-bordered`]:q,[`${t}-hoverable`]:!!J,[`${t}-contain-grid`]:N(b),[`${t}-contain-tabs`]:$&&$.length,[`${t}-${S.value}`]:S.value,[`${t}-type-${w}`]:!!w,[`${t}-rtl`]:p.value==="rtl"},Z=d($a,{loading:!0,active:!0,paragraph:{rows:4},title:!1},{default:()=>[b]}),W=L!==void 0,k={size:"large",[W?"activeKey":"defaultActiveKey"]:W?L:Q,onChange:X,class:`${t}-head-tabs`};let A;const I=$&&$.length?d(j,k,{default:()=>[$.map(u=>{const{tab:M,slots:f}=u,P=f==null?void 0:f.tab;sa(!f,"Card","tabList slots is deprecated, Please use `customTab` instead.");let B=M!==void 0?M:i[P]?i[P](u):null;return B=ca(i,"customTab",u,()=>[B]),d(Ca,{tab:B,key:u.key,disabled:u.disabled},null)})],rightExtra:R?()=>R:null}):null;(C||T||I)&&(A=d("div",{class:`${t}-head`,style:V},[d("div",{class:`${t}-head-wrapper`},[C&&d("div",{class:`${t}-head-title`},[C]),T&&d("div",{class:`${t}-extra`},[T])]),I]));const aa=G?d("div",{class:`${t}-cover`},[G]):null,ea=d("div",{class:`${t}-body`,style:U},[H?Z:b]),ia=z&&z.length?d("ul",{class:`${t}-actions`},[F(z)]):null;return K(d("div",D(D({ref:"cardContainerRef"},n),{},{class:[Y,n.class]}),[A,aa,b&&b.length?ea:null,ia]))}}});var Ra=za;export{Ra as C};
