package com.king.cloudpay.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.king.cloudpay.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 商户应用表
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_mch_app")
public class MchApp extends BaseModel<MchApp> {

    private static final long serialVersionUID = 1L;

    //gw
    public static final LambdaQueryWrapper<MchApp> gw() {
        return new LambdaQueryWrapper<>();
    }

    /**
     * 应用ID
     */
    @TableId(value = "app_id", type = IdType.INPUT)
    private String appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 商户号
     */
    private String mchNo;

    /**
     * 应用状态: 0-停用, 1-正常
     */
    private Byte state;

    /**
     * 应用私钥
     */
    private String appSecret;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者用户ID
     */
    private Long createdUid;

    /**
     * 创建者姓名
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 支付费率
     */
    @TableField(exist = false)
    private String payMentRate;

    /**
     * 转账费率
     */
    @TableField(exist = false)
    private String tranRate;

    // Manual getter and setter methods for Lombok compatibility
    // These ensure the methods are available even if Lombok processing fails
    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
}
