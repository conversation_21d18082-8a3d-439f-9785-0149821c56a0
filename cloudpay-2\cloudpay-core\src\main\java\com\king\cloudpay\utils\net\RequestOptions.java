package com.king.cloudpay.utils.net;

import com.king.cloudpay.utils.Cloudpay;

/**
 * 请求参数选项内容
 *
 * <AUTHOR>
 * @date 2021-06-08 11:00
 */
public class RequestOptions {

    private String uri;
    private String version;
    private String signType;

    private String apiKey;

    private int connectTimeout;
    private int readTimeout;
    private int maxNetworkRetries;
    private String acceptLanguage;

    public static RequestOptions getDefault(String uri, String version) {
        return new RequestOptions(
                uri,
                version,
                Cloudpay.DEFAULT_SIGN_TYPE,
                Cloudpay.apiKey,
                Cloudpay.getConnectTimeout(),
                Cloudpay.getReadTimeout(),
                Cloudpay.getMaxNetworkRetries(),
                Cloudpay.getAcceptLanguage());
    }

    private RequestOptions(
            String uri,
            String version,
            String signType,
            String apiKey,
            int connectTimeout,
            int readTimeout,
            int maxNetworkRetries,
            String acceptLanguage) {
        this.uri = uri;
        this.version = version;
        this.signType = signType;
        this.apiKey = apiKey;
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.maxNetworkRetries = maxNetworkRetries;
        this.acceptLanguage = acceptLanguage;
    }

    public String getUri() {
        return uri;
    }

    public String getVersion() {
        return version;
    }

    public String getSignType() {
        return signType;
    }

    public String getApiKey() {
        return apiKey;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public int getMaxNetworkRetries() {
        return maxNetworkRetries;
    }

    public String getAcceptLanguage() {
        return acceptLanguage;
    }

    public static RequestOptionsBuilder builder() {
        return new RequestOptionsBuilder();
    }

    public static class RequestOptionsBuilder {

        private String uri;
        private String version;
        private String signType;
        private String apiKey;
        private int connectTimeout;
        private int readTimeout;
        private int maxNetworkRetries;
        private String acceptLanguage;

        public RequestOptionsBuilder() {
            this.signType = Cloudpay.DEFAULT_SIGN_TYPE;
            this.apiKey = Cloudpay.apiKey;
            this.connectTimeout = Cloudpay.getConnectTimeout();
            this.readTimeout = Cloudpay.getReadTimeout();
            this.maxNetworkRetries = Cloudpay.getMaxNetworkRetries();
            this.acceptLanguage = Cloudpay.getAcceptLanguage();
        }

        public String getUri() {
            return uri;
        }

        public RequestOptionsBuilder setUri(String uri) {
            this.uri = normalizeApiUri(uri);
            return this;
        }

        public String getVersion() {
            return version;
        }

        public RequestOptionsBuilder setVersion(String version) {
            this.version = version;
            return this;
        }

        public String getSignType() {
            return signType;
        }

        public RequestOptionsBuilder setSignType(String signType) {
            this.signType = signType;
            return this;
        }

        public String getApiKey() {
            return apiKey;
        }

        public RequestOptionsBuilder setApiKey(String apiKey) {
            this.apiKey = normalizeApiKey(apiKey);
            return this;
        }

        public RequestOptionsBuilder clearApiKey() {
            this.apiKey = null;
            return this;
        }

        public int getConnectTimeout() {
            return connectTimeout;
        }

        public RequestOptionsBuilder setConnectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
            return this;
        }

        public int getReadTimeout() {
            return readTimeout;
        }

        public RequestOptionsBuilder setReadTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }

        public int getMaxNetworkRetries() {
            return maxNetworkRetries;
        }

        public RequestOptionsBuilder setMaxNetworkRetries(int maxNetworkRetries) {
            this.maxNetworkRetries = maxNetworkRetries;
            return this;
        }

        public String getAcceptLanguage() {
            return acceptLanguage;
        }

        public RequestOptionsBuilder setAcceptLanguage(String acceptLanguage) {
            this.acceptLanguage = normalizeAcceptLanguage(acceptLanguage);
            return this;
        }

        public RequestOptions build() {
            return new RequestOptions(
                    normalizeApiUri(this.uri),
                    version,
                    signType,
                    normalizeApiKey(this.apiKey),
                    connectTimeout,
                    readTimeout,
                    maxNetworkRetries,
                    acceptLanguage);
        }
    }

    private static String normalizeApiUri(String apiUri) {
        if (apiUri == null) {
            throw new InvalidRequestOptionsException("接口URI不能为空!");
        }
        if (apiUri.startsWith("/")) {
            throw new InvalidRequestOptionsException("接口URI(" + apiUri + ")不能以'/'开头");
        }
        return apiUri;
    }

    private static String normalizeApiKey(String apiKey) {
        if (apiKey == null) {
            return null;
        }
        String normalized = apiKey.trim();
        if (normalized.isEmpty()) {
            throw new InvalidRequestOptionsException("API key不能为空!");
        }
        return normalized;
    }

    private static String normalizeAcceptLanguage(String acceptLanguage) {
        if (acceptLanguage == null) {
            return null;
        }
        String normalized = acceptLanguage.trim();
        if (normalized.isEmpty()) {
            throw new InvalidRequestOptionsException("Accept-Language不能空!");
        }
        return normalized;
    }

    public static class InvalidRequestOptionsException extends RuntimeException {

        private static final long serialVersionUID = 1L;

        public InvalidRequestOptionsException(String message) {
            super(message);
        }
    }
}
