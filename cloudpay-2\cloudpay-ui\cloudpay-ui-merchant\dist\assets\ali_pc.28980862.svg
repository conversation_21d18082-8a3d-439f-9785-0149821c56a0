<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" viewBox="0 0 40 40">
  <defs>
    <clipPath id="clip-path">
      <rect id="矩形_2580" data-name="矩形 2580" width="8" height="8" fill="#fff"/>
    </clipPath>
  </defs>
  <g id="支付宝WEB" transform="translate(-6425 -11807)">
    <path id="路径_4159" data-name="路径 4159" d="M190.234,137.3l-.02-.02-.02-.02c-1.443-1.443-3.6-2.177-6.514-2.562a91.718,91.718,0,0,0-10.431-.453,91.681,91.681,0,0,0-10.431.453c-2.93.387-5.091,1.126-6.534,2.583s-2.2,3.6-2.582,6.534a91.7,91.7,0,0,0-.453,10.431,91.682,91.682,0,0,0,.453,10.431c.385,2.917,1.12,5.071,2.563,6.514l.02.02.02.02c1.443,1.443,3.6,2.177,6.514,2.562a91.714,91.714,0,0,0,10.431.453,91.681,91.681,0,0,0,10.431-.453c2.93-.387,5.091-1.126,6.534-2.583s2.2-3.6,2.582-6.534a91.729,91.729,0,0,0,.453-10.431,91.681,91.681,0,0,0-.453-10.431c-.385-2.917-1.12-5.071-2.563-6.514Z" transform="translate(6271.75 11672.75)" fill="#1977fd"/>
    <g id="组_1011" data-name="组 1011" transform="translate(5891 11690.5)">
      <g id="组_1009" data-name="组 1009" transform="translate(550 130)">
        <g id="组_1008" data-name="组 1008" transform="translate(0 0)" clip-path="url(#clip-path)">
          <path id="路径_4170" data-name="路径 4170" d="M553.877,130.52a3.993,3.993,0,1,0,4,3.993,4,4,0,0,0-4-3.993Zm.321,7.273a.17.17,0,0,0-.061.009v-1.883c.452-.009.86-.017,1.234-.043a4.974,4.974,0,0,1-1.173,1.918Zm-.643,0a5.015,5.015,0,0,1-1.173-1.918c.365.026.782.043,1.234.043V137.8c-.017,0-.035-.009-.061-.009Zm-2.98-3.281a3.92,3.92,0,0,1,.061-.642c.191-.035.556-.087,1.069-.139a4.6,4.6,0,0,0-.07.79,5.476,5.476,0,0,0,.061.781c-.5-.052-.878-.1-1.069-.139a5.478,5.478,0,0,1-.052-.651Zm1.59,0a4.228,4.228,0,0,1,.087-.833c.4-.026.852-.052,1.364-.052V135.4c-.5-.009-.964-.026-1.373-.061a4.831,4.831,0,0,1-.078-.825Zm2.024-3.281a5.288,5.288,0,0,1,1.173,1.918c-.365-.026-.773-.043-1.225-.043v-1.883c.017,0,.035.009.052.009Zm-.573-.009v1.883c-.452.009-.86.017-1.225.043a5.305,5.305,0,0,1,1.164-1.918c.026,0,.043-.009.061-.009Zm.521,4.175v-1.771c.5.009.965.026,1.364.052a4.245,4.245,0,0,1,.009,1.667c-.4.026-.86.043-1.373.052Zm1.9-1.666c.513.052.878.1,1.069.139a3.16,3.16,0,0,1,.07.642,3.92,3.92,0,0,1-.061.642c-.191.035-.556.087-1.069.139a4.621,4.621,0,0,0,.061-.79,4.48,4.48,0,0,0-.069-.772Zm.912-.417c-.243-.035-.591-.078-1.034-.122a5.7,5.7,0,0,0-.878-1.771,3.31,3.31,0,0,1,1.911,1.892Zm-4.24-1.892a5.374,5.374,0,0,0-.878,1.762c-.434.035-.782.087-1.034.122a3.345,3.345,0,0,1,1.912-1.883ZM550.8,135.71c.243.035.591.078,1.025.122a5.371,5.371,0,0,0,.869,1.762,3.341,3.341,0,0,1-1.894-1.883Zm4.258,1.883a5.318,5.318,0,0,0,.869-1.753c.434-.035.782-.087,1.025-.121a3.351,3.351,0,0,1-1.894,1.875Zm0,0" transform="translate(-549.881 -130.515)" fill="#fff"/>
        </g>
      </g>
    </g>
    <path id="联合_46" data-name="联合 46" d="M3.25,20A.249.249,0,0,1,3,19.75v-1a.25.25,0,0,1,.25-.25h6V15H2a2,2,0,0,1-2-2V2A2,2,0,0,1,2,0H18a2,2,0,0,1,2,2V13a2,2,0,0,1-2,2H10.75v3.5h6a.25.25,0,0,1,.25.25v1a.25.25,0,0,1-.25.249ZM1.5,2.25v10.5a.751.751,0,0,0,.75.75h15.5a.751.751,0,0,0,.751-.75V2.25a.751.751,0,0,0-.751-.75H2.25A.751.751,0,0,0,1.5,2.25Z" transform="translate(6435 11817)" fill="#fff"/>
  </g>
</svg>
