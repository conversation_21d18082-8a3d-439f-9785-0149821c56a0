package com.king.cloudpay.core.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.security.authentication.InternalAuthenticationServiceException;

/*
 * Spring Security 框架自定义异常类
 *
 * <AUTHOR>
 * @date 2021/6/15 11:23
 */
@Getter
@Setter
public class CloudpayAuthenticationException extends InternalAuthenticationServiceException {

    private BizException bizException;

    public CloudpayAuthenticationException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public CloudpayAuthenticationException(String msg) {
        super(msg);
    }

    public static CloudpayAuthenticationException build(String msg) {
        return build(new BizException(msg));
    }

    public static CloudpayAuthenticationException build(BizException ex) {

        CloudpayAuthenticationException result = new CloudpayAuthenticationException(ex.getMessage());
        result.setBizException(ex);
        return result;
    }

    // Manual getter/setter methods (in case Lombok doesn't work properly)
    public BizException getBizException() {
        return bizException;
    }

    public void setBizException(BizException bizException) {
        this.bizException = bizException;
    }

}
