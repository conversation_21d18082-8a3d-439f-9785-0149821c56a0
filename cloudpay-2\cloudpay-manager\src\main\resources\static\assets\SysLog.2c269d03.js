import{r as F,M as k}from"./manage.6e729324.js";import{h as Z}from"./moment.40bc58bf.js";import{d as z,g as Q,h as X,r as x,aE as m,w as t,i as b,o as p,b as e,a as f,j as s,aw as h,aF as c,c as S,t as d,aJ as ee,cF as R,bZ as te,B as ae,F as ue,c8 as le,m as se}from"./index.8746381c.js";import{R as oe}from"./dayjs.1ec7c0a1.js";import{S as ne,a as de}from"./index.08051bcd.js";import{D as re,a as ie}from"./index.cbe5d957.js";import{_ as _e}from"./index.54e910b7.js";import{C as pe}from"./Card.d6389e0b.js";import{_ as fe,a as me}from"./index.7c25015e.js";import{_ as ce}from"./index.5e527ed3.js";import{_ as ye}from"./index.9b74c380.js";import"./useMergedState.8a9045a6.js";import"./useMemo.91f6d273.js";import"./List.ee977be2.js";import"./TabPane.9792ea88.js";import"./index.8f4a8fa1.js";import"./index.4c901be3.js";const De={class:"table-page-search-wrapper"},be={class:"table-layer"},Ce={class:"table-page-search-submitButtons"},ge={key:0},Ae=z({__name:"SysLog",setup(Fe){const{$infoBox:ke,$access:v,$hasAgentEnt:ve}=Q().appContext.config.globalProperties,C=X(),w=x([{key:"userName",title:"\u7528\u6237\u540D",fixed:"left",scopedSlots:{customRender:"userNameSlot"}},{key:"userId",title:"\u7528\u6237ID",dataIndex:"userId"},{key:"userIp",title:"\u7528\u6237IP",dataIndex:"userIp"},{key:"sysType",title:"\u6240\u5C5E\u7CFB\u7EDF",scopedSlots:{customRender:"sysTypeSlot"}},{key:"methodRemark",title:"\u64CD\u4F5C\u63CF\u8FF0",ellipsis:!0,dataIndex:"methodRemark"},{key:"createdAt",dataIndex:"createdAt",title:"\u521B\u5EFA\u65E5\u671F"},{key:"op",title:"\u64CD\u4F5C",width:"100px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}]),a=x({date:"",searchData:{},selectedIds:[],createdStart:"",createdEnd:"",visible:!1,detailData:{},btnLoading:!1});function M(o){return F.list(k,o)}function N(){if(a.selectedIds.length===0)return R.error("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u65E5\u5FD7"),!1;te.confirm({title:"\u786E\u8BA4\u5220\u9664"+a.selectedIds.length+"\u6761\u65E5\u5FD7\u5417\uFF1F",okType:"danger",onOk(){F.delById(k,a.selectedIds).then(o=>{a.selectedIds=[],C.value.refTable(!0),R.success("\u5220\u9664\u6210\u529F")})}})}function L(o){a.selectedIds=o}function H(o){F.getById(k,o).then(u=>{a.detailData=u}),a.visible=!0}function U(o,u){a.searchData.createdStart=u[0],a.searchData.createdEnd=u[1]}function G(o){return o&&o>Z().endOf("day")}function P(){a.visible=!1}function Y(){C.value.refTable(!0)}return(o,u)=>{const q=b("a-icon"),A=oe,E=b("cloudpay-text-up"),g=ne,O=de,y=ae,j=ue,I=_e,V=b("cloudpayTable"),$=pe,r=re,i=ie,n=fe,D=me,J=ce,B=le,T=se,K=ye,W=b("page-header-wrapper");return p(),m(W,null,{default:t(()=>[e($,null,{default:t(()=>[f("div",De,[e(j,{layout:"inline",class:"table-head-ground"},{default:t(()=>[f("div",be,[e(A,{class:"table-head-layout",onChange:U,value:a.date,"onUpdate:value":u[0]||(u[0]=l=>a.date=l),"show-time":{format:"HH:mm:ss"},format:"YYYY-MM-DD HH:mm:ss","disabled-date":G},{default:t(()=>[e(q,{slot:"suffixIcon",type:"sync"})]),_:1},8,["value"]),e(E,{placeholder:"\u7528\u6237ID",value:a.searchData.userId,"onUpdate:value":u[1]||(u[1]=l=>a.searchData.userId=l)},null,8,["value"]),e(E,{placeholder:"\u7528\u6237\u540D",value:a.searchData.userName,"onUpdate:value":u[2]||(u[2]=l=>a.searchData.userName=l)},null,8,["value"]),e(O,{value:a.searchData.sysType,"onUpdate:value":u[3]||(u[3]=l=>a.searchData.sysType=l),placeholder:"\u6240\u5C5E\u7CFB\u7EDF",class:"table-head-layout"},{default:t(()=>[e(g,{value:""},{default:t(()=>u[9]||(u[9]=[s("\u5168\u90E8")])),_:1,__:[9]}),e(g,{value:"MGR"},{default:t(()=>u[10]||(u[10]=[s("\u8FD0\u8425\u5E73\u53F0")])),_:1,__:[10]}),e(g,{value:"MCH"},{default:t(()=>u[11]||(u[11]=[s("\u5546\u6237\u7CFB\u7EDF")])),_:1,__:[11]})]),_:1},8,["value"]),f("span",Ce,[e(y,{type:"primary",onClick:Y,loading:a.btnLoading},{default:t(()=>u[12]||(u[12]=[s(" \u641C\u7D22 ")])),_:1,__:[12]},8,["loading"]),e(y,{style:{"margin-left":"8px"},onClick:u[4]||(u[4]=()=>{a.searchData={},a.date=""})},{default:t(()=>u[13]||(u[13]=[s(" \u91CD\u7F6E ")])),_:1,__:[13]})])])]),_:1}),f("div",null,[h(v)("ENT_SYS_LOG_DEL")?(p(),m(y,{key:0,type:"primary",danger:"",onClick:N,class:"mg-b-30"},{default:t(()=>u[14]||(u[14]=[s(" \u5220\u9664 ")])),_:1,__:[14]})):c("",!0)])]),e(V,{onBtnLoadClose:u[5]||(u[5]=l=>a.btnLoading=!1),ref_key:"infoTable",ref:C,initData:!0,"row-selection":{onChange:L},reqTableDataFunc:M,tableColumns:w,searchData:a.searchData,rowKey:"sysLogId"},{bodyCell:t(({column:l,record:_})=>[l.key==="userName"?(p(),S("b",ge,d(_.userName),1)):c("",!0),l.key==="sysType"?(p(),m(I,{key:_.sysType,color:_.sysType==="MGR"?"green":_.sysType==="MCH"?"geekblue":"loser"},{default:t(()=>[s(d(_.sysType==="MGR"?"\u8FD0\u8425\u5E73\u53F0":_.sysType==="MCH"?"\u5546\u6237\u7CFB\u7EDF":"\u5176\u4ED6"),1)]),_:2},1032,["color"])):c("",!0),l.key==="op"?(p(),S(ee,{key:2},[h(v)("ENT_SYS_LOG_VIEW")?(p(),m(y,{key:0,type:"link",onClick:Ee=>H(_.sysLogId)},{default:t(()=>u[15]||(u[15]=[s(" \u8BE6\u60C5 ")])),_:2,__:[15]},1032,["onClick"])):c("",!0)],64)):c("",!0)]),_:1},8,["row-selection","tableColumns","searchData"])]),_:1}),f("template",null,[e(K,{width:"40%",placement:"right",closable:!0,open:a.visible,"onUpdate:open":u[8]||(u[8]=l=>a.visible=l),title:a.visible===!0?"\u65E5\u5FD7\u8BE6\u60C5":"",onClose:P},{default:t(()=>[e(D,{justify:"space-between",type:"flex"},{default:t(()=>[e(n,{sm:12},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u7528\u6237ID"},{default:t(()=>[s(d(a.detailData.userId),1)]),_:1})]),_:1})]),_:1}),e(n,{sm:12},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u7528\u6237IP"},{default:t(()=>[s(d(a.detailData.userIp),1)]),_:1})]),_:1})]),_:1}),e(n,{sm:12},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u7528\u6237\u540D"},{default:t(()=>[f("b",null,d(a.detailData.userName),1)]),_:1})]),_:1})]),_:1}),e(n,{sm:12},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u6240\u5C5E\u7CFB\u7EDF"},{default:t(()=>[(p(),m(I,{key:a.detailData.sysType,color:a.detailData.sysType==="MGR"?"green":a.detailData.sysType==="MCH"?"geekblue":"loser"},{default:t(()=>[s(d(a.detailData.sysType==="MGR"?"\u8FD0\u8425\u5E73\u53F0":a.detailData.sysType==="MCH"?"\u5546\u6237\u7CFB\u7EDF":"\u5176\u4ED6"),1)]),_:1},8,["color"]))]),_:1})]),_:1})]),_:1})]),_:1}),e(J),e(D,{justify:"space-between",type:"flex"},{default:t(()=>[e(n,{sm:24},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u64CD\u4F5C\u63CF\u8FF0"},{default:t(()=>[s(d(a.detailData.methodRemark),1)]),_:1})]),_:1})]),_:1}),e(n,{sm:24},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u8BF7\u6C42\u65B9\u6CD5"},{default:t(()=>[s(d(a.detailData.methodName),1)]),_:1})]),_:1})]),_:1}),e(n,{sm:24},{default:t(()=>[e(i,null,{default:t(()=>[e(r,{label:"\u8BF7\u6C42\u5730\u5740"},{default:t(()=>[s(d(a.detailData.reqUrl),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(D,{justify:"start",type:"flex"},{default:t(()=>[e(n,{sm:24},{default:t(()=>[e(T,{label:"\u8BF7\u6C42\u53C2\u6570"},{default:t(()=>[e(B,{disabled:"disabled",style:{"background-color":"black",color:"#ffffff",height:"100px"},value:a.detailData.optReqParam,"onUpdate:value":u[6]||(u[6]=l=>a.detailData.optReqParam=l)},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(D,{justify:"start",type:"flex"},{default:t(()=>[e(n,{sm:24},{default:t(()=>[e(T,{label:"\u54CD\u5E94\u53C2\u6570"},{default:t(()=>[e(B,{disabled:"disabled",style:{"background-color":"black",color:"#ffffff",height:"150px"},value:a.detailData.optResInfo,"onUpdate:value":u[7]||(u[7]=l=>a.detailData.optResInfo=l)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["open","title"])])]),_:1})}}});export{Ae as default};
