2025-07-18 00:57:38.791 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 00:57:41.291 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@7ceb6c45: tags=[[amq.ctag-gsJndesReYWZyQGvk-lBcA]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@75793f17 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 50071], acknowledgeMode=AUTO local queue size=0
2025-07-18 00:57:41.549 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 00:57:41.653 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@5763a655: tags=[[amq.ctag-s8_N3I8tbNB-6UDPta26Jg]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-18 00:57:41.723 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:1/SimpleConnection@704adfaa [delegate=amqp://guest@127.0.0.1:5672/, localPort= 55127]
2025-07-18 00:57:41.758 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 11:07:59.099 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 11:07:59.931 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@ff8944c: tags=[[amq.ctag-6FtVzToU6lfqa06IiA53Lw]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@704adfaa [delegate=amqp://guest@127.0.0.1:5672/, localPort= 55127], acknowledgeMode=AUTO local queue size=0
2025-07-18 11:08:00.192 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 11:08:00.533 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@60da1675: tags=[[amq.ctag-ZBQacCDWmmObiM_DNwoCAw]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-18 11:08:00.607 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:2/SimpleConnection@7cdec92c [delegate=amqp://guest@127.0.0.1:5672/, localPort= 61116]
2025-07-18 11:08:00.688 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 12:41:51.627 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 12:41:52.025 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@3ad78b5b: tags=[[amq.ctag-hM2u4PSADZMKrLHslDrDyw]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@7cdec92c [delegate=amqp://guest@127.0.0.1:5672/, localPort= 61116], acknowledgeMode=AUTO local queue size=0
2025-07-18 12:41:52.082 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 12:41:52.121 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:3/SimpleConnection@283daa8c [delegate=amqp://guest@127.0.0.1:5672/, localPort= 53787]
2025-07-18 12:41:52.126 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 12:41:52.178 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@5567a088: tags=[[amq.ctag-tdgSmTB_ZL42HAD5JAFPXA]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@283daa8c [delegate=amqp://guest@127.0.0.1:5672/, localPort= 53787], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:05:03.117 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 13:05:03.607 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@7e94a3e6: tags=[[amq.ctag-dlavzIvIux4vwOzZKydGkg]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@283daa8c [delegate=amqp://guest@127.0.0.1:5672/, localPort= 53787], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:05:03.609 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 13:05:03.618 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:4/SimpleConnection@2c8bf1fd [delegate=amqp://guest@127.0.0.1:5672/, localPort= 55701]
2025-07-18 13:05:03.618 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 13:05:03.683 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-4] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@459077a9: tags=[[amq.ctag-K9CD-1HuovApifmp60UxZA]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@2c8bf1fd [delegate=amqp://guest@127.0.0.1:5672/, localPort= 55701], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:14:43.139 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 13:14:43.387 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@68b6c84f: tags=[[amq.ctag-8uQKBO5q2KOdKLIj4InprA]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@2c8bf1fd [delegate=amqp://guest@127.0.0.1:5672/, localPort= 55701], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:14:43.394 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 13:14:43.407 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:5/SimpleConnection@2e50cea7 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 57144]
2025-07-18 13:14:43.408 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 13:14:43.412 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-5] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@5a10379: tags=[[amq.ctag-P-Iu2xXIR08Wc4wTaa5Nig]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@2e50cea7 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 57144], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:35:35.674 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 13:35:36.325 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@2fe65f8f: tags=[[amq.ctag-EqF56a5E_N2Z1a5gWb4D5A]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@2e50cea7 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 57144], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:35:36.327 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-6] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@282da699: tags=[[amq.ctag-vzgkb2BzFxX29MAUcG3Upg]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@2e50cea7 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 57144], acknowledgeMode=AUTO local queue size=0
2025-07-18 13:35:36.334 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 13:35:36.353 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:6/SimpleConnection@28c33841 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 58639]
2025-07-18 13:35:36.353 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 14:40:35.437 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 14:40:36.533 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-7] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@6b2dc904: tags=[[amq.ctag-Q3b1pwnXxY9lSe0XUqH9Pw]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,1), conn: Proxy@696fad31 Shared Rabbit Connection: SimpleConnection@28c33841 [delegate=amqp://guest@127.0.0.1:5672/, localPort= 58639], acknowledgeMode=AUTO local queue size=0
2025-07-18 14:40:36.568 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-8] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 14:40:36.587 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] [o.s.a.r.l.SimpleMessageListenerContainer] - Restarting Consumer@76c574b8: tags=[[amq.ctag-8-Fd1koZadULgo2IXB6aVA]], channel=Cached Rabbit Channel: AMQChannel(amqp://guest@127.0.0.1:5672/,2), conn: Proxy@696fad31 Shared Rabbit Connection: null, acknowledgeMode=AUTO local queue size=0
2025-07-18 14:40:36.616 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-8] [o.s.a.r.c.CachingConnectionFactory] - Created new connection: rabbitConnectionFactory#1d805aa1:7/SimpleConnection@1b4f1e3e [delegate=amqp://guest@127.0.0.1:5672/, localPort= 53617]
2025-07-18 14:40:36.618 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-8] [o.s.a.r.c.RabbitAdmin] - Auto-declaring a non-durable, auto-delete, or exclusive Queue (spring.gen-bsDIMn4NRhiL6y8PWEZniA) durable:false, auto-delete:true, exclusive:true. It will be redeclared if the broker stops and is restarted while the connection factory is alive, but all messages will be lost.
2025-07-18 15:18:46.941 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - Starting CloudpayMchApplication v1.13.0 using Java 17.0.14 on windfred with PID 38108 (C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target\cloudpay-merchant.jar started by winfred in C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target)
2025-07-18 15:18:46.948 DEBUG [main] [c.k.c.m.b.CloudpayMchApplication] - Running with Spring Boot v2.4.8, Spring v5.3.8
2025-07-18 15:18:46.949 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - No active profile set, falling back to default profiles: default
2025-07-18 15:18:52.258 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-18 15:18:52.270 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-18 15:18:52.355 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-18 15:18:52.950 INFO  [main] [o.s.c.a.ConfigurationClassPostProcessor] - Cannot enhance @Configuration bean definition 'rabbitMQBeanProcessor' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-18 15:18:54.800 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 15:18:54.807 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7283a783] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 15:18:54.850 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 15:18:54.915 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@3f19b8b3' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 15:18:54.930 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 15:18:55.920 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat initialized with port(s): 9218 (http)
2025-07-18 15:18:55.944 INFO  [main] [o.a.c.h.Http11NioProtocol] - Initializing ProtocolHandler ["http-nio-9218"]
2025-07-18 15:18:55.946 INFO  [main] [o.a.c.c.StandardService] - Starting service [Tomcat]
2025-07-18 15:18:55.947 INFO  [main] [o.a.c.c.StandardEngine] - Starting Servlet engine: [Apache Tomcat/9.0.48]
2025-07-18 15:18:56.095 INFO  [main] [o.a.c.c.C.[.[.[/]] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:18:56.096 INFO  [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] - Root WebApplicationContext: initialization completed in 8853 ms
2025-07-18 15:18:56.715 INFO  [main] [c.a.d.s.b.a.DruidDataSourceAutoConfigure] - Init DruidDataSource
2025-07-18 15:18:58.484 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} inited
2025-07-18 15:18:59.703 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysUserRoleRela".
2025-07-18 15:19:00.665 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.MchAccountChange".
2025-07-18 15:19:00.890 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysRoleEntRela".
2025-07-18 15:19:03.277 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.AgentInfoVo".
2025-07-18 15:19:04.679 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.InterfaceBankdataInfo".
2025-07-18 15:19:04.727 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.JiaoyiTotleVo".
2025-07-18 15:19:05.635 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysInterfaceEntCountry".
2025-07-18 15:19:05.708 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysuserEntCountry".
2025-07-18 15:19:07.909 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/', GET] with []
2025-07-18 15:19:07.910 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.html', GET] with []
2025-07-18 15:19:07.910 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/favicon.ico', GET] with []
2025-07-18 15:19:07.911 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.html', GET] with []
2025-07-18 15:19:07.911 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.css', GET] with []
2025-07-18 15:19:07.911 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.js', GET] with []
2025-07-18 15:19:07.911 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.png', GET] with []
2025-07-18 15:19:07.912 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpg', GET] with []
2025-07-18 15:19:07.912 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpeg', GET] with []
2025-07-18 15:19:07.912 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.svg', GET] with []
2025-07-18 15:19:07.912 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.ico', GET] with []
2025-07-18 15:19:07.913 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.webp', GET] with []
2025-07-18 15:19:07.913 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.txt', GET] with []
2025-07-18 15:19:07.913 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.xls', GET] with []
2025-07-18 15:19:07.913 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.mp4', GET] with []
2025-07-18 15:19:07.913 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/api/anon/**'] with []
2025-07-18 15:19:07.982 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@557b6a37, org.springframework.security.web.context.SecurityContextPersistenceFilter@6963b88c, org.springframework.security.web.header.HeaderWriterFilter@1e7f19b4, org.springframework.web.filter.CorsFilter@68577ba8, org.springframework.security.web.authentication.logout.LogoutFilter@2fd72332, com.king.cloudpay.mch.secruity.JeeAuthenticationTokenFilter@11b32a14, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54e43bfe, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c369270, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c33e528, org.springframework.security.web.session.SessionManagementFilter@2fbe26da, org.springframework.security.web.access.ExceptionTranslationFilter@10e5bf9c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@718f805a]
2025-07-18 15:19:08.993 INFO  [main] [o.s.b.a.w.s.WelcomePageHandlerMapping] - Adding welcome page: class path resource [static/index.html]
2025-07-18 15:19:10.223 INFO  [main] [o.w.s.a.Web3jAutoConfiguration] - Building service for endpoint: null
2025-07-18 15:19:10.456 INFO  [main] [o.a.c.h.Http11NioProtocol] - Starting ProtocolHandler ["http-nio-9218"]
2025-07-18 15:19:10.507 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat started on port(s): 9218 (http) with context path ''
2025-07-18 15:19:10.511 INFO  [main] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 15:19:10.632 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 15:19:10.639 INFO  [main] [o.s.a.r.l.SimpleMessageListenerContainer] - Broker not available; cannot force queue declarations during start: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
2025-07-18 15:19:10.666 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 15:19:10.677 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 15:19:10.682 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2167)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2140)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2120)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1912)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1893)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1347)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 12 common frames omitted
2025-07-18 15:19:10.693 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 15:19:10.702 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 15:19:10.702 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Consumer received fatal exception on startup
org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 15:19:10.704 WARN  [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
2025-07-18 15:19:10.706 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Stopping container from aborted consumer
2025-07-18 15:19:10.708 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Waiting for workers to finish.
2025-07-18 15:19:10.708 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Successfully waited for workers to finish.
2025-07-18 15:19:10.738 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closing ...
2025-07-18 15:19:10.754 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closed
2025-07-18 15:19:10.784 INFO  [main] [o.a.c.h.Http11NioProtocol] - Pausing ProtocolHandler ["http-nio-9218"]
2025-07-18 15:19:10.785 INFO  [main] [o.a.c.c.StandardService] - Stopping service [Tomcat]
2025-07-18 15:19:10.797 INFO  [main] [o.a.c.h.Http11NioProtocol] - Stopping ProtocolHandler ["http-nio-9218"]
2025-07-18 15:19:10.801 INFO  [main] [o.a.c.h.Http11NioProtocol] - Destroying ProtocolHandler ["http-nio-9218"]
2025-07-18 15:19:10.814 INFO  [main] [o.s.b.a.l.ConditionEvaluationReportLoggingListener] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 15:19:10.856 ERROR [main] [o.s.b.SpringApplication] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:181)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.king.cloudpay.mch.bootstrap.CloudpayMchApplication.main(CloudpayMchApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
Caused by: org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.waitForConsumersToStart(SimpleMessageListenerContainer.java:601)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doStart(SimpleMessageListenerContainer.java:562)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.start(AbstractMessageListenerContainer.java:1376)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.startIfNecessary(RabbitListenerEndpointRegistry.java:299)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.start(RabbitListenerEndpointRegistry.java:249)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	... 23 common frames omitted
Caused by: org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 16:11:29.527 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - Starting CloudpayMchApplication v1.13.0 using Java 17.0.14 on windfred with PID 27792 (C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target\cloudpay-merchant.jar started by winfred in C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target)
2025-07-18 16:11:29.533 DEBUG [main] [c.k.c.m.b.CloudpayMchApplication] - Running with Spring Boot v2.4.8, Spring v5.3.8
2025-07-18 16:11:29.534 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - No active profile set, falling back to default profiles: default
2025-07-18 16:11:34.067 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-18 16:11:34.079 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-18 16:11:34.158 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-07-18 16:11:34.680 INFO  [main] [o.s.c.a.ConfigurationClassPostProcessor] - Cannot enhance @Configuration bean definition 'rabbitMQBeanProcessor' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-18 16:11:36.473 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:11:36.480 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7283a783] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:11:36.545 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:11:36.644 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@3f19b8b3' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:11:36.676 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:11:37.884 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat initialized with port(s): 9218 (http)
2025-07-18 16:11:37.909 INFO  [main] [o.a.c.h.Http11NioProtocol] - Initializing ProtocolHandler ["http-nio-9218"]
2025-07-18 16:11:37.911 INFO  [main] [o.a.c.c.StandardService] - Starting service [Tomcat]
2025-07-18 16:11:37.911 INFO  [main] [o.a.c.c.StandardEngine] - Starting Servlet engine: [Apache Tomcat/9.0.48]
2025-07-18 16:11:38.082 INFO  [main] [o.a.c.c.C.[.[.[/]] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:11:38.083 INFO  [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] - Root WebApplicationContext: initialization completed in 8362 ms
2025-07-18 16:11:38.776 INFO  [main] [c.a.d.s.b.a.DruidDataSourceAutoConfigure] - Init DruidDataSource
2025-07-18 16:11:39.899 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} inited
2025-07-18 16:11:41.427 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysUserRoleRela".
2025-07-18 16:11:42.370 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.MchAccountChange".
2025-07-18 16:11:42.681 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysRoleEntRela".
2025-07-18 16:11:45.406 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.AgentInfoVo".
2025-07-18 16:11:47.261 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.InterfaceBankdataInfo".
2025-07-18 16:11:47.321 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.JiaoyiTotleVo".
2025-07-18 16:11:48.063 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysInterfaceEntCountry".
2025-07-18 16:11:48.119 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysuserEntCountry".
2025-07-18 16:11:50.061 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/', GET] with []
2025-07-18 16:11:50.061 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.html', GET] with []
2025-07-18 16:11:50.061 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/favicon.ico', GET] with []
2025-07-18 16:11:50.062 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.html', GET] with []
2025-07-18 16:11:50.062 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.css', GET] with []
2025-07-18 16:11:50.062 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.js', GET] with []
2025-07-18 16:11:50.062 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.png', GET] with []
2025-07-18 16:11:50.064 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpg', GET] with []
2025-07-18 16:11:50.064 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpeg', GET] with []
2025-07-18 16:11:50.065 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.svg', GET] with []
2025-07-18 16:11:50.065 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.ico', GET] with []
2025-07-18 16:11:50.065 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.webp', GET] with []
2025-07-18 16:11:50.066 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.txt', GET] with []
2025-07-18 16:11:50.066 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.xls', GET] with []
2025-07-18 16:11:50.066 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.mp4', GET] with []
2025-07-18 16:11:50.066 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/api/anon/**'] with []
2025-07-18 16:11:50.131 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5820070c, org.springframework.security.web.context.SecurityContextPersistenceFilter@9f86dc3, org.springframework.security.web.header.HeaderWriterFilter@2fbe26da, org.springframework.web.filter.CorsFilter@51d9b06c, org.springframework.security.web.authentication.logout.LogoutFilter@67bf0480, com.king.cloudpay.mch.secruity.JeeAuthenticationTokenFilter@34208baa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@662e682a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e62ead7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@71370fec, org.springframework.security.web.session.SessionManagementFilter@64387c17, org.springframework.security.web.access.ExceptionTranslationFilter@17fede14, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@53d2d002]
2025-07-18 16:11:51.453 INFO  [main] [o.s.b.a.w.s.WelcomePageHandlerMapping] - Adding welcome page: class path resource [static/index.html]
2025-07-18 16:11:53.006 INFO  [main] [o.w.s.a.Web3jAutoConfiguration] - Building service for endpoint: null
2025-07-18 16:11:53.383 INFO  [main] [o.a.c.h.Http11NioProtocol] - Starting ProtocolHandler ["http-nio-9218"]
2025-07-18 16:11:53.459 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat started on port(s): 9218 (http) with context path ''
2025-07-18 16:11:53.465 INFO  [main] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 16:11:53.586 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 16:11:53.588 INFO  [main] [o.s.a.r.l.SimpleMessageListenerContainer] - Broker not available; cannot force queue declarations during start: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
2025-07-18 16:11:53.614 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 16:11:53.624 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 16:11:53.630 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2167)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2140)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2120)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1912)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1893)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1347)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 12 common frames omitted
2025-07-18 16:11:53.638 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 16:11:53.646 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 16:11:53.646 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Consumer received fatal exception on startup
org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 16:11:53.647 WARN  [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
2025-07-18 16:11:53.651 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Stopping container from aborted consumer
2025-07-18 16:11:53.653 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Waiting for workers to finish.
2025-07-18 16:11:53.653 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Successfully waited for workers to finish.
2025-07-18 16:11:53.691 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closing ...
2025-07-18 16:11:53.707 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closed
2025-07-18 16:11:53.739 INFO  [main] [o.a.c.h.Http11NioProtocol] - Pausing ProtocolHandler ["http-nio-9218"]
2025-07-18 16:11:53.740 INFO  [main] [o.a.c.c.StandardService] - Stopping service [Tomcat]
2025-07-18 16:11:53.762 INFO  [main] [o.a.c.h.Http11NioProtocol] - Stopping ProtocolHandler ["http-nio-9218"]
2025-07-18 16:11:53.769 INFO  [main] [o.a.c.h.Http11NioProtocol] - Destroying ProtocolHandler ["http-nio-9218"]
2025-07-18 16:11:53.789 INFO  [main] [o.s.b.a.l.ConditionEvaluationReportLoggingListener] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 16:11:53.866 ERROR [main] [o.s.b.SpringApplication] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:181)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.king.cloudpay.mch.bootstrap.CloudpayMchApplication.main(CloudpayMchApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
Caused by: org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.waitForConsumersToStart(SimpleMessageListenerContainer.java:601)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doStart(SimpleMessageListenerContainer.java:562)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.start(AbstractMessageListenerContainer.java:1376)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.startIfNecessary(RabbitListenerEndpointRegistry.java:299)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.start(RabbitListenerEndpointRegistry.java:249)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	... 23 common frames omitted
Caused by: org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 16:22:03.286 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - Starting CloudpayMchApplication v1.13.0 using Java 17.0.14 on windfred with PID 7708 (C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target\cloudpay-merchant.jar started by winfred in C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target)
2025-07-18 16:22:03.293 DEBUG [main] [c.k.c.m.b.CloudpayMchApplication] - Running with Spring Boot v2.4.8, Spring v5.3.8
2025-07-18 16:22:03.294 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - No active profile set, falling back to default profiles: default
2025-07-18 16:22:07.437 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-18 16:22:07.447 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-18 16:22:07.527 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-07-18 16:22:07.957 INFO  [main] [o.s.c.a.ConfigurationClassPostProcessor] - Cannot enhance @Configuration bean definition 'rabbitMQBeanProcessor' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-18 16:22:09.267 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:22:09.274 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7283a783] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:22:09.325 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:22:09.378 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@3f19b8b3' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:22:09.396 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 16:22:10.301 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat initialized with port(s): 9218 (http)
2025-07-18 16:22:10.329 INFO  [main] [o.a.c.h.Http11NioProtocol] - Initializing ProtocolHandler ["http-nio-9218"]
2025-07-18 16:22:10.330 INFO  [main] [o.a.c.c.StandardService] - Starting service [Tomcat]
2025-07-18 16:22:10.331 INFO  [main] [o.a.c.c.StandardEngine] - Starting Servlet engine: [Apache Tomcat/9.0.48]
2025-07-18 16:22:10.485 INFO  [main] [o.a.c.c.C.[.[.[/]] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:22:10.485 INFO  [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] - Root WebApplicationContext: initialization completed in 6978 ms
2025-07-18 16:22:11.041 INFO  [main] [c.a.d.s.b.a.DruidDataSourceAutoConfigure] - Init DruidDataSource
2025-07-18 16:22:11.934 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} inited
2025-07-18 16:22:12.884 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysUserRoleRela".
2025-07-18 16:22:13.748 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.MchAccountChange".
2025-07-18 16:22:13.992 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysRoleEntRela".
2025-07-18 16:22:15.915 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.AgentInfoVo".
2025-07-18 16:22:17.277 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.InterfaceBankdataInfo".
2025-07-18 16:22:17.328 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.JiaoyiTotleVo".
2025-07-18 16:22:17.957 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysInterfaceEntCountry".
2025-07-18 16:22:17.991 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysuserEntCountry".
2025-07-18 16:22:19.485 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/', GET] with []
2025-07-18 16:22:19.486 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.html', GET] with []
2025-07-18 16:22:19.486 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/favicon.ico', GET] with []
2025-07-18 16:22:19.486 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.html', GET] with []
2025-07-18 16:22:19.487 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.css', GET] with []
2025-07-18 16:22:19.487 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.js', GET] with []
2025-07-18 16:22:19.487 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.png', GET] with []
2025-07-18 16:22:19.488 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpg', GET] with []
2025-07-18 16:22:19.488 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpeg', GET] with []
2025-07-18 16:22:19.488 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.svg', GET] with []
2025-07-18 16:22:19.488 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.ico', GET] with []
2025-07-18 16:22:19.488 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.webp', GET] with []
2025-07-18 16:22:19.488 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.txt', GET] with []
2025-07-18 16:22:19.490 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.xls', GET] with []
2025-07-18 16:22:19.490 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.mp4', GET] with []
2025-07-18 16:22:19.491 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/api/anon/**'] with []
2025-07-18 16:22:19.571 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@557b6a37, org.springframework.security.web.context.SecurityContextPersistenceFilter@6963b88c, org.springframework.security.web.header.HeaderWriterFilter@1e7f19b4, org.springframework.web.filter.CorsFilter@68577ba8, org.springframework.security.web.authentication.logout.LogoutFilter@2fd72332, com.king.cloudpay.mch.secruity.JeeAuthenticationTokenFilter@11b32a14, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54e43bfe, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c369270, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c33e528, org.springframework.security.web.session.SessionManagementFilter@2fbe26da, org.springframework.security.web.access.ExceptionTranslationFilter@10e5bf9c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@718f805a]
2025-07-18 16:22:21.046 INFO  [main] [o.s.b.a.w.s.WelcomePageHandlerMapping] - Adding welcome page: class path resource [static/index.html]
2025-07-18 16:22:22.895 INFO  [main] [o.w.s.a.Web3jAutoConfiguration] - Building service for endpoint: null
2025-07-18 16:22:23.108 INFO  [main] [o.a.c.h.Http11NioProtocol] - Starting ProtocolHandler ["http-nio-9218"]
2025-07-18 16:22:23.171 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat started on port(s): 9218 (http) with context path ''
2025-07-18 16:22:23.173 INFO  [main] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 16:22:23.242 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Connection reset)
2025-07-18 16:22:23.243 INFO  [main] [o.s.a.r.l.SimpleMessageListenerContainer] - Broker not available; cannot force queue declarations during start: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
2025-07-18 16:22:23.261 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 16:22:23.270 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 16:22:23.275 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2167)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2140)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2120)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1912)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1893)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1347)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 12 common frames omitted
2025-07-18 16:22:23.284 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 16:22:23.293 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 16:22:23.293 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Consumer received fatal exception on startup
org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 16:22:23.296 WARN  [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
2025-07-18 16:22:23.298 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Stopping container from aborted consumer
2025-07-18 16:22:23.299 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Waiting for workers to finish.
2025-07-18 16:22:23.299 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Successfully waited for workers to finish.
2025-07-18 16:22:23.333 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closing ...
2025-07-18 16:22:23.347 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closed
2025-07-18 16:22:23.386 INFO  [main] [o.a.c.h.Http11NioProtocol] - Pausing ProtocolHandler ["http-nio-9218"]
2025-07-18 16:22:23.386 INFO  [main] [o.a.c.c.StandardService] - Stopping service [Tomcat]
2025-07-18 16:22:23.394 INFO  [main] [o.a.c.h.Http11NioProtocol] - Stopping ProtocolHandler ["http-nio-9218"]
2025-07-18 16:22:23.396 INFO  [main] [o.a.c.h.Http11NioProtocol] - Destroying ProtocolHandler ["http-nio-9218"]
2025-07-18 16:22:23.408 INFO  [main] [o.s.b.a.l.ConditionEvaluationReportLoggingListener] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 16:22:23.459 ERROR [main] [o.s.b.SpringApplication] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:181)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.king.cloudpay.mch.bootstrap.CloudpayMchApplication.main(CloudpayMchApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
Caused by: org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.waitForConsumersToStart(SimpleMessageListenerContainer.java:601)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doStart(SimpleMessageListenerContainer.java:562)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.start(AbstractMessageListenerContainer.java:1376)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.startIfNecessary(RabbitListenerEndpointRegistry.java:299)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.start(RabbitListenerEndpointRegistry.java:249)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	... 23 common frames omitted
Caused by: org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 22:16:59.254 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - Starting CloudpayMchApplication v1.13.0 using Java 17.0.14 on windfred with PID 39784 (C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target\cloudpay-merchant.jar started by winfred in C:\Users\<USER>\Downloads\cloudpay-2\cloudpay-2\cloudpay-merchant\target)
2025-07-18 22:16:59.261 DEBUG [main] [c.k.c.m.b.CloudpayMchApplication] - Running with Spring Boot v2.4.8, Spring v5.3.8
2025-07-18 22:16:59.262 INFO  [main] [c.k.c.m.b.CloudpayMchApplication] - No active profile set, falling back to default profiles: default
2025-07-18 22:17:05.377 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-18 22:17:05.391 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-18 22:17:05.637 INFO  [main] [o.s.d.r.c.RepositoryConfigurationDelegate] - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-18 22:17:06.484 INFO  [main] [o.s.c.a.ConfigurationClassPostProcessor] - Cannot enhance @Configuration bean definition 'rabbitMQBeanProcessor' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-18 22:17:09.132 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 22:17:09.150 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7283a783] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 22:17:09.252 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 22:17:09.397 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@372ea2bc' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 22:17:09.427 INFO  [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-18 22:17:11.412 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat initialized with port(s): 9218 (http)
2025-07-18 22:17:11.481 INFO  [main] [o.a.c.h.Http11NioProtocol] - Initializing ProtocolHandler ["http-nio-9218"]
2025-07-18 22:17:11.483 INFO  [main] [o.a.c.c.StandardService] - Starting service [Tomcat]
2025-07-18 22:17:11.484 INFO  [main] [o.a.c.c.StandardEngine] - Starting Servlet engine: [Apache Tomcat/9.0.48]
2025-07-18 22:17:11.750 INFO  [main] [o.a.c.c.C.[.[.[/]] - Initializing Spring embedded WebApplicationContext
2025-07-18 22:17:11.752 INFO  [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] - Root WebApplicationContext: initialization completed in 12248 ms
2025-07-18 22:17:12.826 INFO  [main] [c.a.d.s.b.a.DruidDataSourceAutoConfigure] - Init DruidDataSource
2025-07-18 22:17:14.734 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} inited
2025-07-18 22:17:18.782 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysUserRoleRela".
2025-07-18 22:17:20.558 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.MchAccountChange".
2025-07-18 22:17:21.483 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysRoleEntRela".
2025-07-18 22:17:26.215 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.AgentInfoVo".
2025-07-18 22:17:29.556 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.InterfaceBankdataInfo".
2025-07-18 22:17:29.664 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.vo.JiaoyiTotleVo".
2025-07-18 22:17:30.852 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysInterfaceEntCountry".
2025-07-18 22:17:30.892 WARN  [main] [c.b.m.c.m.TableInfoHelper] - Can not find table primary key in Class: "com.king.cloudpay.core.entity.SysuserEntCountry".
2025-07-18 22:17:32.757 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/', GET] with []
2025-07-18 22:17:32.765 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.html', GET] with []
2025-07-18 22:17:32.773 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/favicon.ico', GET] with []
2025-07-18 22:17:32.781 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.html', GET] with []
2025-07-18 22:17:32.790 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.css', GET] with []
2025-07-18 22:17:32.793 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.js', GET] with []
2025-07-18 22:17:32.795 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.png', GET] with []
2025-07-18 22:17:32.796 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpg', GET] with []
2025-07-18 22:17:32.798 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.jpeg', GET] with []
2025-07-18 22:17:32.802 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.svg', GET] with []
2025-07-18 22:17:32.805 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.ico', GET] with []
2025-07-18 22:17:32.806 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.webp', GET] with []
2025-07-18 22:17:32.807 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/*.txt', GET] with []
2025-07-18 22:17:32.808 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.xls', GET] with []
2025-07-18 22:17:32.810 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/**/*.mp4', GET] with []
2025-07-18 22:17:32.812 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure Ant [pattern='/api/anon/**'] with []
2025-07-18 22:17:32.992 INFO  [main] [o.s.s.w.DefaultSecurityFilterChain] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34be065a, org.springframework.security.web.context.SecurityContextPersistenceFilter@86d6bf7, org.springframework.security.web.header.HeaderWriterFilter@10e5bf9c, org.springframework.web.filter.CorsFilter@51d9b06c, org.springframework.security.web.authentication.logout.LogoutFilter@3b98b809, com.king.cloudpay.mch.secruity.JeeAuthenticationTokenFilter@718f805a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4ed15347, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@54e43bfe, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e09f1b6, org.springframework.security.web.session.SessionManagementFilter@17fede14, org.springframework.security.web.access.ExceptionTranslationFilter@161dd92a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@250b5e5b]
2025-07-18 22:17:41.204 INFO  [main] [o.s.b.a.w.s.WelcomePageHandlerMapping] - Adding welcome page: class path resource [static/index.html]
2025-07-18 22:17:47.878 INFO  [main] [o.w.s.a.Web3jAutoConfiguration] - Building service for endpoint: null
2025-07-18 22:17:48.707 INFO  [main] [o.a.c.h.Http11NioProtocol] - Starting ProtocolHandler ["http-nio-9218"]
2025-07-18 22:17:48.856 INFO  [main] [o.s.b.w.e.t.TomcatWebServer] - Tomcat started on port(s): 9218 (http) with context path ''
2025-07-18 22:17:48.863 INFO  [main] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 22:17:49.057 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 22:17:49.061 INFO  [main] [o.s.a.r.l.SimpleMessageListenerContainer] - Broker not available; cannot force queue declarations during start: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
2025-07-18 22:17:49.110 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 22:17:49.124 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 22:17:49.137 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Failed to check/redeclare auto-delete queue(s).
org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.doExecute(RabbitTemplate.java:2167)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2140)
	at org.springframework.amqp.rabbit.core.RabbitTemplate.execute(RabbitTemplate.java:2120)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueInfo(RabbitAdmin.java:407)
	at org.springframework.amqp.rabbit.core.RabbitAdmin.getQueueProperties(RabbitAdmin.java:391)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.attemptDeclarations(AbstractMessageListenerContainer.java:1912)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.redeclareElementsIfNecessary(AbstractMessageListenerContainer.java:1893)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1347)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 12 common frames omitted
2025-07-18 22:17:49.158 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.c.CachingConnectionFactory] - Attempting to connect to: [127.0.0.1:5672]
2025-07-18 22:17:49.180 WARN  [AMQP Connection 127.0.0.1:5672] [c.r.c.i.ForgivingExceptionHandler] - An unexpected connection driver error occured (Exception message: Socket closed)
2025-07-18 22:17:49.182 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Consumer received fatal exception on startup
org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
2025-07-18 22:17:49.191 WARN  [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
2025-07-18 22:17:49.196 ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Stopping container from aborted consumer
2025-07-18 22:17:49.207 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Waiting for workers to finish.
2025-07-18 22:17:49.208 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] [o.s.a.r.l.SimpleMessageListenerContainer] - Successfully waited for workers to finish.
2025-07-18 22:17:49.328 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closing ...
2025-07-18 22:17:49.355 INFO  [main] [c.a.d.p.DruidDataSource] - {dataSource-1} closed
2025-07-18 22:17:49.509 INFO  [main] [o.a.c.h.Http11NioProtocol] - Pausing ProtocolHandler ["http-nio-9218"]
2025-07-18 22:17:49.510 INFO  [main] [o.a.c.c.StandardService] - Stopping service [Tomcat]
2025-07-18 22:17:49.550 INFO  [main] [o.a.c.h.Http11NioProtocol] - Stopping ProtocolHandler ["http-nio-9218"]
2025-07-18 22:17:49.560 INFO  [main] [o.a.c.h.Http11NioProtocol] - Destroying ProtocolHandler ["http-nio-9218"]
2025-07-18 22:17:49.611 INFO  [main] [o.s.b.a.l.ConditionEvaluationReportLoggingListener] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-18 22:17:49.722 ERROR [main] [o.s.b.SpringApplication] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'; nested exception is org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:181)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:935)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:586)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.king.cloudpay.mch.bootstrap.CloudpayMchApplication.main(CloudpayMchApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
Caused by: org.springframework.amqp.AmqpIllegalStateException: Fatal exception on listener startup
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.waitForConsumersToStart(SimpleMessageListenerContainer.java:601)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doStart(SimpleMessageListenerContainer.java:562)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.start(AbstractMessageListenerContainer.java:1376)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.startIfNecessary(RabbitListenerEndpointRegistry.java:299)
	at org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry.start(RabbitListenerEndpointRegistry.java:249)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	... 23 common frames omitted
Caused by: org.springframework.amqp.rabbit.listener.exception.FatalListenerStartupException: Authentication failure
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:602)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.initialize(SimpleMessageListenerContainer.java:1348)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1193)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.springframework.amqp.AmqpAuthenticationException: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at org.springframework.amqp.rabbit.support.RabbitExceptionTranslator.convertRabbitAccessException(RabbitExceptionTranslator.java:64)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:602)
	at org.springframework.amqp.rabbit.connection.CachingConnectionFactory.createConnection(CachingConnectionFactory.java:724)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.createConnection(ConnectionFactoryUtils.java:216)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils$RabbitResourceFactory.createConnection(ConnectionFactoryUtils.java:295)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.doGetTransactionalResourceHolder(ConnectionFactoryUtils.java:130)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:92)
	at org.springframework.amqp.rabbit.connection.ConnectionFactoryUtils.getTransactionalResourceHolder(ConnectionFactoryUtils.java:75)
	at org.springframework.amqp.rabbit.listener.BlockingQueueConsumer.start(BlockingQueueConsumer.java:596)
	... 3 common frames omitted
Caused by: com.rabbitmq.client.AuthenticationFailureException: ACCESS_REFUSED - Login was refused using authentication mechanism PLAIN. For details see the broker logfile.
	at com.rabbitmq.client.impl.AMQConnection.start(AMQConnection.java:385)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1139)
	at com.rabbitmq.client.ConnectionFactory.newConnection(ConnectionFactory.java:1087)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connectAddresses(AbstractConnectionFactory.java:640)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.connect(AbstractConnectionFactory.java:615)
	at org.springframework.amqp.rabbit.connection.AbstractConnectionFactory.createBareConnection(AbstractConnectionFactory.java:565)
	... 10 common frames omitted
