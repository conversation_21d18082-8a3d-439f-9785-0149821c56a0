package com.king.cloudpay.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.king.cloudpay.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商户信息表
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_mch_info")
public class MchInfo extends BaseModel<MchInfo> {

    //gw
    public static final LambdaQueryWrapper<MchInfo> gw() {
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    public static final byte TYPE_NORMAL = 1; //商户类型： 1-普通商户
    public static final byte TYPE_ISVSUB = 2; //商户类型： 2-特约商户

    // 支付订单交易总额    单位：分
    @TableField(exist = false)
    private String pOrderSum;
    // 转账订单交易总额    单位：分
    @TableField(exist = false)
    private String tOrderSun;
    // 退款订单交易总额    单位：分
    @TableField(exist = false)
    private String rOrderSum;

    // 绑定了哪些卡商卡商
    @TableField(exist = false)
    private String cardNo;

    /**
     * 商户号
     */
    @TableId(value = "mch_no", type = IdType.INPUT)
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户简称
     */
    private String mchShortName;

    /**
     * 类型: 1-普通商户, 2-特约商户(服务商模式)
     */
    private Byte type;

    /**
     * 代理商号 t_sys_user
     */
    private Long agentNo;

    /**
     * 服务商号
     */
    private String isvNo;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactTel;

    /**
     * google 验证码随机密钥
     */
    private String googlePrivate;

    /**
     * google 验证码是否绑定 0 未绑定 1 已绑定
     */
    private Byte googleState;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 商户状态: 0-停用, 1-正常
     */
    private Byte state;

    /**
     * 商户备注
     */
    private String remark;

    /**
     * 初始用户ID（创建商户时，允许商户登录的用户）
     */
    private Long initUserId;

    /**
     * 创建者用户ID
     */
    private Long createdUid;

    /**
     * 创建者姓名
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;


    /*-------------------------------商户费率设置------------------------------------*/
    /**
     * 支付费率类型 0 百分比 1 单笔
     */
    private Byte rateType;

    /**
     * 支付费率值
     */
    private BigDecimal raeTypeExchange;

    /**
     * 转账费率类型 0 百分比 1 单笔
     */
    private Byte tranAccountsType;

    /**
     * 转账费率值
     */
    private BigDecimal tranAccountsExchange;


    /*-------------------------------代理商费率设置------------------------------------*/
    /**
     * 支付费率类型 0 百分比 1 单笔
     */
    private Byte agentRateType;

    /**
     * 支付费率值
     */
    private BigDecimal agentRaeTypeExchange;

    /**
     * 转账费率类型 0 百分比 1 单笔
     */
    private Byte agentTranAccountsType;

    /**
     * 转账费率值
     */
    private BigDecimal agentTranAccountsExchange;


    /*-------------------------------单独设置这个商户可以代收多少，代付多少额度------------------------------------*/
    /**
     * 代收额度 元
     */
    private BigDecimal daishouLimit;

    /**
     * 代付额度 元
     */
    private BigDecimal daifuLimit;

    /**
     * ip白名单 英文,分割
     */
    private String whiteIp;

    // Manual getter/setter methods (in case Lombok doesn't work properly)
    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getGoogleState() {
        return googleState;
    }

    public void setGoogleState(Byte googleState) {
        this.googleState = googleState;
    }

    public String getGooglePrivate() {
        return googlePrivate;
    }

    public void setGooglePrivate(String googlePrivate) {
        this.googlePrivate = googlePrivate;
    }

    /*-------------------------------给商户绑定结算配置------------------------------------*/
    private Long settlementId;

    /**
     * 结算配置 实体类
     */
    @TableField(exist = false)
    private Settlement settlement;
}
