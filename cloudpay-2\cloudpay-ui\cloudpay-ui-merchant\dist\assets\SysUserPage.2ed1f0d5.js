import{r as S,A as U,i as Z,j as W,k as X,l as J}from"./manage.2dfb5a24.js";import{e as N,g as q,f as $,r as z,o as i,C as f,w as t,b as u,d,E as m,a as E,c as H,G as Q,H as ee,i as se,I as ue,j as te,l as ae,R as ne,k as le,n as oe,U as re,W as de,X as Y,q as ie,B as M,F as G,V as K,Y as pe,M as A,m as I,Z as fe,$ as ce}from"./index.fba97cfa.js";const _e={style:{display:"flex","flex-direction":"row"}},me={key:1},Fe={class:"drawer-btn-center"},we=N({__name:"AddOrEdit",props:{callbackFunc:{type:Function,default:()=>({})}},setup(R,{expose:x}){const{$infoBox:v,$access:b}=q().appContext.config.globalProperties,a=R,w=$(),e=z({newPwd:"",resetIsShow:!1,sysPassword:{resetPass:!1,defaultPass:!0,confirmPwd:""},loading:!1,value:1,confirmLoading:!1,isAdd:!0,isShow:!1,saveObject:{},recordId:null,rules:{realname:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u59D3\u540D",trigger:"blur"}],telphone:[{required:!0,pattern:/^[1][0-9]{10}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}],userNo:[{required:!0,message:"\u8BF7\u8F93\u5165\u7F16\u53F7",trigger:"blur"}],loginUsername:[],newPwd:[{required:!1,trigger:"blur"},{validator:(p,s)=>!e.sysPassword.defaultPass&&(e.newPwd.length<6||e.newPwd.length>12)?Promise.reject("\u8BF7\u8F93\u51656-12\u4F4D\u65B0\u5BC6\u7801"):Promise.resolve()}],confirmPwd:[{required:!1,trigger:"blur"},{validator:(p,s)=>!e.sysPassword.defaultPass&&e.newPwd===e.sysPassword.confirmPwd?Promise.resolve():Promise.reject("\u65B0\u5BC6\u7801\u4E0E\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4")}]}});function D(p){w.value&&w.value.resetFields(),e.isAdd=!p,e.saveObject={state:1,sex:1},e.rules.loginUsername=[],e.confirmLoading=!1,e.isAdd&&e.rules.loginUsername.push({required:!0,pattern:/^[a-zA-Z][a-zA-Z0-9]{5,17}$/,message:"\u8BF7\u8F93\u5165\u5B57\u6BCD\u5F00\u5934\uFF0C\u957F\u5EA6\u4E3A6-18\u4F4D\u7684\u767B\u5F55\u540D",trigger:"blur"}),e.isAdd||(e.resetIsShow=!0,e.recordId=p,S.getById(U,p).then(s=>{e.saveObject=s})),e.isShow=!0}function c(){w.value.validate().then(p=>{p&&(e.loading=!0,e.confirmLoading=!0,e.isAdd?S.add(U,e.saveObject).then(s=>{v.message.success("\u65B0\u589E\u6210\u529F"),e.isShow=!1,e.loading=!1,a.callbackFunc()}).catch(s=>{e.confirmLoading=!1}):(e.sysPassword.confirmPwd=se.encode(e.sysPassword.confirmPwd),Object.assign(e.saveObject,e.sysPassword),S.updateById(U,e.recordId,e.saveObject).then(s=>{v.message.success("\u4FEE\u6539\u6210\u529F"),e.isShow=!1,a.callbackFunc(),e.resetIsShow=!1,e.sysPassword.resetPass=!1,e.sysPassword.defaultPass=!0,k()}).catch(s=>{e.confirmLoading=!1,e.resetIsShow=!1,e.sysPassword.resetPass=!1,e.sysPassword.defaultPass=!0,k()})))})}function l(){e.isShow=!1,e.resetIsShow=!1,k(),e.sysPassword.resetPass=!1,e.sysPassword.defaultPass=!0}function g(){e.sysPassword.defaultPass||(e.newPwd="",e.sysPassword.confirmPwd="")}function k(){e.newPwd="",e.sysPassword.confirmPwd=""}return x({show:D}),(p,s)=>{const _=ue,r=te,n=ae,P=ne,F=le,B=oe,h=re,C=de,L=Y,O=ie,j=M,V=G,y=K;return i(),f(y,{title:e.isAdd?"\u65B0\u589E\u64CD\u4F5C\u5458":"\u4FEE\u6539\u64CD\u4F5C\u5458",placement:"right",closable:!0,onOk:c,open:e.isShow,"onUpdate:open":s[10]||(s[10]=o=>e.isShow=o),width:"600",onClose:l,maskClosable:!1},{default:t(()=>[u(V,{ref_key:"infoFormModel",ref:w,model:e.saveObject,rules:e.rules,style:{"padding-bottom":"50px"},layout:"vertical"},{default:t(()=>[u(B,{justify:"space-between",type:"flex"},{default:t(()=>[u(n,{span:10},{default:t(()=>[u(r,{label:"\u7528\u6237\u767B\u5F55\u540D:",name:"loginUsername"},{default:t(()=>[u(_,{value:e.saveObject.loginUsername,"onUpdate:value":s[0]||(s[0]=o=>e.saveObject.loginUsername=o),disabled:!e.isAdd},null,8,["value","disabled"])]),_:1})]),_:1}),u(n,{span:10},{default:t(()=>[u(r,{label:"\u7528\u6237\u59D3\u540D\uFF1A",name:"realname"},{default:t(()=>[u(_,{value:e.saveObject.realname,"onUpdate:value":s[1]||(s[1]=o=>e.saveObject.realname=o)},null,8,["value"])]),_:1})]),_:1}),u(n,{span:10},{default:t(()=>[u(r,{label:"\u624B\u673A\u53F7\uFF1A",name:"telphone"},{default:t(()=>[u(_,{value:e.saveObject.telphone,"onUpdate:value":s[2]||(s[2]=o=>e.saveObject.telphone=o)},null,8,["value"])]),_:1})]),_:1}),u(n,{span:10},{default:t(()=>[u(r,{label:"\u7F16\u53F7\uFF1A",name:"userNo"},{default:t(()=>[u(_,{value:e.saveObject.userNo,"onUpdate:value":s[3]||(s[3]=o=>e.saveObject.userNo=o)},null,8,["value"])]),_:1})]),_:1}),u(n,{span:10},{default:t(()=>[u(r,{label:"\u8BF7\u9009\u62E9\u6027\u522B\uFF1A",name:"sex"},{default:t(()=>[u(F,{value:e.saveObject.sex,"onUpdate:value":s[4]||(s[4]=o=>e.saveObject.sex=o)},{default:t(()=>[u(P,{value:1},{default:t(()=>s[11]||(s[11]=[d("\u7537")])),_:1}),u(P,{value:2},{default:t(()=>s[12]||(s[12]=[d("\u5973")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),u(n,{span:10},{default:t(()=>[u(r,{label:"\u72B6\u6001\uFF1A",name:"state"},{default:t(()=>[u(F,{value:e.saveObject.state,"onUpdate:value":s[5]||(s[5]=o=>e.saveObject.state=o)},{default:t(()=>[u(P,{value:1},{default:t(()=>s[13]||(s[13]=[d("\u542F\u7528")])),_:1}),u(P,{value:0},{default:t(()=>s[14]||(s[14]=[d("\u505C\u7528")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e.resetIsShow?(i(),f(C,{key:0,orientation:"left"},{default:t(()=>[u(h,{color:"#FF4B33"},{default:t(()=>s[15]||(s[15]=[d("\u8D26\u6237\u5B89\u5168")])),_:1})]),_:1})):m("",!0),E("div",_e,[u(B,{justify:"space-between",type:"flex",style:{width:"100%"}},{default:t(()=>[u(n,{span:10},{default:t(()=>[e.resetIsShow?(i(),f(r,{key:0,label:""},{default:t(()=>[s[16]||(s[16]=d(" \u91CD\u7F6E\u5BC6\u7801\uFF1A ")),u(L,{checked:e.sysPassword.resetPass,"onUpdate:checked":s[6]||(s[6]=o=>e.sysPassword.resetPass=o)},null,8,["checked"])]),_:1})):m("",!0)]),_:1}),u(n,{span:10},{default:t(()=>[e.sysPassword.resetPass?(i(),f(r,{key:0,label:""},{default:t(()=>[s[17]||(s[17]=d(" \u6062\u590D\u9ED8\u8BA4\u5BC6\u7801\uFF1A ")),u(L,{checked:e.sysPassword.defaultPass,"onUpdate:checked":s[7]||(s[7]=o=>e.sysPassword.defaultPass=o),onClick:g},null,8,["checked"])]),_:1})):m("",!0)]),_:1})]),_:1})]),e.sysPassword.resetPass?(i(),H("div",me,[Q(E("div",null,[u(B,{justify:"space-between",type:"flex"},{default:t(()=>[u(n,{span:10},{default:t(()=>[u(r,{label:"\u65B0\u5BC6\u7801\uFF1A",name:"newPwd"},{default:t(()=>[u(O,{autocomplete:"new-password",value:e.newPwd,"onUpdate:value":s[8]||(s[8]=o=>e.newPwd=o),disabled:e.sysPassword.defaultPass},null,8,["value","disabled"])]),_:1})]),_:1}),u(n,{span:10},{default:t(()=>[u(r,{label:"\u786E\u8BA4\u65B0\u5BC6\u7801\uFF1A",name:"confirmPwd"},{default:t(()=>[u(O,{autocomplete:"new-password",value:e.sysPassword.confirmPwd,"onUpdate:value":s[9]||(s[9]=o=>e.sysPassword.confirmPwd=o),disabled:e.sysPassword.defaultPass},null,8,["value","disabled"])]),_:1})]),_:1})]),_:1})],512),[[ee,!e.sysPassword.defaultPass]])])):m("",!0),E("div",Fe,[u(j,{style:{marginRight:"8px"},onClick:l},{default:t(()=>s[18]||(s[18]=[d("\u53D6\u6D88")])),_:1}),u(j,{type:"primary",onClick:c,loading:e.confirmLoading},{default:t(()=>s[19]||(s[19]=[d(" \u4FDD\u5B58 ")])),_:1},8,["loading"])])]),_:1},8,["model","rules"])]),_:1},8,["title","open"])}}}),ge={style:{borderBottom:"1px solid #E9E9E9"}},ye={class:"drawer-btn-center"},ve=N({__name:"RoleDist",props:{callbackFunc:{type:Function,default:()=>{}}},setup(R,{expose:x}){const{$infoBox:v}=q().appContext.config.globalProperties,b=R,a=z({confirmLoading:!1,isShow:!1,recordId:null,allRoleList:[],checkedVal:[]});function w(c){a.allRoleList=[],a.checkedVal=[],a.confirmLoading=!1,a.recordId=c,Z.list(W,{pageSize:-1}).then(l=>{if(l.total<=0)return v.message.error("\u5F53\u524D\u6682\u65E0\u89D2\u8272\uFF0C\u8BF7\u5148\u884C\u6DFB\u52A0");a.allRoleList=[],l.records.map(g=>{a.allRoleList.push({label:g.roleName,value:g.roleId}),a.isShow=!0}),S.list(X,{pageSize:-1,userId:c}).then(g=>{g.records.map(k=>{a.checkedVal.push(k.roleId)})})})}function e(){a.confirmLoading=!0,J(a.recordId,a.checkedVal).then(c=>{v.message.success("\u66F4\u65B0\u6210\u529F\uFF01"),a.isShow=!1,a.callbackFunc!==void 0&&b.callbackFunc()}).catch(c=>{a.confirmLoading=!1})}function D(c){a.checkedVal=[],c.target.checked&&a.allRoleList.map(l=>{a.checkedVal.push(l.value)})}return x({show:w}),(c,l)=>{const g=Y,k=pe,p=M,s=K;return i(),f(s,{open:a.isShow,"onUpdate:open":l[2]||(l[2]=_=>a.isShow=_),title:"\u5206\u914D\u89D2\u8272",width:"30%",maskClosable:!1,onClose:l[3]||(l[3]=_=>a.isShow=!1)},{default:t(()=>[E("div",null,[E("div",ge,[u(g,{indeterminate:a.checkedVal.length&&a.allRoleList.length!=a.checkedVal.length,checked:a.checkedVal.length&&a.allRoleList.length===a.checkedVal.length,onChange:D},{default:t(()=>l[4]||(l[4]=[d(" \u5168\u9009 ")])),_:1},8,["indeterminate","checked"])]),l[5]||(l[5]=E("br",null,null,-1)),u(k,{value:a.checkedVal,"onUpdate:value":l[0]||(l[0]=_=>a.checkedVal=_),options:a.allRoleList},null,8,["value","options"])]),E("div",ye,[u(p,{style:{marginRight:"8px"},onClick:l[1]||(l[1]=_=>a.isShow=!1)},{default:t(()=>l[6]||(l[6]=[d("\u53D6\u6D88")])),_:1}),u(p,{type:"primary",onClick:e,loading:a.confirmLoading},{default:t(()=>l[7]||(l[7]=[d("\u4FDD\u5B58")])),_:1},8,["loading"])])]),_:1},8,["open"])}}}),be={key:0,class:"table-page-search-wrapper"},ke={class:"table-layer"},Pe={class:"table-page-search-submitButtons"},Ce=N({__name:"SysUserPage",setup(R){const x=[{title:"\u7528\u6237ID",dataIndex:"sysUserId",fixed:"left"},{title:"\u59D3\u540D",dataIndex:"realname"},{title:"\u6027\u522B",dataIndex:"sex",customRender:({record:r})=>r.sex===1?"\u7537":r.sex===2?"\u5973":"\u672A\u77E5"},{title:"\u5934\u50CF",key:"avatar",scopedSlots:{customRender:"avatarSlot"}},{title:"\u7F16\u53F7",dataIndex:"userNo"},{title:"\u624B\u673A\u53F7",dataIndex:"telphone"},{title:"\u8D85\u7BA1",dataIndex:"isAdmin",customRender:({record:r})=>r.isAdmin===1?"\u662F":"\u5426"},{title:"\u72B6\u6001",key:"state",scopedSlots:{customRender:"stateSlot"},align:"center"},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt"},{title:"\u4FEE\u6539\u65F6\u95F4",dataIndex:"updatedAt"},{key:"op",title:"\u64CD\u4F5C",width:"200px",fixed:"right",align:"center",scopedSlots:{customRender:"opSlot"}}],{$infoBox:v,$access:b}=q().appContext.config.globalProperties,a=z({tableColumns:x,searchData:{},btnLoading:!1}),w=$(),e=$(),D=$();function c(r){return S.list(U,r)}function l(){a.btnLoading=!0,w.value.refTable(!0)}function g(){e.value.show()}function k(r){v.confirmDanger("\u786E\u8BA4\u5220\u9664\uFF1F","",()=>S.delById(U,r).then(n=>{v.message.success("\u5220\u9664\u6210\u529F\uFF01"),w.value.refTable(!1)}))}function p(r){e.value.show(r)}function s(r){D.value.show(r)}function _(r,n){const P=n===1?"\u786E\u8BA4[\u542F\u7528]\u8BE5\u7528\u6237\uFF1F":"\u786E\u8BA4[\u505C\u7528]\u8BE5\u7528\u6237\uFF1F",F=n===1?"\u542F\u7528\u540E\u7528\u6237\u53EF\u8FDB\u884C\u767B\u9646\u7B49\u4E00\u7CFB\u5217\u64CD\u4F5C":"\u505C\u7528\u540E\u8BE5\u7528\u6237\u5C06\u7ACB\u5373\u9000\u51FA\u7CFB\u7EDF\u5E76\u4E0D\u53EF\u518D\u6B21\u767B\u9646";return new Promise((B,h)=>{v.confirmDanger(P,F,()=>Z.updateById(U,r,{state:n}).then(C=>{l(),B(C)}).catch(C=>h(C)),()=>{h(new Error)})})}return(r,n)=>{const P=I("cloudpay-text-up"),F=M,B=G,h=fe,C=I("cloudpayTableColState"),L=I("cloudpayTableColumns"),O=I("cloudpayTable"),j=ce,V=I("page-header-wrapper");return i(),f(V,null,{default:t(()=>[u(j,null,{default:t(()=>[A(b)("ENT_UR_USER_SEARCH")?(i(),H("div",be,[u(B,{layout:"inline",class:"table-head-ground"},{default:t(()=>[E("div",ke,[u(P,{placeholder:"\u7528\u6237ID",value:a.searchData.sysUserId,"onUpdate:value":n[0]||(n[0]=y=>a.searchData.sysUserId=y)},null,8,["value"]),u(P,{placeholder:"\u7528\u6237\u59D3\u540D",value:a.searchData.realname,"onUpdate:value":n[1]||(n[1]=y=>a.searchData.realname=y)},null,8,["value"]),E("span",Pe,[u(F,{type:"primary",onClick:l,loading:a.btnLoading},{default:t(()=>n[4]||(n[4]=[d(" \u67E5\u8BE2 ")])),_:1},8,["loading"]),u(F,{style:{"margin-left":"8px"},onClick:n[2]||(n[2]=()=>a.searchData={})},{default:t(()=>n[5]||(n[5]=[d(" \u91CD\u7F6E ")])),_:1})])])]),_:1})])):m("",!0),u(O,{onBtnLoadClose:n[3]||(n[3]=y=>a.btnLoading=!1),ref_key:"infoTable",ref:w,initData:!0,reqTableDataFunc:c,tableColumns:a.tableColumns,searchData:a.searchData,rowKey:"sysUserId"},{opRow:t(()=>[A(b)("ENT_UR_USER_ADD")?(i(),f(F,{key:0,type:"primary",onClick:g},{default:t(()=>n[6]||(n[6]=[d(" \u65B0\u5EFA ")])),_:1})):m("",!0)]),bodyCell:t(({column:y,record:o})=>[y.key==="avatar"?(i(),f(h,{key:0,size:"default",src:o.avatarUrl},null,8,["src"])):m("",!0),y.key==="state"?(i(),f(C,{key:1,state:o.state,showSwitchType:A(b)("ENT_UR_USER_EDIT"),onChange:T=>_(o.sysUserId,T)},null,8,["state","showSwitchType","onChange"])):m("",!0),y.key==="op"?(i(),f(L,{key:2},{default:t(()=>[A(b)("ENT_UR_USER_UPD_ROLE")?(i(),f(F,{key:0,type:"link",onClick:T=>s(o.sysUserId)},{default:t(()=>n[7]||(n[7]=[d(" \u53D8\u66F4\u89D2\u8272 ")])),_:2},1032,["onClick"])):m("",!0),A(b)("ENT_UR_USER_EDIT")?(i(),f(F,{key:1,type:"link",onClick:T=>p(o.sysUserId)},{default:t(()=>n[8]||(n[8]=[d(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])):m("",!0),A(b)("ENT_UR_USER_DELETE")?(i(),f(F,{key:2,type:"link",danger:"",onClick:T=>k(o.sysUserId)},{default:t(()=>n[9]||(n[9]=[d(" \u5220\u9664 ")])),_:2},1032,["onClick"])):m("",!0)]),_:2},1024)):m("",!0)]),_:1},8,["tableColumns","searchData"])]),_:1}),u(we,{ref_key:"infoAddOrEdit",ref:e,callbackFunc:l},null,512),u(ve,{ref_key:"roleDistRef",ref:D},null,512)]),_:1})}}});export{Ce as default};
