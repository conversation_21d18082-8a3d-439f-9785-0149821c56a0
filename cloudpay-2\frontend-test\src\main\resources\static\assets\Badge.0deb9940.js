import{d as W,H as f,r as at,h as R,P as J,bm as rt,M as i,b as v,J as F,u as M,bL as lt,a3 as K,V as z,a5 as it,a6 as st,am as O,bu as U,a7 as Y,bv as q,e as I,bs as ut,ab as ct,bp as dt,bn as bt,aG as mt,aH as gt}from"./index.8746381c.js";import{i as ft}from"./index.9b74c380.js";function G(t){let{prefixCls:o,value:a,current:e,offset:n=0}=t,c;return n&&(c={position:"absolute",top:`${n}00%`,left:0}),v("p",{style:c,class:F(`${o}-only-unit`,{current:e})},[a])}function vt(t,o,a){let e=t,n=0;for(;(e+10)%10!==o;)e+=a,n+=a;return n}var pt=W({compatConfig:{MODE:3},name:"SingleNumber",props:{prefixCls:String,value:String,count:Number},setup(t){const o=f(()=>Number(t.value)),a=f(()=>Math.abs(t.count)),e=at({prevValue:o.value,prevCount:a.value}),n=()=>{e.prevValue=o.value,e.prevCount=a.value},c=R();return J(o,()=>{clearTimeout(c.value),c.value=setTimeout(()=>{n()},1e3)},{flush:"post"}),rt(()=>{clearTimeout(c.value)}),()=>{let d,p={};const s=o.value;if(e.prevValue===s||Number.isNaN(s)||Number.isNaN(e.prevValue))d=[G(i(i({},t),{current:!0}))],p={transition:"none"};else{d=[];const h=s+10,b=[];for(let r=s;r<=h;r+=1)b.push(r);const l=b.findIndex(r=>r%10===e.prevValue);d=b.map((r,y)=>{const $=r%10;return G(i(i({},t),{value:$,offset:y-l,current:y===l}))});const u=e.prevCount<a.value?1:-1;p={transform:`translateY(${-vt(e.prevValue,s,u)}00%)`}}return v("span",{class:`${t.prefixCls}-only`,style:p,onTransitionend:()=>n()},[d])}}}),ht=globalThis&&globalThis.__rest||function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(a[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,e=Object.getOwnPropertySymbols(t);n<e.length;n++)o.indexOf(e[n])<0&&Object.prototype.propertyIsEnumerable.call(t,e[n])&&(a[e[n]]=t[e[n]]);return a};const $t={prefixCls:String,count:z.any,component:String,title:z.any,show:Boolean};var St=W({compatConfig:{MODE:3},name:"ScrollNumber",inheritAttrs:!1,props:$t,setup(t,o){let{attrs:a,slots:e}=o;const{prefixCls:n}=M("scroll-number",t);return()=>{var c;const d=i(i({},t),a),{prefixCls:p,count:s,title:h,show:b,component:l="sup",class:u,style:r}=d,y=ht(d,["prefixCls","count","title","show","component","class","style"]),$=i(i({},y),{style:r,"data-show":t.show,class:F(n.value,u),title:h});let m=s;if(s&&Number(s)%1===0){const g=String(s).split("");m=g.map((P,T)=>v(pt,{prefixCls:n.value,count:Number(s),value:P,key:g.length-T},null))}r&&r.borderColor&&($.style=i(i({},r),{boxShadow:`0 0 0 1px ${r.borderColor} inset`}));const S=lt((c=e.default)===null||c===void 0?void 0:c.call(e));return S&&S.length?K(S,{class:F(`${n.value}-custom-component`)},!1):v(l,$,{default:()=>[m]})}}});const yt=new O("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),Ct=new O("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),xt=new O("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),wt=new O("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),Nt=new O("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),Ot=new O("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),Pt=t=>{const{componentCls:o,iconCls:a,antCls:e,badgeFontHeight:n,badgeShadowSize:c,badgeHeightSm:d,motionDurationSlow:p,badgeStatusSize:s,marginXS:h,badgeRibbonOffset:b}=t,l=`${e}-scroll-number`,u=`${e}-ribbon`,r=`${e}-ribbon-wrapper`,y=U(t,(m,S)=>{let{darkColor:g}=S;return{[`&${o} ${o}-color-${m}`]:{background:g,[`&:not(${o}-count)`]:{color:g}}}}),$=U(t,(m,S)=>{let{darkColor:g}=S;return{[`&${u}-color-${m}`]:{background:g,color:g}}});return{[o]:i(i(i(i({},Y(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${o}-count`]:{zIndex:t.badgeZIndex,minWidth:t.badgeHeight,height:t.badgeHeight,color:t.badgeTextColor,fontWeight:t.badgeFontWeight,fontSize:t.badgeFontSize,lineHeight:`${t.badgeHeight}px`,whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:t.badgeHeight/2,boxShadow:`0 0 0 ${c}px ${t.badgeShadowColor}`,transition:`background ${t.motionDurationMid}`,a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},[`${o}-count-sm`]:{minWidth:d,height:d,fontSize:t.badgeFontSizeSm,lineHeight:`${d}px`,borderRadius:d/2},[`${o}-multiple-words`]:{padding:`0 ${t.paddingXS}px`},[`${o}-dot`]:{zIndex:t.badgeZIndex,width:t.badgeDotSize,minWidth:t.badgeDotSize,height:t.badgeDotSize,background:t.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${c}px ${t.badgeShadowColor}`},[`${o}-dot${l}`]:{transition:`background ${p}`},[`${o}-count, ${o}-dot, ${l}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${a}-spin`]:{animationName:Ot,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${o}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${o}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:s,height:s,verticalAlign:"middle",borderRadius:"50%"},[`${o}-status-success`]:{backgroundColor:t.colorSuccess},[`${o}-status-processing`]:{overflow:"visible",color:t.colorPrimary,backgroundColor:t.colorPrimary,"&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:c,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:yt,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${o}-status-default`]:{backgroundColor:t.colorTextPlaceholder},[`${o}-status-error`]:{backgroundColor:t.colorError},[`${o}-status-warning`]:{backgroundColor:t.colorWarning},[`${o}-status-text`]:{marginInlineStart:h,color:t.colorText,fontSize:t.fontSize}}}),y),{[`${o}-zoom-appear, ${o}-zoom-enter`]:{animationName:Ct,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`${o}-zoom-leave`]:{animationName:xt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`&${o}-not-a-wrapper`]:{[`${o}-zoom-appear, ${o}-zoom-enter`]:{animationName:wt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`${o}-zoom-leave`]:{animationName:Nt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`&:not(${o}-status)`]:{verticalAlign:"middle"},[`${l}-custom-component, ${o}-count`]:{transform:"none"},[`${l}-custom-component, ${l}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[`${l}`]:{overflow:"hidden",[`${l}-only`]:{position:"relative",display:"inline-block",height:t.badgeHeight,transition:`all ${t.motionDurationSlow} ${t.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${l}-only-unit`]:{height:t.badgeHeight,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${l}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${o}-count, ${o}-dot, ${l}-custom-component`]:{transform:"translate(-50%, -50%)"}}}),[`${r}`]:{position:"relative"},[`${u}`]:i(i(i(i({},Y(t)),{position:"absolute",top:h,padding:`0 ${t.paddingXS}px`,color:t.colorPrimary,lineHeight:`${n}px`,whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,[`${u}-text`]:{color:t.colorTextLightSolid},[`${u}-corner`]:{position:"absolute",top:"100%",width:b,height:b,color:"currentcolor",border:`${b/2}px solid`,transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),$),{[`&${u}-placement-end`]:{insetInlineEnd:-b,borderEndEndRadius:0,[`${u}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${u}-placement-start`]:{insetInlineStart:-b,borderEndStartRadius:0,[`${u}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}};var Q=it("Badge",t=>{const{fontSize:o,lineHeight:a,fontSizeSM:e,lineWidth:n,marginXS:c,colorBorderBg:d}=t,p=Math.round(o*a),s=n,h="auto",b=p-2*s,l=t.colorBgContainer,u="normal",r=e,y=t.colorError,$=t.colorErrorHover,m=o,S=e/2,g=e,P=e/2,T=st(t,{badgeFontHeight:p,badgeShadowSize:s,badgeZIndex:h,badgeHeight:b,badgeTextColor:l,badgeFontWeight:u,badgeFontSize:r,badgeColor:y,badgeColorHover:$,badgeShadowColor:d,badgeHeightSm:m,badgeDotSize:S,badgeFontSizeSm:g,badgeStatusSize:P,badgeProcessingDuration:"1.2s",badgeRibbonOffset:c,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"});return[Pt(T)]}),Tt=globalThis&&globalThis.__rest||function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(a[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,e=Object.getOwnPropertySymbols(t);n<e.length;n++)o.indexOf(e[n])<0&&Object.prototype.propertyIsEnumerable.call(t,e[n])&&(a[e[n]]=t[e[n]]);return a};const Bt=()=>({prefix:String,color:{type:String},text:z.any,placement:{type:String,default:"end"}});var It=W({compatConfig:{MODE:3},name:"ABadgeRibbon",inheritAttrs:!1,props:Bt(),slots:Object,setup(t,o){let{attrs:a,slots:e}=o;const{prefixCls:n,direction:c}=M("ribbon",t),[d,p]=Q(n),s=f(()=>q(t.color,!1)),h=f(()=>[n.value,`${n.value}-placement-${t.placement}`,{[`${n.value}-rtl`]:c.value==="rtl",[`${n.value}-color-${t.color}`]:s.value}]);return()=>{var b,l;const{class:u,style:r}=a,y=Tt(a,["class","style"]),$={},m={};return t.color&&!s.value&&($.background=t.color,m.color=t.color),d(v("div",I({class:`${n.value}-wrapper ${p.value}`},y),[(b=e.default)===null||b===void 0?void 0:b.call(e),v("div",{class:[h.value,u,p.value],style:i(i({},$),r)},[v("span",{class:`${n.value}-text`},[t.text||((l=e.text)===null||l===void 0?void 0:l.call(e))]),v("div",{class:`${n.value}-corner`,style:m},null)])]))}}});const zt=()=>({count:z.any.def(null),showZero:{type:Boolean,default:void 0},overflowCount:{type:Number,default:99},dot:{type:Boolean,default:void 0},prefixCls:String,scrollNumberPrefixCls:String,status:{type:String},size:{type:String,default:"default"},color:String,text:z.any,offset:Array,numberStyle:{type:Object,default:void 0},title:String});var Ht=W({compatConfig:{MODE:3},name:"ABadge",Ribbon:It,inheritAttrs:!1,props:zt(),slots:Object,setup(t,o){let{slots:a,attrs:e}=o;const{prefixCls:n,direction:c}=M("badge",t),[d,p]=Q(n),s=f(()=>t.count>t.overflowCount?`${t.overflowCount}+`:t.count),h=f(()=>s.value==="0"||s.value===0),b=f(()=>t.count===null||h.value&&!t.showZero),l=f(()=>(t.status!==null&&t.status!==void 0||t.color!==null&&t.color!==void 0)&&b.value),u=f(()=>t.dot&&!h.value),r=f(()=>u.value?"":s.value),y=f(()=>(r.value===null||r.value===void 0||r.value===""||h.value&&!t.showZero)&&!u.value),$=R(t.count),m=R(r.value),S=R(u.value);J([()=>t.count,r,u],()=>{y.value||($.value=t.count,m.value=r.value,S.value=u.value)},{immediate:!0});const g=f(()=>q(t.color,!1)),P=f(()=>({[`${n.value}-status-dot`]:l.value,[`${n.value}-status-${t.status}`]:!!t.status,[`${n.value}-color-${t.color}`]:g.value})),T=f(()=>t.color&&!g.value?{background:t.color,color:t.color}:{}),k=f(()=>({[`${n.value}-dot`]:S.value,[`${n.value}-count`]:!S.value,[`${n.value}-count-sm`]:t.size==="small",[`${n.value}-multiple-words`]:!S.value&&m.value&&m.value.toString().length>1,[`${n.value}-status-${t.status}`]:!!t.status,[`${n.value}-color-${t.color}`]:g.value}));return()=>{var E,_;const{offset:N,title:Z,color:V}=t,L=e.style,j=ut(a,t,"text"),x=n.value,C=$.value;let w=ct((E=a.default)===null||E===void 0?void 0:E.call(a));w=w.length?w:null;const A=!!(!y.value||a.count),D=(()=>{if(!N)return i({},L);const B={marginTop:ft(N[1])?`${N[1]}px`:N[1]};return c.value==="rtl"?B.left=`${parseInt(N[0],10)}px`:B.right=`${-parseInt(N[0],10)}px`,i(i({},B),L)})(),tt=Z!=null?Z:typeof C=="string"||typeof C=="number"?C:void 0,et=A||!j?null:v("span",{class:`${x}-status-text`},[j]),ot=typeof C=="object"||C===void 0&&a.count?K(C!=null?C:(_=a.count)===null||_===void 0?void 0:_.call(a),{style:D},!1):null,X=F(x,{[`${x}-status`]:l.value,[`${x}-not-a-wrapper`]:!w,[`${x}-rtl`]:c.value==="rtl"},e.class,p.value);if(!w&&l.value){const B=D.color;return d(v("span",I(I({},e),{},{class:X,style:D}),[v("span",{class:P.value,style:T.value},null),v("span",{style:{color:B},class:`${x}-status-text`},[j])]))}const nt=dt(w?`${x}-zoom`:"",{appear:!1});let H=i(i({},D),t.numberStyle);return V&&!g.value&&(H=H||{},H.background=V),d(v("span",I(I({},e),{},{class:X}),[w,v(bt,nt,{default:()=>[mt(v(St,{prefixCls:t.scrollNumberPrefixCls,show:A,class:k.value,count:m.value,title:tt,style:H,key:"scrollNumber"},{default:()=>[ot]}),[[gt,A]])]}),et]))}}});export{Ht as B,It as R};
