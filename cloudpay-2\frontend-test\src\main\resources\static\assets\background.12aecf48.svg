<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1920" height="970" viewBox="0 0 1920 970"><defs><style>.a{fill:url(#a);}.b{clip-path:url(#b);}.c{filter:url(#n);}.d{filter:url(#k);}.e{filter:url(#h);}.f{filter:url(#e);}</style><linearGradient id="a" y1="0.383" x2="1" y2="0.589" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#f1f8ff"/><stop offset="1" stop-color="#f7fbfe"/></linearGradient><clipPath id="b"><rect class="a" width="1920" height="970"/></clipPath><filter id="e" x="-169" y="375" width="756" height="756" filterUnits="userSpaceOnUse"><feOffset dx="20" dy="-20" input="SourceAlpha"/><feGaussianBlur stdDeviation="30" result="f"/><feFlood flood-color="#211f47" flood-opacity="0.071"/><feComposite operator="in" in2="f"/><feComposite in="SourceGraphic"/></filter><filter id="h" x="1230" y="568" width="433" height="433" filterUnits="userSpaceOnUse"><feOffset dx="2" dy="-10" input="SourceAlpha"/><feGaussianBlur stdDeviation="30" result="i"/><feFlood flood-color="#211f47" flood-opacity="0.071"/><feComposite operator="in" in2="i"/><feComposite in="SourceGraphic"/></filter><filter id="k" x="1440" y="88.44" width="655.121" height="655.121" filterUnits="userSpaceOnUse"><feOffset dx="2" dy="30" input="SourceAlpha"/><feGaussianBlur stdDeviation="30" result="l"/><feFlood flood-color="#211f47" flood-opacity="0.122"/><feComposite operator="in" in2="l"/><feComposite in="SourceGraphic"/></filter><filter id="n" x="422.061" y="2.561" width="350.879" height="350.879" filterUnits="userSpaceOnUse"><feOffset dx="2" dy="10" input="SourceAlpha"/><feGaussianBlur stdDeviation="30" result="o"/><feFlood flood-color="#211f47" flood-opacity="0.122"/><feComposite operator="in" in2="o"/><feComposite in="SourceGraphic"/></filter></defs><g class="b"><rect class="a" width="1920" height="970"/><g class="f" transform="matrix(1, 0, 0, 1, 0, 0)"><circle class="a" cx="288" cy="288" r="288" transform="translate(-99 485)"/></g><g class="e" transform="matrix(1, 0, 0, 1, 0, 0)"><circle class="a" cx="126.5" cy="126.5" r="126.5" transform="translate(1318 668)"/></g><g class="d" transform="matrix(1, 0, 0, 1, 0, 0)"><rect class="a" width="335.961" height="335.961" rx="30" transform="translate(1528 386) rotate(-45)"/></g><g class="c" transform="matrix(1, 0, 0, 1, 0, 0)"><rect class="a" width="170.879" height="170.879" transform="translate(510.06 82.56)"/></g></g></svg>